package com.lenskart.example.test;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.example.dataprovider.AnythingEndpointDP;
import com.lenskart.example.helper.GetDataHelper;
import com.lenskart.example.model.DiscoveryTestContext;
import org.testng.annotations.Test;

/**
 * Example test class demonstrating the use of TestCategory annotation
 * at both class and method levels.
 */
@TestCategory(TestCategory.Category.REGRESSION) // Default category for all methods in this class
public class CategoryExampleTest {

    @Test(dataProviderClass = AnythingEndpointDP.class, dataProvider = "anythingValueProvider")
    public void regressionTest(String userId) {
        // This test inherits the REGRESSION category from the class
        GetDataHelper.builder()
                .testContext(DiscoveryTestContext.builder()
                        .userId(userId)
                        .build())
                .build()
                .test();
    }
    
    @Test(dataProviderClass = AnythingEndpointDP.class, dataProvider = "anythingValueProvider")
    @TestCategory(TestCategory.Category.SANITY) // Override the class-level category
    public void sanityTest(String userId) {
        // This test is explicitly marked as SANITY
        GetDataHelper.builder()
                .testContext(DiscoveryTestContext.builder()
                        .userId(userId)
                        .build())
                .build()
                .test();
    }
    
    @Test(dataProviderClass = AnythingEndpointDP.class, dataProvider = "anythingValueProvider")
    @TestCategory(TestCategory.Category.E2E) // Override the class-level category
    public void e2eTest(String userId) {
        // This test is explicitly marked as E2E
        GetDataHelper.builder()
                .testContext(DiscoveryTestContext.builder()
                        .userId(userId)
                        .build())
                .build()
                .test();
    }
}

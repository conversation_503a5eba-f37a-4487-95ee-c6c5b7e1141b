package com.lenskart.example.test;

import com.lenskart.example.dataprovider.AnythingEndpointDP;
import com.lenskart.example.helper.GetDataHelper;
import com.lenskart.example.model.DiscoveryTestContext;
import org.testng.annotations.Test;

public class GetDiscoveryTest {


    @Test(dataProviderClass = AnythingEndpointDP.class, dataProvider = "anythingValueProvider")
    public void getDiscoveryTest(String userId) {
        GetDataHelper.builder()
                .testContext(DiscoveryTestContext.builder()
                        .userId(userId)
                        .build())
                .build()
                .test();
    }
}
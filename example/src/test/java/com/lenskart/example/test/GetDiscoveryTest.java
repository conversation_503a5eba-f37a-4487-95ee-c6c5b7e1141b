package com.lenskart.example.test;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.example.dataprovider.AnythingEndpointDP;
import com.lenskart.example.helper.GetDataHelper;
import com.lenskart.example.model.DiscoveryTestContext;
import org.testng.annotations.Test;

public class GetDiscoveryTest {


    @TestCategory(TestCategory.Category.REGRESSION)
    @Test(dataProviderClass = AnythingEndpointDP.class, dataProvider = "anythingValueProvider")
    public void getDiscoveryTest(String userId) {
        GetDataHelper.builder()
                .testContext(DiscoveryTestContext.builder()
                        .userId(userId)
                        .build())
                .build()
                .test();
    }
}
package com.lenskart.example.validator;

import com.lenskart.commons.base.IValidator;
import com.lenskart.example.model.DiscoveryTestContext;
import com.lenskart.example.model.GetRequest;
import com.lenskart.example.model.GetResponse;
import lombok.Builder;
import org.testng.asserts.SoftAssert;

@Builder
public class GetDataValidator implements IValidator {

    DiscoveryTestContext testContext;
    GetRequest request;
    GetResponse response;
    static SoftAssert softAssert;

    static {
        softAssert = new SoftAssert();
    }


    @Override
    public void validateNode() {
        softAssert = new SoftAssert();
        softAssert.assertNotNull(response, "Response should not be null");

        // Validate specific fields in the response
        if (response != null) {
            // Verify that the request data was correctly processed
            softAssert.assertEquals(response.getMethod(), "POST", "HTTP method should be POST");

            // Verify that the URL contains the expected path
            softAssert.assertTrue(response.getUrl().contains("/anything/123"), "URL should contain the expected path");

            // Verify that the JSON data contains our request data
            if (response.getJson() != null) {
                softAssert.assertTrue(response.getJson().containsKey("expand"), "JSON should contain 'expand' field");
                softAssert.assertTrue(response.getJson().containsKey("amount"), "JSON should contain 'amount' field");
            }
        }

        softAssert.assertAll();
    }

    @Override
    public void validateDBEntities() {

    }
}
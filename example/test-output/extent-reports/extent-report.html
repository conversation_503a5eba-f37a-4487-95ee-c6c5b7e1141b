

<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<title>API Automation Test Report</title>
<link rel="apple-touch-icon" href="https://cdn.jsdelivr.net/gh/extent-framework/extent-github-cdn@b00a2d0486596e73dd7326beacf352c639623a0e/commons/img/logo.png">
<link rel="shortcut icon" href="https://cdn.jsdelivr.net/gh/extent-framework/extent-github-cdn@b00a2d0486596e73dd7326beacf352c639623a0e/commons/img/logo.png">
<link href="https://cdn.jsdelivr.net/gh/extent-framework/extent-github-cdn@ce8b10435bcbae260c334c0d0c6b61d2c19b6168/spark/css/spark-style.css" rel="stylesheet" />
<link href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/gh/extent-framework/extent-github-cdn@7cc78ce/spark/js/jsontree.js"></script>
<style type="text/css"></style></head><body class="spa -report dark">
  <div class="app">
    <div class="layout">
<div class="header navbar">
<div class="vheader">
<div class="nav-logo">
<a href="#">
<div class="logo" style="background-image: url('https://cdn.jsdelivr.net/gh/extent-framework/extent-github-cdn@b00a2d0486596e73dd7326beacf352c639623a0e/commons/img/logo.png')"></div>
</a>
</div>
<ul class="nav-left">
<li class="search-box">
<a class="search-toggle" href="#">
<i class="search-icon fa fa-search"></i>
<i class="search-icon-close fa fa-close"></i>
</a>
</li>
<li class="search-input"><input id="search-tests" class="form-control" type="text" placeholder="Search..."></li>
</ul>
<ul class="nav-right">
<li class="m-r-10">
<a href="#"><span class="badge badge-primary">Automation Report</span></a>
</li>
<li class="m-r-10">
<a href="#"><span class="badge badge-primary">2025-07-03 20:19:34</span></a>
</li>
</ul>
</div>
</div><div class="side-nav">
<div class="side-nav-inner">
<ul class="side-nav-menu">
<li class="nav-item dropdown" onclick="toggleView('test-view')">
<a id="nav-test" class="dropdown-toggle" href="#">
<span class="ico"><i class="fa fa-list"></i></span>
</a>
</li>
<li class="nav-item dropdown" onclick="toggleView('exception-view')">
<a id="nav-exception" class="dropdown-toggle" href="#">
<span class="ico"><i class="fa fa-bug"></i></span>
</a>
</li>
<li class="nav-item dropdown" onclick="toggleView('category-view')">
<a id="nav-category" class="dropdown-toggle" href="#">
<span class="ico"><i class="fa fa-tag"></i></span>
</a>
</li>
<li class="nav-item dropdown" onclick="toggleView('dashboard-view')">
<a id="nav-dashboard" class="dropdown-toggle" href="#">
<span class="ico"><i class="fa fa-bar-chart"></i></span>
</a>
</li>
</ul>
</div>
</div>      <div class="vcontainer">
        <div class="main-content">
<div class="test-wrapper row view test-view">
  <div class="test-list">
    <div class="test-list-tools">
<ul class="tools pull-left">
<li><a href="#"><span class="font-size-14">Tests</span></a></li>
</ul>
<ul class="tools text-right">
<li class="dropdown">
<a href="#" class="dropdown-toggle" data-toggle="dropdown"><i class="fa fa-exclamation-circle"></i></a>
<ul id="status-toggle" class="dropdown-menu dropdown-md p-v-0">
<a class="dropdown-item" status="pass" href="#"><span>Pass</span><span class="status success"></span></a>
<a class="dropdown-item" status="fail" href="#"><span>Fail</span><span class="status danger"></span></a>
<div class="dropdown-divider"></div>
<a status="clear" class="dropdown-item" href="#"><span>Clear</span><span class="pull-right"><i class="fa fa-close"></i></span></a>
</ul>
</li>
<li class="dropdown">
<a href="#" class="dropdown-toggle" data-toggle="dropdown"><i class="fa fa-tag"></i></a>
<ul id="tag-toggle" class="dropdown-menu dropdown-md p-v-0">
<a class="dropdown-item" href="#">com.lenskart.example.test.CategoryExampleTest</a><a class="dropdown-item" href="#">com.lenskart.example.test.GetDiscoveryTest</a>
</ul>
</li>
</ul>
</div>    <div class="test-list-wrapper scrollable">
      <ul class="test-list-item">
        <li class="test-item"  status="pass" test-id="1"
          author=""
          tag="com.lenskart.example.test.GetDiscoveryTest"
          device="">
          <div class="test-detail">
            <p class="name">getDiscoveryTest</p>
            <p class="text-sm">
              <span>8:19:34 pm</span> / <span>00:00:04:580</span>
              <span class="badge pass-bg log float-right">Pass</span>
            </p>
          </div>
          <div class="test-contents d-none">
<div class="detail-head">
<div class="p-v-10">
<div class="info">
<h5 class="test-status text-pass">getDiscoveryTest</h5>
<span class='badge badge-success'>07.03.2025 8:19:34 pm</span>
<span class='badge badge-danger'>07.03.2025 8:19:39 pm</span>
<span class='badge badge-default'>00:00:04:580</span>
&middot; <span class='uri-anchor badge badge-default'>#test-id=1</span>
<span title='Skip to the next failed step' class='badge badge-danger pointer float-right ne ml-1'><i class="fa fa-fast-forward"></i></span>
<span title='Collapse all nodes' class='badge badge-default pointer float-right ct ml-1'><i class="fa fa-compress"></i></span>
<span title='Expand all nodes' class='badge badge-default pointer float-right et'><i class="fa fa-expand"></i></span>
</div>
<div class="m-t-15"><span class="badge badge-pill badge-default">com.lenskart.example.test.GetDiscoveryTest</span></div>
<div class="m-t-10 m-l-5">getDiscoveryTest</div>
</div>
</div><div class="detail-body mt-4">
<table class="table table-sm">
  <thead><tr><th class="status-col">Status</th><th class="timestamp-col">Timestamp</th><th class="details-col">Details</th></tr></thead>
  <tbody>
      <tr class="event-row">
        <td><span class="badge log pass-bg">Pass</span></td>
        <td>8:19:39 pm</td>
        <td>
          <span class='badge white-text green'>Test Passed</span>
        </td>
      </tr>
  </tbody>
</table>
</div>
          </div>
        </li>
        <li class="test-item"  status="pass" test-id="2"
          author=""
          tag="com.lenskart.example.test.GetDiscoveryTest"
          device="">
          <div class="test-detail">
            <p class="name">getDiscoveryTest</p>
            <p class="text-sm">
              <span>8:19:39 pm</span> / <span>00:00:01:272</span>
              <span class="badge pass-bg log float-right">Pass</span>
            </p>
          </div>
          <div class="test-contents d-none">
<div class="detail-head">
<div class="p-v-10">
<div class="info">
<h5 class="test-status text-pass">getDiscoveryTest</h5>
<span class='badge badge-success'>07.03.2025 8:19:39 pm</span>
<span class='badge badge-danger'>07.03.2025 8:19:40 pm</span>
<span class='badge badge-default'>00:00:01:272</span>
&middot; <span class='uri-anchor badge badge-default'>#test-id=2</span>
<span title='Skip to the next failed step' class='badge badge-danger pointer float-right ne ml-1'><i class="fa fa-fast-forward"></i></span>
<span title='Collapse all nodes' class='badge badge-default pointer float-right ct ml-1'><i class="fa fa-compress"></i></span>
<span title='Expand all nodes' class='badge badge-default pointer float-right et'><i class="fa fa-expand"></i></span>
</div>
<div class="m-t-15"><span class="badge badge-pill badge-default">com.lenskart.example.test.GetDiscoveryTest</span></div>
<div class="m-t-10 m-l-5">getDiscoveryTest</div>
</div>
</div><div class="detail-body mt-4">
<table class="table table-sm">
  <thead><tr><th class="status-col">Status</th><th class="timestamp-col">Timestamp</th><th class="details-col">Details</th></tr></thead>
  <tbody>
      <tr class="event-row">
        <td><span class="badge log pass-bg">Pass</span></td>
        <td>8:19:40 pm</td>
        <td>
          <span class='badge white-text green'>Test Passed</span>
        </td>
      </tr>
  </tbody>
</table>
</div>
          </div>
        </li>
        <li class="test-item"  status="pass" test-id="3"
          author=""
          tag="com.lenskart.example.test.GetDiscoveryTest"
          device="">
          <div class="test-detail">
            <p class="name">getDiscoveryTest</p>
            <p class="text-sm">
              <span>8:19:40 pm</span> / <span>00:00:02:809</span>
              <span class="badge pass-bg log float-right">Pass</span>
            </p>
          </div>
          <div class="test-contents d-none">
<div class="detail-head">
<div class="p-v-10">
<div class="info">
<h5 class="test-status text-pass">getDiscoveryTest</h5>
<span class='badge badge-success'>07.03.2025 8:19:40 pm</span>
<span class='badge badge-danger'>07.03.2025 8:19:43 pm</span>
<span class='badge badge-default'>00:00:02:809</span>
&middot; <span class='uri-anchor badge badge-default'>#test-id=3</span>
<span title='Skip to the next failed step' class='badge badge-danger pointer float-right ne ml-1'><i class="fa fa-fast-forward"></i></span>
<span title='Collapse all nodes' class='badge badge-default pointer float-right ct ml-1'><i class="fa fa-compress"></i></span>
<span title='Expand all nodes' class='badge badge-default pointer float-right et'><i class="fa fa-expand"></i></span>
</div>
<div class="m-t-15"><span class="badge badge-pill badge-default">com.lenskart.example.test.GetDiscoveryTest</span></div>
<div class="m-t-10 m-l-5">getDiscoveryTest</div>
</div>
</div><div class="detail-body mt-4">
<table class="table table-sm">
  <thead><tr><th class="status-col">Status</th><th class="timestamp-col">Timestamp</th><th class="details-col">Details</th></tr></thead>
  <tbody>
      <tr class="event-row">
        <td><span class="badge log pass-bg">Pass</span></td>
        <td>8:19:43 pm</td>
        <td>
          <span class='badge white-text green'>Test Passed</span>
        </td>
      </tr>
  </tbody>
</table>
</div>
          </div>
        </li>
        <li class="test-item"  status="pass" test-id="4"
          author=""
          tag="com.lenskart.example.test.CategoryExampleTest"
          device="">
          <div class="test-detail">
            <p class="name">e2eTest</p>
            <p class="text-sm">
              <span>8:19:43 pm</span> / <span>00:00:01:099</span>
              <span class="badge pass-bg log float-right">Pass</span>
            </p>
          </div>
          <div class="test-contents d-none">
<div class="detail-head">
<div class="p-v-10">
<div class="info">
<h5 class="test-status text-pass">e2eTest</h5>
<span class='badge badge-success'>07.03.2025 8:19:43 pm</span>
<span class='badge badge-danger'>07.03.2025 8:19:44 pm</span>
<span class='badge badge-default'>00:00:01:099</span>
&middot; <span class='uri-anchor badge badge-default'>#test-id=4</span>
<span title='Skip to the next failed step' class='badge badge-danger pointer float-right ne ml-1'><i class="fa fa-fast-forward"></i></span>
<span title='Collapse all nodes' class='badge badge-default pointer float-right ct ml-1'><i class="fa fa-compress"></i></span>
<span title='Expand all nodes' class='badge badge-default pointer float-right et'><i class="fa fa-expand"></i></span>
</div>
<div class="m-t-15"><span class="badge badge-pill badge-default">com.lenskart.example.test.CategoryExampleTest</span></div>
<div class="m-t-10 m-l-5">e2eTest</div>
</div>
</div><div class="detail-body mt-4">
<table class="table table-sm">
  <thead><tr><th class="status-col">Status</th><th class="timestamp-col">Timestamp</th><th class="details-col">Details</th></tr></thead>
  <tbody>
      <tr class="event-row">
        <td><span class="badge log pass-bg">Pass</span></td>
        <td>8:19:44 pm</td>
        <td>
          <span class='badge white-text green'>Test Passed</span>
        </td>
      </tr>
  </tbody>
</table>
</div>
          </div>
        </li>
        <li class="test-item"  status="fail" test-id="5"
          author=""
          tag="com.lenskart.example.test.CategoryExampleTest"
          device="">
          <div class="test-detail">
            <p class="name">e2eTest</p>
            <p class="text-sm">
              <span>8:19:44 pm</span> / <span>00:00:02:991</span>
              <span class="badge fail-bg log float-right">Fail</span>
            </p>
          </div>
          <div class="test-contents d-none">
<div class="detail-head">
<div class="p-v-10">
<div class="info">
<h5 class="test-status text-fail">e2eTest</h5>
<span class='badge badge-success'>07.03.2025 8:19:44 pm</span>
<span class='badge badge-danger'>07.03.2025 8:19:47 pm</span>
<span class='badge badge-default'>00:00:02:991</span>
&middot; <span class='uri-anchor badge badge-default'>#test-id=5</span>
<span title='Skip to the next failed step' class='badge badge-danger pointer float-right ne ml-1'><i class="fa fa-fast-forward"></i></span>
<span title='Collapse all nodes' class='badge badge-default pointer float-right ct ml-1'><i class="fa fa-compress"></i></span>
<span title='Expand all nodes' class='badge badge-default pointer float-right et'><i class="fa fa-expand"></i></span>
</div>
<div class="m-t-15"><span class="badge badge-pill badge-default">com.lenskart.example.test.CategoryExampleTest</span></div>
<div class="m-t-10 m-l-5">e2eTest</div>
</div>
</div><div class="detail-body mt-4">
<table class="table table-sm">
  <thead><tr><th class="status-col">Status</th><th class="timestamp-col">Timestamp</th><th class="details-col">Details</th></tr></thead>
  <tbody>
      <tr class="event-row">
        <td><span class="badge log fail-bg">Fail</span></td>
        <td>8:19:47 pm</td>
        <td>
          <span class='badge white-text red'>Test Failed</span>
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log fail-bg">Fail</span></td>
        <td>8:19:47 pm</td>
        <td>
          <textarea readonly class="code-block">java.lang.AssertionError: Failed to parse JSON string to object: Unexpected character ('<' (code 60)): expected a valid value (JSON String, Number, Array, Object or token 'null', 'true' or 'false')
 at [Source: (String)"<html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
</body>
</html>
"; line: 1, column: 2]
	at org.testng.Assert.fail(Assert.java:111)
	at com.lenskart.commons.utils.JsonUtils.parseJsonString(JsonUtils.java:18)
	at com.lenskart.commons.base.BaseHelper.parseResponse(BaseHelper.java:25)
	at com.lenskart.example.helper.GetDataHelper.process(GetDataHelper.java:37)
	at com.lenskart.example.helper.GetDataHelper.test(GetDataHelper.java:56)
	at com.lenskart.example.test.CategoryExampleTest.e2eTest(CategoryExampleTest.java:47)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)
	at org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:664)
	at org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:227)
	at org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:50)
	at org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:957)
	at org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:200)
	at org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148)
	at org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.testng.TestRunner.privateRun(TestRunner.java:848)
	at org.testng.TestRunner.run(TestRunner.java:621)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:443)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:437)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:397)
	at org.testng.SuiteRunner.run(SuiteRunner.java:336)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1280)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1200)
	at org.testng.TestNG.runSuites(TestNG.java:1114)
	at org.testng.TestNG.run(TestNG.java:1082)
	at com.intellij.rt.testng.IDEARemoteTestNG.run(IDEARemoteTestNG.java:65)
	at com.intellij.rt.testng.RemoteTestNGStarter.main(RemoteTestNGStarter.java:105)
</textarea>
          
        </td>
      </tr>
  </tbody>
</table>
</div>
          </div>
        </li>
        <li class="test-item"  status="pass" test-id="6"
          author=""
          tag="com.lenskart.example.test.CategoryExampleTest"
          device="">
          <div class="test-detail">
            <p class="name">e2eTest</p>
            <p class="text-sm">
              <span>8:19:47 pm</span> / <span>00:00:01:469</span>
              <span class="badge pass-bg log float-right">Pass</span>
            </p>
          </div>
          <div class="test-contents d-none">
<div class="detail-head">
<div class="p-v-10">
<div class="info">
<h5 class="test-status text-pass">e2eTest</h5>
<span class='badge badge-success'>07.03.2025 8:19:47 pm</span>
<span class='badge badge-danger'>07.03.2025 8:19:48 pm</span>
<span class='badge badge-default'>00:00:01:469</span>
&middot; <span class='uri-anchor badge badge-default'>#test-id=6</span>
<span title='Skip to the next failed step' class='badge badge-danger pointer float-right ne ml-1'><i class="fa fa-fast-forward"></i></span>
<span title='Collapse all nodes' class='badge badge-default pointer float-right ct ml-1'><i class="fa fa-compress"></i></span>
<span title='Expand all nodes' class='badge badge-default pointer float-right et'><i class="fa fa-expand"></i></span>
</div>
<div class="m-t-15"><span class="badge badge-pill badge-default">com.lenskart.example.test.CategoryExampleTest</span></div>
<div class="m-t-10 m-l-5">e2eTest</div>
</div>
</div><div class="detail-body mt-4">
<table class="table table-sm">
  <thead><tr><th class="status-col">Status</th><th class="timestamp-col">Timestamp</th><th class="details-col">Details</th></tr></thead>
  <tbody>
      <tr class="event-row">
        <td><span class="badge log pass-bg">Pass</span></td>
        <td>8:19:48 pm</td>
        <td>
          <span class='badge white-text green'>Test Passed</span>
        </td>
      </tr>
  </tbody>
</table>
</div>
          </div>
        </li>
        <li class="test-item"  status="pass" test-id="7"
          author=""
          tag="com.lenskart.example.test.CategoryExampleTest"
          device="">
          <div class="test-detail">
            <p class="name">regressionTest</p>
            <p class="text-sm">
              <span>8:19:48 pm</span> / <span>00:00:01:603</span>
              <span class="badge pass-bg log float-right">Pass</span>
            </p>
          </div>
          <div class="test-contents d-none">
<div class="detail-head">
<div class="p-v-10">
<div class="info">
<h5 class="test-status text-pass">regressionTest</h5>
<span class='badge badge-success'>07.03.2025 8:19:48 pm</span>
<span class='badge badge-danger'>07.03.2025 8:19:50 pm</span>
<span class='badge badge-default'>00:00:01:603</span>
&middot; <span class='uri-anchor badge badge-default'>#test-id=7</span>
<span title='Skip to the next failed step' class='badge badge-danger pointer float-right ne ml-1'><i class="fa fa-fast-forward"></i></span>
<span title='Collapse all nodes' class='badge badge-default pointer float-right ct ml-1'><i class="fa fa-compress"></i></span>
<span title='Expand all nodes' class='badge badge-default pointer float-right et'><i class="fa fa-expand"></i></span>
</div>
<div class="m-t-15"><span class="badge badge-pill badge-default">com.lenskart.example.test.CategoryExampleTest</span></div>
<div class="m-t-10 m-l-5">regressionTest</div>
</div>
</div><div class="detail-body mt-4">
<table class="table table-sm">
  <thead><tr><th class="status-col">Status</th><th class="timestamp-col">Timestamp</th><th class="details-col">Details</th></tr></thead>
  <tbody>
      <tr class="event-row">
        <td><span class="badge log pass-bg">Pass</span></td>
        <td>8:19:50 pm</td>
        <td>
          <span class='badge white-text green'>Test Passed</span>
        </td>
      </tr>
  </tbody>
</table>
</div>
          </div>
        </li>
        <li class="test-item"  status="pass" test-id="8"
          author=""
          tag="com.lenskart.example.test.CategoryExampleTest"
          device="">
          <div class="test-detail">
            <p class="name">regressionTest</p>
            <p class="text-sm">
              <span>8:19:50 pm</span> / <span>00:00:02:088</span>
              <span class="badge pass-bg log float-right">Pass</span>
            </p>
          </div>
          <div class="test-contents d-none">
<div class="detail-head">
<div class="p-v-10">
<div class="info">
<h5 class="test-status text-pass">regressionTest</h5>
<span class='badge badge-success'>07.03.2025 8:19:50 pm</span>
<span class='badge badge-danger'>07.03.2025 8:19:52 pm</span>
<span class='badge badge-default'>00:00:02:088</span>
&middot; <span class='uri-anchor badge badge-default'>#test-id=8</span>
<span title='Skip to the next failed step' class='badge badge-danger pointer float-right ne ml-1'><i class="fa fa-fast-forward"></i></span>
<span title='Collapse all nodes' class='badge badge-default pointer float-right ct ml-1'><i class="fa fa-compress"></i></span>
<span title='Expand all nodes' class='badge badge-default pointer float-right et'><i class="fa fa-expand"></i></span>
</div>
<div class="m-t-15"><span class="badge badge-pill badge-default">com.lenskart.example.test.CategoryExampleTest</span></div>
<div class="m-t-10 m-l-5">regressionTest</div>
</div>
</div><div class="detail-body mt-4">
<table class="table table-sm">
  <thead><tr><th class="status-col">Status</th><th class="timestamp-col">Timestamp</th><th class="details-col">Details</th></tr></thead>
  <tbody>
      <tr class="event-row">
        <td><span class="badge log pass-bg">Pass</span></td>
        <td>8:19:52 pm</td>
        <td>
          <span class='badge white-text green'>Test Passed</span>
        </td>
      </tr>
  </tbody>
</table>
</div>
          </div>
        </li>
        <li class="test-item"  status="pass" test-id="9"
          author=""
          tag="com.lenskart.example.test.CategoryExampleTest"
          device="">
          <div class="test-detail">
            <p class="name">regressionTest</p>
            <p class="text-sm">
              <span>8:19:52 pm</span> / <span>00:00:03:469</span>
              <span class="badge pass-bg log float-right">Pass</span>
            </p>
          </div>
          <div class="test-contents d-none">
<div class="detail-head">
<div class="p-v-10">
<div class="info">
<h5 class="test-status text-pass">regressionTest</h5>
<span class='badge badge-success'>07.03.2025 8:19:52 pm</span>
<span class='badge badge-danger'>07.03.2025 8:19:55 pm</span>
<span class='badge badge-default'>00:00:03:469</span>
&middot; <span class='uri-anchor badge badge-default'>#test-id=9</span>
<span title='Skip to the next failed step' class='badge badge-danger pointer float-right ne ml-1'><i class="fa fa-fast-forward"></i></span>
<span title='Collapse all nodes' class='badge badge-default pointer float-right ct ml-1'><i class="fa fa-compress"></i></span>
<span title='Expand all nodes' class='badge badge-default pointer float-right et'><i class="fa fa-expand"></i></span>
</div>
<div class="m-t-15"><span class="badge badge-pill badge-default">com.lenskart.example.test.CategoryExampleTest</span></div>
<div class="m-t-10 m-l-5">regressionTest</div>
</div>
</div><div class="detail-body mt-4">
<table class="table table-sm">
  <thead><tr><th class="status-col">Status</th><th class="timestamp-col">Timestamp</th><th class="details-col">Details</th></tr></thead>
  <tbody>
      <tr class="event-row">
        <td><span class="badge log pass-bg">Pass</span></td>
        <td>8:19:55 pm</td>
        <td>
          <span class='badge white-text green'>Test Passed</span>
        </td>
      </tr>
  </tbody>
</table>
</div>
          </div>
        </li>
        <li class="test-item"  status="pass" test-id="10"
          author=""
          tag="com.lenskart.example.test.CategoryExampleTest"
          device="">
          <div class="test-detail">
            <p class="name">sanityTest</p>
            <p class="text-sm">
              <span>8:19:55 pm</span> / <span>00:00:01:294</span>
              <span class="badge pass-bg log float-right">Pass</span>
            </p>
          </div>
          <div class="test-contents d-none">
<div class="detail-head">
<div class="p-v-10">
<div class="info">
<h5 class="test-status text-pass">sanityTest</h5>
<span class='badge badge-success'>07.03.2025 8:19:55 pm</span>
<span class='badge badge-danger'>07.03.2025 8:19:57 pm</span>
<span class='badge badge-default'>00:00:01:294</span>
&middot; <span class='uri-anchor badge badge-default'>#test-id=10</span>
<span title='Skip to the next failed step' class='badge badge-danger pointer float-right ne ml-1'><i class="fa fa-fast-forward"></i></span>
<span title='Collapse all nodes' class='badge badge-default pointer float-right ct ml-1'><i class="fa fa-compress"></i></span>
<span title='Expand all nodes' class='badge badge-default pointer float-right et'><i class="fa fa-expand"></i></span>
</div>
<div class="m-t-15"><span class="badge badge-pill badge-default">com.lenskart.example.test.CategoryExampleTest</span></div>
<div class="m-t-10 m-l-5">sanityTest</div>
</div>
</div><div class="detail-body mt-4">
<table class="table table-sm">
  <thead><tr><th class="status-col">Status</th><th class="timestamp-col">Timestamp</th><th class="details-col">Details</th></tr></thead>
  <tbody>
      <tr class="event-row">
        <td><span class="badge log pass-bg">Pass</span></td>
        <td>8:19:57 pm</td>
        <td>
          <span class='badge white-text green'>Test Passed</span>
        </td>
      </tr>
  </tbody>
</table>
</div>
          </div>
        </li>
        <li class="test-item"  status="fail" test-id="11"
          author=""
          tag="com.lenskart.example.test.CategoryExampleTest"
          device="">
          <div class="test-detail">
            <p class="name">sanityTest</p>
            <p class="text-sm">
              <span>8:19:57 pm</span> / <span>00:00:02:862</span>
              <span class="badge fail-bg log float-right">Fail</span>
            </p>
          </div>
          <div class="test-contents d-none">
<div class="detail-head">
<div class="p-v-10">
<div class="info">
<h5 class="test-status text-fail">sanityTest</h5>
<span class='badge badge-success'>07.03.2025 8:19:57 pm</span>
<span class='badge badge-danger'>07.03.2025 8:19:59 pm</span>
<span class='badge badge-default'>00:00:02:862</span>
&middot; <span class='uri-anchor badge badge-default'>#test-id=11</span>
<span title='Skip to the next failed step' class='badge badge-danger pointer float-right ne ml-1'><i class="fa fa-fast-forward"></i></span>
<span title='Collapse all nodes' class='badge badge-default pointer float-right ct ml-1'><i class="fa fa-compress"></i></span>
<span title='Expand all nodes' class='badge badge-default pointer float-right et'><i class="fa fa-expand"></i></span>
</div>
<div class="m-t-15"><span class="badge badge-pill badge-default">com.lenskart.example.test.CategoryExampleTest</span></div>
<div class="m-t-10 m-l-5">sanityTest</div>
</div>
</div><div class="detail-body mt-4">
<table class="table table-sm">
  <thead><tr><th class="status-col">Status</th><th class="timestamp-col">Timestamp</th><th class="details-col">Details</th></tr></thead>
  <tbody>
      <tr class="event-row">
        <td><span class="badge log fail-bg">Fail</span></td>
        <td>8:19:59 pm</td>
        <td>
          <span class='badge white-text red'>Test Failed</span>
        </td>
      </tr>
      <tr class="event-row">
        <td><span class="badge log fail-bg">Fail</span></td>
        <td>8:19:59 pm</td>
        <td>
          <textarea readonly class="code-block">java.lang.AssertionError: Failed to parse JSON string to object: Unexpected character ('<' (code 60)): expected a valid value (JSON String, Number, Array, Object or token 'null', 'true' or 'false')
 at [Source: (String)"<html>
<head><title>502 Bad Gateway</title></head>
<body>
<center><h1>502 Bad Gateway</h1></center>
</body>
</html>
"; line: 1, column: 2]
	at org.testng.Assert.fail(Assert.java:111)
	at com.lenskart.commons.utils.JsonUtils.parseJsonString(JsonUtils.java:18)
	at com.lenskart.commons.base.BaseHelper.parseResponse(BaseHelper.java:25)
	at com.lenskart.example.helper.GetDataHelper.process(GetDataHelper.java:37)
	at com.lenskart.example.helper.GetDataHelper.test(GetDataHelper.java:56)
	at com.lenskart.example.test.CategoryExampleTest.sanityTest(CategoryExampleTest.java:36)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.testng.internal.invokers.MethodInvocationHelper.invokeMethod(MethodInvocationHelper.java:139)
	at org.testng.internal.invokers.TestInvoker.invokeMethod(TestInvoker.java:664)
	at org.testng.internal.invokers.TestInvoker.invokeTestMethod(TestInvoker.java:227)
	at org.testng.internal.invokers.MethodRunner.runInSequence(MethodRunner.java:50)
	at org.testng.internal.invokers.TestInvoker$MethodInvocationAgent.invoke(TestInvoker.java:957)
	at org.testng.internal.invokers.TestInvoker.invokeTestMethods(TestInvoker.java:200)
	at org.testng.internal.invokers.TestMethodWorker.invokeTestMethods(TestMethodWorker.java:148)
	at org.testng.internal.invokers.TestMethodWorker.run(TestMethodWorker.java:128)
	at java.base/java.util.ArrayList.forEach(ArrayList.java:1596)
	at org.testng.TestRunner.privateRun(TestRunner.java:848)
	at org.testng.TestRunner.run(TestRunner.java:621)
	at org.testng.SuiteRunner.runTest(SuiteRunner.java:443)
	at org.testng.SuiteRunner.runSequentially(SuiteRunner.java:437)
	at org.testng.SuiteRunner.privateRun(SuiteRunner.java:397)
	at org.testng.SuiteRunner.run(SuiteRunner.java:336)
	at org.testng.SuiteRunnerWorker.runSuite(SuiteRunnerWorker.java:52)
	at org.testng.SuiteRunnerWorker.run(SuiteRunnerWorker.java:95)
	at org.testng.TestNG.runSuitesSequentially(TestNG.java:1280)
	at org.testng.TestNG.runSuitesLocally(TestNG.java:1200)
	at org.testng.TestNG.runSuites(TestNG.java:1114)
	at org.testng.TestNG.run(TestNG.java:1082)
	at com.intellij.rt.testng.IDEARemoteTestNG.run(IDEARemoteTestNG.java:65)
	at com.intellij.rt.testng.RemoteTestNGStarter.main(RemoteTestNGStarter.java:105)
</textarea>
          
        </td>
      </tr>
  </tbody>
</table>
</div>
          </div>
        </li>
        <li class="test-item"  status="pass" test-id="12"
          author=""
          tag="com.lenskart.example.test.CategoryExampleTest"
          device="">
          <div class="test-detail">
            <p class="name">sanityTest</p>
            <p class="text-sm">
              <span>8:19:59 pm</span> / <span>00:00:06:589</span>
              <span class="badge pass-bg log float-right">Pass</span>
            </p>
          </div>
          <div class="test-contents d-none">
<div class="detail-head">
<div class="p-v-10">
<div class="info">
<h5 class="test-status text-pass">sanityTest</h5>
<span class='badge badge-success'>07.03.2025 8:19:59 pm</span>
<span class='badge badge-danger'>07.03.2025 8:20:06 pm</span>
<span class='badge badge-default'>00:00:06:589</span>
&middot; <span class='uri-anchor badge badge-default'>#test-id=12</span>
<span title='Skip to the next failed step' class='badge badge-danger pointer float-right ne ml-1'><i class="fa fa-fast-forward"></i></span>
<span title='Collapse all nodes' class='badge badge-default pointer float-right ct ml-1'><i class="fa fa-compress"></i></span>
<span title='Expand all nodes' class='badge badge-default pointer float-right et'><i class="fa fa-expand"></i></span>
</div>
<div class="m-t-15"><span class="badge badge-pill badge-default">com.lenskart.example.test.CategoryExampleTest</span></div>
<div class="m-t-10 m-l-5">sanityTest</div>
</div>
</div><div class="detail-body mt-4">
<table class="table table-sm">
  <thead><tr><th class="status-col">Status</th><th class="timestamp-col">Timestamp</th><th class="details-col">Details</th></tr></thead>
  <tbody>
      <tr class="event-row">
        <td><span class="badge log pass-bg">Pass</span></td>
        <td>8:20:06 pm</td>
        <td>
          <span class='badge white-text green'>Test Passed</span>
        </td>
      </tr>
  </tbody>
</table>
</div>
          </div>
        </li>
      </ul>
    </div>
  </div>
<div class="test-content scrollable">
<div class="test-content-tools">
<ul><li><a class="back-to-test" href="#"><i class="fa fa-arrow-left"></i></a></li></ul>
</div>
<div class="test-content-detail"><div class="detail-body"></div></div>
</div></div>
<div class="test-wrapper row view exception-view attributes-view">
<div class="test-list">
<div class="test-list-tools">
<ul class="tools pull-left"><li><a href=""><span class="font-size-14">Exception</span></a></li></ul>
<ul class="tools text-right"><li><a href="#"><span class="badge badge-primary">1</span></a></li></ul>
</div>
<div class="test-list-wrapper scrollable">
<ul class="test-list-item">
<li class="test-item">
<div class="test-detail">
<span class="meta">
<span class='badge log badge-danger'>2</span>
</span>
<p class="name">java.lang.AssertionError</p>
<p class="duration text-sm">2 tests</p>
</div>
<div class="test-contents d-none">
<div class="info">
<h4>java.lang.AssertionError</h4>
<span status="fail" class='badge log badge-danger'>2 failed</span>
</div>
<table class='table table-sm mt-4'>
<thead>
<tr>
<th class="status-col">Status</th>
<th class="timestamp-col">Timestamp</th>
<th>TestName</th>
</tr>
</thead>
<tbody>
<tr class="tag-test-status" status="fail">
<td><span class="badge log fail-bg">Fail</span></td>
<td>20:19:44 pm</td>
<td>
<a href="#" class="linked" test-id='5' id='5'>e2eTest</a>
</td>
</tr>
<tr class="tag-test-status" status="fail">
<td><span class="badge log fail-bg">Fail</span></td>
<td>20:19:57 pm</td>
<td>
<a href="#" class="linked" test-id='11' id='11'>sanityTest</a>
</td>
</tr>
</tbody>
</table>
</div>
</li>
</ul>
</div>
</div>
<div class="test-content scrollable">
<div class="test-content-detail">
<div class="detail-body"></div>
</div>
</div>
</div><div class="test-wrapper row view category-view attributes-view">
<div class="test-list">
<div class="test-list-tools">
<ul class="tools pull-left"><li><a href=""><span class="font-size-14">Category</span></a></li></ul>
<ul class="tools text-right"><li><a href="#"><span class="badge badge-primary">2</span></a></li></ul>
</div>
<div class="test-list-wrapper scrollable">
<ul class="test-list-item">
<li class="test-item">
<div class="test-detail">
<span class="meta">
<span class='badge log pass-bg'>7</span>
<span class='badge log badge-danger'>2</span>
</span>
<p class="name">com.lenskart.example.test.CategoryExampleTest</p>
<p class="duration text-sm">9 tests</p>
</div>
<div class="test-contents d-none">
<div class="info">
<h4>com.lenskart.example.test.CategoryExampleTest</h4>
<span status="pass" class='badge log pass-bg'>7 passed</span>
<span status="fail" class='badge log badge-danger'>2 failed</span>
</div>
<table class='table table-sm mt-4'>
<thead>
<tr>
<th class="status-col">Status</th>
<th class="timestamp-col">Timestamp</th>
<th>TestName</th>
</tr>
</thead>
<tbody>
<tr class="tag-test-status" status="pass">
<td><span class="badge log pass-bg">Pass</span></td>
<td>20:19:43 pm</td>
<td>
<a href="#" class="linked" test-id='4' id='4'>e2eTest</a>
</td>
</tr>
<tr class="tag-test-status" status="fail">
<td><span class="badge log fail-bg">Fail</span></td>
<td>20:19:44 pm</td>
<td>
<a href="#" class="linked" test-id='5' id='5'>e2eTest</a>
</td>
</tr>
<tr class="tag-test-status" status="pass">
<td><span class="badge log pass-bg">Pass</span></td>
<td>20:19:47 pm</td>
<td>
<a href="#" class="linked" test-id='6' id='6'>e2eTest</a>
</td>
</tr>
<tr class="tag-test-status" status="pass">
<td><span class="badge log pass-bg">Pass</span></td>
<td>20:19:48 pm</td>
<td>
<a href="#" class="linked" test-id='7' id='7'>regressionTest</a>
</td>
</tr>
<tr class="tag-test-status" status="pass">
<td><span class="badge log pass-bg">Pass</span></td>
<td>20:19:50 pm</td>
<td>
<a href="#" class="linked" test-id='8' id='8'>regressionTest</a>
</td>
</tr>
<tr class="tag-test-status" status="pass">
<td><span class="badge log pass-bg">Pass</span></td>
<td>20:19:52 pm</td>
<td>
<a href="#" class="linked" test-id='9' id='9'>regressionTest</a>
</td>
</tr>
<tr class="tag-test-status" status="pass">
<td><span class="badge log pass-bg">Pass</span></td>
<td>20:19:55 pm</td>
<td>
<a href="#" class="linked" test-id='10' id='10'>sanityTest</a>
</td>
</tr>
<tr class="tag-test-status" status="fail">
<td><span class="badge log fail-bg">Fail</span></td>
<td>20:19:57 pm</td>
<td>
<a href="#" class="linked" test-id='11' id='11'>sanityTest</a>
</td>
</tr>
<tr class="tag-test-status" status="pass">
<td><span class="badge log pass-bg">Pass</span></td>
<td>20:19:59 pm</td>
<td>
<a href="#" class="linked" test-id='12' id='12'>sanityTest</a>
</td>
</tr>
</tbody>
</table>
</div>
</li>
<li class="test-item">
<div class="test-detail">
<span class="meta">
<span class='badge log pass-bg'>3</span>
</span>
<p class="name">com.lenskart.example.test.GetDiscoveryTest</p>
<p class="duration text-sm">3 tests</p>
</div>
<div class="test-contents d-none">
<div class="info">
<h4>com.lenskart.example.test.GetDiscoveryTest</h4>
<span status="pass" class='badge log pass-bg'>3 passed</span>
</div>
<table class='table table-sm mt-4'>
<thead>
<tr>
<th class="status-col">Status</th>
<th class="timestamp-col">Timestamp</th>
<th>TestName</th>
</tr>
</thead>
<tbody>
<tr class="tag-test-status" status="pass">
<td><span class="badge log pass-bg">Pass</span></td>
<td>20:19:34 pm</td>
<td>
<a href="#" class="linked" test-id='1' id='1'>getDiscoveryTest</a>
</td>
</tr>
<tr class="tag-test-status" status="pass">
<td><span class="badge log pass-bg">Pass</span></td>
<td>20:19:39 pm</td>
<td>
<a href="#" class="linked" test-id='2' id='2'>getDiscoveryTest</a>
</td>
</tr>
<tr class="tag-test-status" status="pass">
<td><span class="badge log pass-bg">Pass</span></td>
<td>20:19:40 pm</td>
<td>
<a href="#" class="linked" test-id='3' id='3'>getDiscoveryTest</a>
</td>
</tr>
</tbody>
</table>
</div>
</li>
</ul>
</div>
</div>
<div class="test-content scrollable">
<div class="test-content-detail">
<div class="detail-body"></div>
</div>
</div>
</div><div class="container-fluid p-4 view dashboard-view">
<div class="row">
<div class="col-md-3">
<div class="card"><div class="card-body">
<p class="m-b-0">Started</p>
<h3>2025-07-03 20:19:34</h3>
</div></div>
</div>
<div class="col-md-3">
<div class="card"><div class="card-body">
<p class="m-b-0">Ended</p>
<h3>2025-07-03 20:19:34</h3>
</div></div>
</div>
<div class="col-md-3">
<div class="card"><div class="card-body">
<p class="m-b-0 text-pass">Tests Passed</p>
<h3>10</h3>
</div></div>
</div>
<div class="col-md-3">
<div class="card"><div class="card-body">
<p class="m-b-0 text-fail">Tests Failed</p>
<h3>2</h3>
</div></div>
</div>
</div>
<div class="row">
<div class="col-md-6">
<div class="card">
<div class="card-header">
<h6 class="card-title">Tests</h6>
</div>
<div class="card-body">
<div class="">
<canvas id='parent-analysis' width='115' height='90'></canvas>
</div>
</div>
<div class="card-footer">
<div><small data-tooltip='83%'>
<b>10</b> tests passed
</small>
</div>
<div>
<small data-tooltip='16%'><b>2</b> tests failed,
<b>0</b> skipped, <b data-tooltip='0%'>0</b> others
</small>
</div>
</div>
</div>
</div>
<div class="col-md-6">
<div class="card">
<div class="card-header">
<h6 class="card-title">Log events</h6>
</div>
<div class="card-body">
<div class="">
<canvas id='events-analysis' width='115' height='90'></canvas>
</div>
</div>
<div class="card-footer">
<div><small data-tooltip='71%'><b>10</b> events passed</small></div>
<div>
<small data-tooltip='28%'><b>4</b> events failed,
<b data-tooltip='%'>0</b> others
</small>
</div>
</div>
</div>
</div>
</div>
<div class="row"><div class="col-md-12">
<div class="card"><div class="card-header"><p>Timeline</p></div>
<div class="card-body pt-0"><div>
<canvas id="timeline" height="120"></canvas>
</div></div>
</div>
</div></div>
<script>
var timeline = {
"getDiscoveryTest":4.58,"getDiscoveryTest":1.272,"getDiscoveryTest":2.809,"e2eTest":1.099,"e2eTest":2.991,"e2eTest":1.469,"regressionTest":1.603,"regressionTest":2.088,"regressionTest":3.469,"sanityTest":1.294,"sanityTest":2.862,"sanityTest":6.589
};
</script>
<div class="row">
<div class="col-lg-6 col-md-12 category-container">
<div class="card">
<div class="card-header"><p>Tags</p></div>
<div class="card-body pb-0 pt-0"><table class="table table-sm table-bordered">
<thead><tr class="bg-gray"><th>Name</th><th>Passed</th><th>Failed</th><th>Skipped</th><th>Others</th><th>Passed %</th></tr></thead><tbody>
<tr>
<td>com.lenskart.example.test.CategoryExampleTest</td>
<td>7</td>
<td>2</td>
<td>0</td>
<td>0</td>
<td>77.778%</td>
</tr>
<tr>
<td>com.lenskart.example.test.GetDiscoveryTest</td>
<td>3</td>
<td>0</td>
<td>0</td>
<td>0</td>
<td>100%</td>
</tr>
</tbody>
</table></div>
</div>
</div>
<div class="col-lg-6 col-md-12 sysenv-container">
<div class="card">
<div class="card-header"><p>System/Environment</p></div>
<div class="card-body pb-0 pt-0"><table class="table table-sm table-bordered">
<thead><tr class="bg-gray"><th>Name</th><th>Value</th></tr></thead>
<tbody>
<tr>
<td>OS</td>
<td>Mac OS X</td>
</tr>
<tr>
<td>Java Version</td>
<td>21.0.7</td>
</tr>
<tr>
<td>User</td>
<td>amit</td>
</tr>
<tr>
<td>Organization</td>
<td>Lenskart</td>
</tr>
<tr>
<td>Timezone</td>
<td>IST</td>
</tr>
</tbody>
</table></div>
</div>
</div>
</div>
</div>
<script>
var statusGroup = {
parentCount: 5,
passParent: 10,
failParent: 2,
warningParent: 0,
skipParent: 0,
childCount: 5,
passChild: 0,
failChild: 0,
warningChild: 0,
skipChild: 0,
infoChild: 0,
grandChildCount: 5,
passGrandChild: 0,
failGrandChild: 0,
warningGrandChild: 0,
skipGrandChild: 0,
infoGrandChild: 0,
eventsCount: 5,
passEvents: 10,
failEvents: 4,
warningEvents: 0,
skipEvents: 0,
infoEvents: 0
};
</script>        </div>
      </div>
    </div>
  </div>
<script src="https://cdn.jsdelivr.net/gh/extent-framework/extent-github-cdn@ce8b10435bcbae260c334c0d0c6b61d2c19b6168/spark/js/spark-script.js"></script>
<script type="text/javascript"></script></body>
</html>
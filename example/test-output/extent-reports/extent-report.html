

<!DOCTYPE html>
<html>
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
<title>API Automation Test Report</title>
<link rel="apple-touch-icon" href="https://cdn.jsdelivr.net/gh/extent-framework/extent-github-cdn@b00a2d0486596e73dd7326beacf352c639623a0e/commons/img/logo.png">
<link rel="shortcut icon" href="https://cdn.jsdelivr.net/gh/extent-framework/extent-github-cdn@b00a2d0486596e73dd7326beacf352c639623a0e/commons/img/logo.png">
<link href="https://cdn.jsdelivr.net/gh/extent-framework/extent-github-cdn@ce8b10435bcbae260c334c0d0c6b61d2c19b6168/spark/css/spark-style.css" rel="stylesheet" />
<link href="https://stackpath.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet">
<script src="https://cdn.jsdelivr.net/gh/extent-framework/extent-github-cdn@7cc78ce/spark/js/jsontree.js"></script>
<style type="text/css"></style></head><body class="spa -report dark">
  <div class="app">
    <div class="layout">
<div class="header navbar">
<div class="vheader">
<div class="nav-logo">
<a href="#">
<div class="logo" style="background-image: url('https://cdn.jsdelivr.net/gh/extent-framework/extent-github-cdn@b00a2d0486596e73dd7326beacf352c639623a0e/commons/img/logo.png')"></div>
</a>
</div>
<ul class="nav-left">
<li class="search-box">
<a class="search-toggle" href="#">
<i class="search-icon fa fa-search"></i>
<i class="search-icon-close fa fa-close"></i>
</a>
</li>
<li class="search-input"><input id="search-tests" class="form-control" type="text" placeholder="Search..."></li>
</ul>
<ul class="nav-right">
<li class="m-r-10">
<a href="#"><span class="badge badge-primary">Automation Report</span></a>
</li>
<li class="m-r-10">
<a href="#"><span class="badge badge-primary">2025-06-19 18:06:43</span></a>
</li>
</ul>
</div>
</div><div class="side-nav">
<div class="side-nav-inner">
<ul class="side-nav-menu">
<li class="nav-item dropdown" onclick="toggleView('test-view')">
<a id="nav-test" class="dropdown-toggle" href="#">
<span class="ico"><i class="fa fa-list"></i></span>
</a>
</li>
<li class="nav-item dropdown" onclick="toggleView('category-view')">
<a id="nav-category" class="dropdown-toggle" href="#">
<span class="ico"><i class="fa fa-tag"></i></span>
</a>
</li>
<li class="nav-item dropdown" onclick="toggleView('dashboard-view')">
<a id="nav-dashboard" class="dropdown-toggle" href="#">
<span class="ico"><i class="fa fa-bar-chart"></i></span>
</a>
</li>
</ul>
</div>
</div>      <div class="vcontainer">
        <div class="main-content">
<div class="test-wrapper row view test-view">
  <div class="test-list">
    <div class="test-list-tools">
<ul class="tools pull-left">
<li><a href="#"><span class="font-size-14">Tests</span></a></li>
</ul>
<ul class="tools text-right">
<li class="dropdown">
<a href="#" class="dropdown-toggle" data-toggle="dropdown"><i class="fa fa-exclamation-circle"></i></a>
<ul id="status-toggle" class="dropdown-menu dropdown-md p-v-0">
<a class="dropdown-item" status="pass" href="#"><span>Pass</span><span class="status success"></span></a>
<div class="dropdown-divider"></div>
<a status="clear" class="dropdown-item" href="#"><span>Clear</span><span class="pull-right"><i class="fa fa-close"></i></span></a>
</ul>
</li>
<li class="dropdown">
<a href="#" class="dropdown-toggle" data-toggle="dropdown"><i class="fa fa-tag"></i></a>
<ul id="tag-toggle" class="dropdown-menu dropdown-md p-v-0">
<a class="dropdown-item" href="#">com.lenskart.example.test.CategoryExampleTest</a><a class="dropdown-item" href="#">com.lenskart.example.test.GetDiscoveryTest</a>
</ul>
</li>
</ul>
</div>    <div class="test-list-wrapper scrollable">
      <ul class="test-list-item">
        <li class="test-item"  status="pass" test-id="1"
          author=""
          tag="com.lenskart.example.test.GetDiscoveryTest"
          device="">
          <div class="test-detail">
            <p class="name">getDiscoveryTest</p>
            <p class="text-sm">
              <span>6:06:43 pm</span> / <span>00:00:02:105</span>
              <span class="badge pass-bg log float-right">Pass</span>
            </p>
          </div>
          <div class="test-contents d-none">
<div class="detail-head">
<div class="p-v-10">
<div class="info">
<h5 class="test-status text-pass">getDiscoveryTest</h5>
<span class='badge badge-success'>06.19.2025 6:06:43 pm</span>
<span class='badge badge-danger'>06.19.2025 6:06:45 pm</span>
<span class='badge badge-default'>00:00:02:105</span>
&middot; <span class='uri-anchor badge badge-default'>#test-id=1</span>
<span title='Skip to the next failed step' class='badge badge-danger pointer float-right ne ml-1'><i class="fa fa-fast-forward"></i></span>
<span title='Collapse all nodes' class='badge badge-default pointer float-right ct ml-1'><i class="fa fa-compress"></i></span>
<span title='Expand all nodes' class='badge badge-default pointer float-right et'><i class="fa fa-expand"></i></span>
</div>
<div class="m-t-15"><span class="badge badge-pill badge-default">com.lenskart.example.test.GetDiscoveryTest</span></div>
<div class="m-t-10 m-l-5">getDiscoveryTest</div>
</div>
</div><div class="detail-body mt-4">
<table class="table table-sm">
  <thead><tr><th class="status-col">Status</th><th class="timestamp-col">Timestamp</th><th class="details-col">Details</th></tr></thead>
  <tbody>
      <tr class="event-row">
        <td><span class="badge log pass-bg">Pass</span></td>
        <td>6:06:45 pm</td>
        <td>
          <span class='badge white-text green'>Test Passed</span>
        </td>
      </tr>
  </tbody>
</table>
</div>
          </div>
        </li>
        <li class="test-item"  status="pass" test-id="2"
          author=""
          tag="com.lenskart.example.test.GetDiscoveryTest"
          device="">
          <div class="test-detail">
            <p class="name">getDiscoveryTest</p>
            <p class="text-sm">
              <span>6:06:45 pm</span> / <span>00:00:01:775</span>
              <span class="badge pass-bg log float-right">Pass</span>
            </p>
          </div>
          <div class="test-contents d-none">
<div class="detail-head">
<div class="p-v-10">
<div class="info">
<h5 class="test-status text-pass">getDiscoveryTest</h5>
<span class='badge badge-success'>06.19.2025 6:06:45 pm</span>
<span class='badge badge-danger'>06.19.2025 6:06:47 pm</span>
<span class='badge badge-default'>00:00:01:775</span>
&middot; <span class='uri-anchor badge badge-default'>#test-id=2</span>
<span title='Skip to the next failed step' class='badge badge-danger pointer float-right ne ml-1'><i class="fa fa-fast-forward"></i></span>
<span title='Collapse all nodes' class='badge badge-default pointer float-right ct ml-1'><i class="fa fa-compress"></i></span>
<span title='Expand all nodes' class='badge badge-default pointer float-right et'><i class="fa fa-expand"></i></span>
</div>
<div class="m-t-15"><span class="badge badge-pill badge-default">com.lenskart.example.test.GetDiscoveryTest</span></div>
<div class="m-t-10 m-l-5">getDiscoveryTest</div>
</div>
</div><div class="detail-body mt-4">
<table class="table table-sm">
  <thead><tr><th class="status-col">Status</th><th class="timestamp-col">Timestamp</th><th class="details-col">Details</th></tr></thead>
  <tbody>
      <tr class="event-row">
        <td><span class="badge log pass-bg">Pass</span></td>
        <td>6:06:47 pm</td>
        <td>
          <span class='badge white-text green'>Test Passed</span>
        </td>
      </tr>
  </tbody>
</table>
</div>
          </div>
        </li>
        <li class="test-item"  status="pass" test-id="3"
          author=""
          tag="com.lenskart.example.test.GetDiscoveryTest"
          device="">
          <div class="test-detail">
            <p class="name">getDiscoveryTest</p>
            <p class="text-sm">
              <span>6:06:47 pm</span> / <span>00:00:01:250</span>
              <span class="badge pass-bg log float-right">Pass</span>
            </p>
          </div>
          <div class="test-contents d-none">
<div class="detail-head">
<div class="p-v-10">
<div class="info">
<h5 class="test-status text-pass">getDiscoveryTest</h5>
<span class='badge badge-success'>06.19.2025 6:06:47 pm</span>
<span class='badge badge-danger'>06.19.2025 6:06:48 pm</span>
<span class='badge badge-default'>00:00:01:250</span>
&middot; <span class='uri-anchor badge badge-default'>#test-id=3</span>
<span title='Skip to the next failed step' class='badge badge-danger pointer float-right ne ml-1'><i class="fa fa-fast-forward"></i></span>
<span title='Collapse all nodes' class='badge badge-default pointer float-right ct ml-1'><i class="fa fa-compress"></i></span>
<span title='Expand all nodes' class='badge badge-default pointer float-right et'><i class="fa fa-expand"></i></span>
</div>
<div class="m-t-15"><span class="badge badge-pill badge-default">com.lenskart.example.test.GetDiscoveryTest</span></div>
<div class="m-t-10 m-l-5">getDiscoveryTest</div>
</div>
</div><div class="detail-body mt-4">
<table class="table table-sm">
  <thead><tr><th class="status-col">Status</th><th class="timestamp-col">Timestamp</th><th class="details-col">Details</th></tr></thead>
  <tbody>
      <tr class="event-row">
        <td><span class="badge log pass-bg">Pass</span></td>
        <td>6:06:48 pm</td>
        <td>
          <span class='badge white-text green'>Test Passed</span>
        </td>
      </tr>
  </tbody>
</table>
</div>
          </div>
        </li>
        <li class="test-item"  status="pass" test-id="4"
          author=""
          tag="com.lenskart.example.test.CategoryExampleTest"
          device="">
          <div class="test-detail">
            <p class="name">e2eTest</p>
            <p class="text-sm">
              <span>6:06:49 pm</span> / <span>00:00:02:748</span>
              <span class="badge pass-bg log float-right">Pass</span>
            </p>
          </div>
          <div class="test-contents d-none">
<div class="detail-head">
<div class="p-v-10">
<div class="info">
<h5 class="test-status text-pass">e2eTest</h5>
<span class='badge badge-success'>06.19.2025 6:06:49 pm</span>
<span class='badge badge-danger'>06.19.2025 6:06:51 pm</span>
<span class='badge badge-default'>00:00:02:748</span>
&middot; <span class='uri-anchor badge badge-default'>#test-id=4</span>
<span title='Skip to the next failed step' class='badge badge-danger pointer float-right ne ml-1'><i class="fa fa-fast-forward"></i></span>
<span title='Collapse all nodes' class='badge badge-default pointer float-right ct ml-1'><i class="fa fa-compress"></i></span>
<span title='Expand all nodes' class='badge badge-default pointer float-right et'><i class="fa fa-expand"></i></span>
</div>
<div class="m-t-15"><span class="badge badge-pill badge-default">com.lenskart.example.test.CategoryExampleTest</span></div>
<div class="m-t-10 m-l-5">e2eTest</div>
</div>
</div><div class="detail-body mt-4">
<table class="table table-sm">
  <thead><tr><th class="status-col">Status</th><th class="timestamp-col">Timestamp</th><th class="details-col">Details</th></tr></thead>
  <tbody>
      <tr class="event-row">
        <td><span class="badge log pass-bg">Pass</span></td>
        <td>6:06:51 pm</td>
        <td>
          <span class='badge white-text green'>Test Passed</span>
        </td>
      </tr>
  </tbody>
</table>
</div>
          </div>
        </li>
        <li class="test-item"  status="pass" test-id="5"
          author=""
          tag="com.lenskart.example.test.CategoryExampleTest"
          device="">
          <div class="test-detail">
            <p class="name">e2eTest</p>
            <p class="text-sm">
              <span>6:06:51 pm</span> / <span>00:00:01:160</span>
              <span class="badge pass-bg log float-right">Pass</span>
            </p>
          </div>
          <div class="test-contents d-none">
<div class="detail-head">
<div class="p-v-10">
<div class="info">
<h5 class="test-status text-pass">e2eTest</h5>
<span class='badge badge-success'>06.19.2025 6:06:51 pm</span>
<span class='badge badge-danger'>06.19.2025 6:06:52 pm</span>
<span class='badge badge-default'>00:00:01:160</span>
&middot; <span class='uri-anchor badge badge-default'>#test-id=5</span>
<span title='Skip to the next failed step' class='badge badge-danger pointer float-right ne ml-1'><i class="fa fa-fast-forward"></i></span>
<span title='Collapse all nodes' class='badge badge-default pointer float-right ct ml-1'><i class="fa fa-compress"></i></span>
<span title='Expand all nodes' class='badge badge-default pointer float-right et'><i class="fa fa-expand"></i></span>
</div>
<div class="m-t-15"><span class="badge badge-pill badge-default">com.lenskart.example.test.CategoryExampleTest</span></div>
<div class="m-t-10 m-l-5">e2eTest</div>
</div>
</div><div class="detail-body mt-4">
<table class="table table-sm">
  <thead><tr><th class="status-col">Status</th><th class="timestamp-col">Timestamp</th><th class="details-col">Details</th></tr></thead>
  <tbody>
      <tr class="event-row">
        <td><span class="badge log pass-bg">Pass</span></td>
        <td>6:06:52 pm</td>
        <td>
          <span class='badge white-text green'>Test Passed</span>
        </td>
      </tr>
  </tbody>
</table>
</div>
          </div>
        </li>
        <li class="test-item"  status="pass" test-id="6"
          author=""
          tag="com.lenskart.example.test.CategoryExampleTest"
          device="">
          <div class="test-detail">
            <p class="name">e2eTest</p>
            <p class="text-sm">
              <span>6:06:52 pm</span> / <span>00:00:01:340</span>
              <span class="badge pass-bg log float-right">Pass</span>
            </p>
          </div>
          <div class="test-contents d-none">
<div class="detail-head">
<div class="p-v-10">
<div class="info">
<h5 class="test-status text-pass">e2eTest</h5>
<span class='badge badge-success'>06.19.2025 6:06:52 pm</span>
<span class='badge badge-danger'>06.19.2025 6:06:54 pm</span>
<span class='badge badge-default'>00:00:01:340</span>
&middot; <span class='uri-anchor badge badge-default'>#test-id=6</span>
<span title='Skip to the next failed step' class='badge badge-danger pointer float-right ne ml-1'><i class="fa fa-fast-forward"></i></span>
<span title='Collapse all nodes' class='badge badge-default pointer float-right ct ml-1'><i class="fa fa-compress"></i></span>
<span title='Expand all nodes' class='badge badge-default pointer float-right et'><i class="fa fa-expand"></i></span>
</div>
<div class="m-t-15"><span class="badge badge-pill badge-default">com.lenskart.example.test.CategoryExampleTest</span></div>
<div class="m-t-10 m-l-5">e2eTest</div>
</div>
</div><div class="detail-body mt-4">
<table class="table table-sm">
  <thead><tr><th class="status-col">Status</th><th class="timestamp-col">Timestamp</th><th class="details-col">Details</th></tr></thead>
  <tbody>
      <tr class="event-row">
        <td><span class="badge log pass-bg">Pass</span></td>
        <td>6:06:54 pm</td>
        <td>
          <span class='badge white-text green'>Test Passed</span>
        </td>
      </tr>
  </tbody>
</table>
</div>
          </div>
        </li>
        <li class="test-item"  status="pass" test-id="7"
          author=""
          tag="com.lenskart.example.test.CategoryExampleTest"
          device="">
          <div class="test-detail">
            <p class="name">regressionTest</p>
            <p class="text-sm">
              <span>6:06:54 pm</span> / <span>00:00:01:453</span>
              <span class="badge pass-bg log float-right">Pass</span>
            </p>
          </div>
          <div class="test-contents d-none">
<div class="detail-head">
<div class="p-v-10">
<div class="info">
<h5 class="test-status text-pass">regressionTest</h5>
<span class='badge badge-success'>06.19.2025 6:06:54 pm</span>
<span class='badge badge-danger'>06.19.2025 6:06:55 pm</span>
<span class='badge badge-default'>00:00:01:453</span>
&middot; <span class='uri-anchor badge badge-default'>#test-id=7</span>
<span title='Skip to the next failed step' class='badge badge-danger pointer float-right ne ml-1'><i class="fa fa-fast-forward"></i></span>
<span title='Collapse all nodes' class='badge badge-default pointer float-right ct ml-1'><i class="fa fa-compress"></i></span>
<span title='Expand all nodes' class='badge badge-default pointer float-right et'><i class="fa fa-expand"></i></span>
</div>
<div class="m-t-15"><span class="badge badge-pill badge-default">com.lenskart.example.test.CategoryExampleTest</span></div>
<div class="m-t-10 m-l-5">regressionTest</div>
</div>
</div><div class="detail-body mt-4">
<table class="table table-sm">
  <thead><tr><th class="status-col">Status</th><th class="timestamp-col">Timestamp</th><th class="details-col">Details</th></tr></thead>
  <tbody>
      <tr class="event-row">
        <td><span class="badge log pass-bg">Pass</span></td>
        <td>6:06:55 pm</td>
        <td>
          <span class='badge white-text green'>Test Passed</span>
        </td>
      </tr>
  </tbody>
</table>
</div>
          </div>
        </li>
        <li class="test-item"  status="pass" test-id="8"
          author=""
          tag="com.lenskart.example.test.CategoryExampleTest"
          device="">
          <div class="test-detail">
            <p class="name">regressionTest</p>
            <p class="text-sm">
              <span>6:06:55 pm</span> / <span>00:00:01:183</span>
              <span class="badge pass-bg log float-right">Pass</span>
            </p>
          </div>
          <div class="test-contents d-none">
<div class="detail-head">
<div class="p-v-10">
<div class="info">
<h5 class="test-status text-pass">regressionTest</h5>
<span class='badge badge-success'>06.19.2025 6:06:55 pm</span>
<span class='badge badge-danger'>06.19.2025 6:06:56 pm</span>
<span class='badge badge-default'>00:00:01:183</span>
&middot; <span class='uri-anchor badge badge-default'>#test-id=8</span>
<span title='Skip to the next failed step' class='badge badge-danger pointer float-right ne ml-1'><i class="fa fa-fast-forward"></i></span>
<span title='Collapse all nodes' class='badge badge-default pointer float-right ct ml-1'><i class="fa fa-compress"></i></span>
<span title='Expand all nodes' class='badge badge-default pointer float-right et'><i class="fa fa-expand"></i></span>
</div>
<div class="m-t-15"><span class="badge badge-pill badge-default">com.lenskart.example.test.CategoryExampleTest</span></div>
<div class="m-t-10 m-l-5">regressionTest</div>
</div>
</div><div class="detail-body mt-4">
<table class="table table-sm">
  <thead><tr><th class="status-col">Status</th><th class="timestamp-col">Timestamp</th><th class="details-col">Details</th></tr></thead>
  <tbody>
      <tr class="event-row">
        <td><span class="badge log pass-bg">Pass</span></td>
        <td>6:06:56 pm</td>
        <td>
          <span class='badge white-text green'>Test Passed</span>
        </td>
      </tr>
  </tbody>
</table>
</div>
          </div>
        </li>
        <li class="test-item"  status="pass" test-id="9"
          author=""
          tag="com.lenskart.example.test.CategoryExampleTest"
          device="">
          <div class="test-detail">
            <p class="name">regressionTest</p>
            <p class="text-sm">
              <span>6:06:56 pm</span> / <span>00:00:03:218</span>
              <span class="badge pass-bg log float-right">Pass</span>
            </p>
          </div>
          <div class="test-contents d-none">
<div class="detail-head">
<div class="p-v-10">
<div class="info">
<h5 class="test-status text-pass">regressionTest</h5>
<span class='badge badge-success'>06.19.2025 6:06:56 pm</span>
<span class='badge badge-danger'>06.19.2025 6:07:00 pm</span>
<span class='badge badge-default'>00:00:03:218</span>
&middot; <span class='uri-anchor badge badge-default'>#test-id=9</span>
<span title='Skip to the next failed step' class='badge badge-danger pointer float-right ne ml-1'><i class="fa fa-fast-forward"></i></span>
<span title='Collapse all nodes' class='badge badge-default pointer float-right ct ml-1'><i class="fa fa-compress"></i></span>
<span title='Expand all nodes' class='badge badge-default pointer float-right et'><i class="fa fa-expand"></i></span>
</div>
<div class="m-t-15"><span class="badge badge-pill badge-default">com.lenskart.example.test.CategoryExampleTest</span></div>
<div class="m-t-10 m-l-5">regressionTest</div>
</div>
</div><div class="detail-body mt-4">
<table class="table table-sm">
  <thead><tr><th class="status-col">Status</th><th class="timestamp-col">Timestamp</th><th class="details-col">Details</th></tr></thead>
  <tbody>
      <tr class="event-row">
        <td><span class="badge log pass-bg">Pass</span></td>
        <td>6:07:00 pm</td>
        <td>
          <span class='badge white-text green'>Test Passed</span>
        </td>
      </tr>
  </tbody>
</table>
</div>
          </div>
        </li>
        <li class="test-item"  status="pass" test-id="10"
          author=""
          tag="com.lenskart.example.test.CategoryExampleTest"
          device="">
          <div class="test-detail">
            <p class="name">sanityTest</p>
            <p class="text-sm">
              <span>6:07:00 pm</span> / <span>00:00:01:341</span>
              <span class="badge pass-bg log float-right">Pass</span>
            </p>
          </div>
          <div class="test-contents d-none">
<div class="detail-head">
<div class="p-v-10">
<div class="info">
<h5 class="test-status text-pass">sanityTest</h5>
<span class='badge badge-success'>06.19.2025 6:07:00 pm</span>
<span class='badge badge-danger'>06.19.2025 6:07:01 pm</span>
<span class='badge badge-default'>00:00:01:341</span>
&middot; <span class='uri-anchor badge badge-default'>#test-id=10</span>
<span title='Skip to the next failed step' class='badge badge-danger pointer float-right ne ml-1'><i class="fa fa-fast-forward"></i></span>
<span title='Collapse all nodes' class='badge badge-default pointer float-right ct ml-1'><i class="fa fa-compress"></i></span>
<span title='Expand all nodes' class='badge badge-default pointer float-right et'><i class="fa fa-expand"></i></span>
</div>
<div class="m-t-15"><span class="badge badge-pill badge-default">com.lenskart.example.test.CategoryExampleTest</span></div>
<div class="m-t-10 m-l-5">sanityTest</div>
</div>
</div><div class="detail-body mt-4">
<table class="table table-sm">
  <thead><tr><th class="status-col">Status</th><th class="timestamp-col">Timestamp</th><th class="details-col">Details</th></tr></thead>
  <tbody>
      <tr class="event-row">
        <td><span class="badge log pass-bg">Pass</span></td>
        <td>6:07:01 pm</td>
        <td>
          <span class='badge white-text green'>Test Passed</span>
        </td>
      </tr>
  </tbody>
</table>
</div>
          </div>
        </li>
        <li class="test-item"  status="pass" test-id="11"
          author=""
          tag="com.lenskart.example.test.CategoryExampleTest"
          device="">
          <div class="test-detail">
            <p class="name">sanityTest</p>
            <p class="text-sm">
              <span>6:07:01 pm</span> / <span>00:00:01:851</span>
              <span class="badge pass-bg log float-right">Pass</span>
            </p>
          </div>
          <div class="test-contents d-none">
<div class="detail-head">
<div class="p-v-10">
<div class="info">
<h5 class="test-status text-pass">sanityTest</h5>
<span class='badge badge-success'>06.19.2025 6:07:01 pm</span>
<span class='badge badge-danger'>06.19.2025 6:07:03 pm</span>
<span class='badge badge-default'>00:00:01:851</span>
&middot; <span class='uri-anchor badge badge-default'>#test-id=11</span>
<span title='Skip to the next failed step' class='badge badge-danger pointer float-right ne ml-1'><i class="fa fa-fast-forward"></i></span>
<span title='Collapse all nodes' class='badge badge-default pointer float-right ct ml-1'><i class="fa fa-compress"></i></span>
<span title='Expand all nodes' class='badge badge-default pointer float-right et'><i class="fa fa-expand"></i></span>
</div>
<div class="m-t-15"><span class="badge badge-pill badge-default">com.lenskart.example.test.CategoryExampleTest</span></div>
<div class="m-t-10 m-l-5">sanityTest</div>
</div>
</div><div class="detail-body mt-4">
<table class="table table-sm">
  <thead><tr><th class="status-col">Status</th><th class="timestamp-col">Timestamp</th><th class="details-col">Details</th></tr></thead>
  <tbody>
      <tr class="event-row">
        <td><span class="badge log pass-bg">Pass</span></td>
        <td>6:07:03 pm</td>
        <td>
          <span class='badge white-text green'>Test Passed</span>
        </td>
      </tr>
  </tbody>
</table>
</div>
          </div>
        </li>
        <li class="test-item"  status="pass" test-id="12"
          author=""
          tag="com.lenskart.example.test.CategoryExampleTest"
          device="">
          <div class="test-detail">
            <p class="name">sanityTest</p>
            <p class="text-sm">
              <span>6:07:03 pm</span> / <span>00:00:01:209</span>
              <span class="badge pass-bg log float-right">Pass</span>
            </p>
          </div>
          <div class="test-contents d-none">
<div class="detail-head">
<div class="p-v-10">
<div class="info">
<h5 class="test-status text-pass">sanityTest</h5>
<span class='badge badge-success'>06.19.2025 6:07:03 pm</span>
<span class='badge badge-danger'>06.19.2025 6:07:04 pm</span>
<span class='badge badge-default'>00:00:01:209</span>
&middot; <span class='uri-anchor badge badge-default'>#test-id=12</span>
<span title='Skip to the next failed step' class='badge badge-danger pointer float-right ne ml-1'><i class="fa fa-fast-forward"></i></span>
<span title='Collapse all nodes' class='badge badge-default pointer float-right ct ml-1'><i class="fa fa-compress"></i></span>
<span title='Expand all nodes' class='badge badge-default pointer float-right et'><i class="fa fa-expand"></i></span>
</div>
<div class="m-t-15"><span class="badge badge-pill badge-default">com.lenskart.example.test.CategoryExampleTest</span></div>
<div class="m-t-10 m-l-5">sanityTest</div>
</div>
</div><div class="detail-body mt-4">
<table class="table table-sm">
  <thead><tr><th class="status-col">Status</th><th class="timestamp-col">Timestamp</th><th class="details-col">Details</th></tr></thead>
  <tbody>
      <tr class="event-row">
        <td><span class="badge log pass-bg">Pass</span></td>
        <td>6:07:04 pm</td>
        <td>
          <span class='badge white-text green'>Test Passed</span>
        </td>
      </tr>
  </tbody>
</table>
</div>
          </div>
        </li>
      </ul>
    </div>
  </div>
<div class="test-content scrollable">
<div class="test-content-tools">
<ul><li><a class="back-to-test" href="#"><i class="fa fa-arrow-left"></i></a></li></ul>
</div>
<div class="test-content-detail"><div class="detail-body"></div></div>
</div></div>
<div class="test-wrapper row view category-view attributes-view">
<div class="test-list">
<div class="test-list-tools">
<ul class="tools pull-left"><li><a href=""><span class="font-size-14">Category</span></a></li></ul>
<ul class="tools text-right"><li><a href="#"><span class="badge badge-primary">2</span></a></li></ul>
</div>
<div class="test-list-wrapper scrollable">
<ul class="test-list-item">
<li class="test-item">
<div class="test-detail">
<span class="meta">
<span class='badge log pass-bg'>9</span>
</span>
<p class="name">com.lenskart.example.test.CategoryExampleTest</p>
<p class="duration text-sm">9 tests</p>
</div>
<div class="test-contents d-none">
<div class="info">
<h4>com.lenskart.example.test.CategoryExampleTest</h4>
<span status="pass" class='badge log pass-bg'>9 passed</span>
</div>
<table class='table table-sm mt-4'>
<thead>
<tr>
<th class="status-col">Status</th>
<th class="timestamp-col">Timestamp</th>
<th>TestName</th>
</tr>
</thead>
<tbody>
<tr class="tag-test-status" status="pass">
<td><span class="badge log pass-bg">Pass</span></td>
<td>18:06:49 pm</td>
<td>
<a href="#" class="linked" test-id='4' id='4'>e2eTest</a>
</td>
</tr>
<tr class="tag-test-status" status="pass">
<td><span class="badge log pass-bg">Pass</span></td>
<td>18:06:51 pm</td>
<td>
<a href="#" class="linked" test-id='5' id='5'>e2eTest</a>
</td>
</tr>
<tr class="tag-test-status" status="pass">
<td><span class="badge log pass-bg">Pass</span></td>
<td>18:06:52 pm</td>
<td>
<a href="#" class="linked" test-id='6' id='6'>e2eTest</a>
</td>
</tr>
<tr class="tag-test-status" status="pass">
<td><span class="badge log pass-bg">Pass</span></td>
<td>18:06:54 pm</td>
<td>
<a href="#" class="linked" test-id='7' id='7'>regressionTest</a>
</td>
</tr>
<tr class="tag-test-status" status="pass">
<td><span class="badge log pass-bg">Pass</span></td>
<td>18:06:55 pm</td>
<td>
<a href="#" class="linked" test-id='8' id='8'>regressionTest</a>
</td>
</tr>
<tr class="tag-test-status" status="pass">
<td><span class="badge log pass-bg">Pass</span></td>
<td>18:06:56 pm</td>
<td>
<a href="#" class="linked" test-id='9' id='9'>regressionTest</a>
</td>
</tr>
<tr class="tag-test-status" status="pass">
<td><span class="badge log pass-bg">Pass</span></td>
<td>18:07:00 pm</td>
<td>
<a href="#" class="linked" test-id='10' id='10'>sanityTest</a>
</td>
</tr>
<tr class="tag-test-status" status="pass">
<td><span class="badge log pass-bg">Pass</span></td>
<td>18:07:01 pm</td>
<td>
<a href="#" class="linked" test-id='11' id='11'>sanityTest</a>
</td>
</tr>
<tr class="tag-test-status" status="pass">
<td><span class="badge log pass-bg">Pass</span></td>
<td>18:07:03 pm</td>
<td>
<a href="#" class="linked" test-id='12' id='12'>sanityTest</a>
</td>
</tr>
</tbody>
</table>
</div>
</li>
<li class="test-item">
<div class="test-detail">
<span class="meta">
<span class='badge log pass-bg'>3</span>
</span>
<p class="name">com.lenskart.example.test.GetDiscoveryTest</p>
<p class="duration text-sm">3 tests</p>
</div>
<div class="test-contents d-none">
<div class="info">
<h4>com.lenskart.example.test.GetDiscoveryTest</h4>
<span status="pass" class='badge log pass-bg'>3 passed</span>
</div>
<table class='table table-sm mt-4'>
<thead>
<tr>
<th class="status-col">Status</th>
<th class="timestamp-col">Timestamp</th>
<th>TestName</th>
</tr>
</thead>
<tbody>
<tr class="tag-test-status" status="pass">
<td><span class="badge log pass-bg">Pass</span></td>
<td>18:06:43 pm</td>
<td>
<a href="#" class="linked" test-id='1' id='1'>getDiscoveryTest</a>
</td>
</tr>
<tr class="tag-test-status" status="pass">
<td><span class="badge log pass-bg">Pass</span></td>
<td>18:06:45 pm</td>
<td>
<a href="#" class="linked" test-id='2' id='2'>getDiscoveryTest</a>
</td>
</tr>
<tr class="tag-test-status" status="pass">
<td><span class="badge log pass-bg">Pass</span></td>
<td>18:06:47 pm</td>
<td>
<a href="#" class="linked" test-id='3' id='3'>getDiscoveryTest</a>
</td>
</tr>
</tbody>
</table>
</div>
</li>
</ul>
</div>
</div>
<div class="test-content scrollable">
<div class="test-content-detail">
<div class="detail-body"></div>
</div>
</div>
</div><div class="container-fluid p-4 view dashboard-view">
<div class="row">
<div class="col-md-3">
<div class="card"><div class="card-body">
<p class="m-b-0">Started</p>
<h3>2025-06-19 18:06:43</h3>
</div></div>
</div>
<div class="col-md-3">
<div class="card"><div class="card-body">
<p class="m-b-0">Ended</p>
<h3>2025-06-19 18:06:43</h3>
</div></div>
</div>
<div class="col-md-3">
<div class="card"><div class="card-body">
<p class="m-b-0 text-pass">Tests Passed</p>
<h3>12</h3>
</div></div>
</div>
<div class="col-md-3">
<div class="card"><div class="card-body">
<p class="m-b-0 text-fail">Tests Failed</p>
<h3>0</h3>
</div></div>
</div>
</div>
<div class="row">
<div class="col-md-6">
<div class="card">
<div class="card-header">
<h6 class="card-title">Tests</h6>
</div>
<div class="card-body">
<div class="">
<canvas id='parent-analysis' width='115' height='90'></canvas>
</div>
</div>
<div class="card-footer">
<div><small data-tooltip='100%'>
<b>12</b> tests passed
</small>
</div>
<div>
<small data-tooltip='0%'><b>0</b> tests failed,
<b>0</b> skipped, <b data-tooltip='0%'>0</b> others
</small>
</div>
</div>
</div>
</div>
<div class="col-md-6">
<div class="card">
<div class="card-header">
<h6 class="card-title">Log events</h6>
</div>
<div class="card-body">
<div class="">
<canvas id='events-analysis' width='115' height='90'></canvas>
</div>
</div>
<div class="card-footer">
<div><small data-tooltip='100%'><b>12</b> events passed</small></div>
<div>
<small data-tooltip='0%'><b>0</b> events failed,
<b data-tooltip='%'>0</b> others
</small>
</div>
</div>
</div>
</div>
</div>
<div class="row"><div class="col-md-12">
<div class="card"><div class="card-header"><p>Timeline</p></div>
<div class="card-body pt-0"><div>
<canvas id="timeline" height="120"></canvas>
</div></div>
</div>
</div></div>
<script>
var timeline = {
"getDiscoveryTest":2.105,"getDiscoveryTest":1.775,"getDiscoveryTest":1.25,"e2eTest":2.748,"e2eTest":1.16,"e2eTest":1.34,"regressionTest":1.453,"regressionTest":1.183,"regressionTest":3.218,"sanityTest":1.341,"sanityTest":1.851,"sanityTest":1.209
};
</script>
<div class="row">
<div class="col-lg-6 col-md-12 category-container">
<div class="card">
<div class="card-header"><p>Tags</p></div>
<div class="card-body pb-0 pt-0"><table class="table table-sm table-bordered">
<thead><tr class="bg-gray"><th>Name</th><th>Passed</th><th>Failed</th><th>Skipped</th><th>Others</th><th>Passed %</th></tr></thead><tbody>
<tr>
<td>com.lenskart.example.test.CategoryExampleTest</td>
<td>9</td>
<td>0</td>
<td>0</td>
<td>0</td>
<td>100%</td>
</tr>
<tr>
<td>com.lenskart.example.test.GetDiscoveryTest</td>
<td>3</td>
<td>0</td>
<td>0</td>
<td>0</td>
<td>100%</td>
</tr>
</tbody>
</table></div>
</div>
</div>
<div class="col-lg-6 col-md-12 sysenv-container">
<div class="card">
<div class="card-header"><p>System/Environment</p></div>
<div class="card-body pb-0 pt-0"><table class="table table-sm table-bordered">
<thead><tr class="bg-gray"><th>Name</th><th>Value</th></tr></thead>
<tbody>
<tr>
<td>OS</td>
<td>Mac OS X</td>
</tr>
<tr>
<td>Java Version</td>
<td>23.0.2</td>
</tr>
<tr>
<td>User</td>
<td>amit</td>
</tr>
<tr>
<td>Organization</td>
<td>Lenskart</td>
</tr>
<tr>
<td>Timezone</td>
<td>IST</td>
</tr>
</tbody>
</table></div>
</div>
</div>
</div>
</div>
<script>
var statusGroup = {
parentCount: 5,
passParent: 12,
failParent: 0,
warningParent: 0,
skipParent: 0,
childCount: 5,
passChild: 0,
failChild: 0,
warningChild: 0,
skipChild: 0,
infoChild: 0,
grandChildCount: 5,
passGrandChild: 0,
failGrandChild: 0,
warningGrandChild: 0,
skipGrandChild: 0,
infoGrandChild: 0,
eventsCount: 5,
passEvents: 12,
failEvents: 0,
warningEvents: 0,
skipEvents: 0,
infoEvents: 0
};
</script>        </div>
      </div>
    </div>
  </div>
<script src="https://cdn.jsdelivr.net/gh/extent-framework/extent-github-cdn@ce8b10435bcbae260c334c0d0c6b61d2c19b6168/spark/js/spark-script.js"></script>
<script type="text/javascript"></script></body>
</html>
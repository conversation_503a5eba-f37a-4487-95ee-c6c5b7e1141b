<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.lenskart</groupId>
        <artifactId>be-automation</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>example</artifactId>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <commons.version>1.0.0</commons.version>
        <lombok.version>1.18.30</lombok.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>commons</artifactId>
            <version>${commons.version}</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <!-- Repository configuration for internal dependencies -->
    <repositories>
        <repository>
            <id>central</id>
            <name>Maven Central Repository</name>
            <url>https://repo.maven.apache.org/maven2</url>
            <layout>default</layout>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>

        <repository>
            <id>archiva.internal</id>
            <name>Lenskart Internal Repository</name>
            <url>http://archiva-new.prod.internal:8080/repository/internal</url>
            <layout>default</layout>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>daily</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>daily</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </snapshots>
        </repository>

        <repository>
            <id>archiva.nexs.releases</id>
            <name>Lenskart NEXS Releases Repository</name>
            <url>http://archiva-new.prod.internal:8080/repository/internal/nexs-releases</url>
            <layout>default</layout>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>daily</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>

    <!-- Plugin repository configuration for internal plugins -->
    <pluginRepositories>
        <pluginRepository>
            <id>central</id>
            <name>Maven Central Plugin Repository</name>
            <url>https://repo.maven.apache.org/maven2</url>
            <layout>default</layout>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
            <releases>
                <updatePolicy>never</updatePolicy>
            </releases>
        </pluginRepository>

        <pluginRepository>
            <id>archiva.internal.plugins</id>
            <name>Lenskart Internal Plugin Repository</name>
            <url>http://archiva-new.prod.internal:8080/repository/internal</url>
            <layout>default</layout>
            <releases>
                <enabled>true</enabled>
                <updatePolicy>daily</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </releases>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>daily</updatePolicy>
                <checksumPolicy>warn</checksumPolicy>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

    <!-- Distribution management for deploying artifacts -->
    <distributionManagement>
        <repository>
            <id>archiva.internal.releases</id>
            <name>Lenskart Internal Releases</name>
            <url>http://archiva-new.prod.internal:8080/repository/internal</url>
        </repository>
        <snapshotRepository>
            <id>archiva.internal.snapshots</id>
            <name>Lenskart Internal Snapshots</name>
            <url>http://archiva-new.prod.internal:8080/repository/internal</url>
        </snapshotRepository>
    </distributionManagement>

    <build>
        <plugins>
            <!-- Lombok Annotation Processor -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.13.0</version>
                <configuration>
                    <release>21</release>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
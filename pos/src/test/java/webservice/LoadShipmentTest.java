package webservice;

import com.lenskart.commons.model.Countries;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.pos.helpers.AuthenticationHelper;
import com.lenskart.pos.helpers.LoadShipmentStatusHelper;
import com.lenskart.pos.model.POS;
import org.testng.annotations.Test;
import com.lenskart.commons.model.Client;

public class LoadShipmentTest {
    @Test
    public void loadShipment() {
        OrderContext orderContext = OrderContext.builder()
                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                        .country(Countries.IN).build())
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN).build())
                .headers(OrderContext.Headers.builder().client(Client.DESKTOP).build())
                .build();
        AuthenticationHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        LoadShipmentStatusHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

    }
}


package webservice;

import com.lenskart.commons.model.Client;
import com.lenskart.commons.model.Countries;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.pos.helpers.*;
import com.lenskart.pos.model.POS;
import com.lenskart.pos.model.PosContext;
import org.testng.annotations.Test;

public class FiservDeviceDetailsTest {
    @Test
    public void GetFiservDeviceDetails() {
        OrderContext orderContext = OrderContext.builder()
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.SG)
                        .build())
                .isPosOrder(true)
                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                        .country(Countries.SG)
                        .storeId(POS.SG_PREPROD_STORE.getStoreId())
                        .build())
                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                .build();

        PosContext posContext = PosContext.builder()
                .fiservDeviceInsertMapper(PosContext.FiservDeviceInsertMapper.builder()
                        .colorCode("#808000")
                        .deviceColor("Olive")
                        .serialNumber("99459017")
                        .build())
                .fiservDeviceUpdateMapper(PosContext.FiservDeviceUpdateMapper.builder()
                        .colorCode("#FFA500")
                        .deviceColor("Orange")
                        .serialNumber("99459017")
                        .build())
                .build();

        AuthenticationHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        GetFiservDeviceDetailsHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        InsertFiservDeviceDetailsHelper.builder()
                .orderContext(orderContext)
                .posContext(posContext)
                .build()
                .test();

        UpdateFiservDeviceDetailsHelper.builder()
                .orderContext(orderContext)
                .posContext(posContext)
                .build()
                .test();

        DeleteFiservDeviceDetailsHelper.builder()
                .orderContext(orderContext)
                .posContext(posContext)
                .build()
                .test();
    }
}
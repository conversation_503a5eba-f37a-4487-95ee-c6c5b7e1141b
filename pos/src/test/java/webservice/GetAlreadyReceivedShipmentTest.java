package webservice;

import com.lenskart.commons.model.Client;
import com.lenskart.commons.model.Countries;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.pos.helpers.AuthenticationHelper;
import com.lenskart.pos.model.POS;
import org.testng.annotations.Test;
import com.lenskart.pos.helpers.GetAlreadyReceivedShipmentsHelper;

public class GetAlreadyReceivedShipmentTest {
    @Test
    public void GetAlreadyShipment() {
        OrderContext orderContext = OrderContext.builder()
                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                        .country(Countries.IN).build())
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN).build())
                .headers(OrderContext.Headers.builder().client(Client.POS_Web).build())
                .build();
        AuthenticationHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        GetAlreadyReceivedShipmentsHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

    }
}


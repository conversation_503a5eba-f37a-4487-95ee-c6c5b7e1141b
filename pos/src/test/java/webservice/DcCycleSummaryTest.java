package webservice;

import com.lenskart.commons.model.Client;
import com.lenskart.commons.model.Countries;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.pos.helpers.AuthenticationHelper;
import com.lenskart.pos.helpers.GetDcCycleSummaryHelper;
import com.lenskart.pos.helpers.GetSalesmanListHelper;
import com.lenskart.pos.model.POS;
import org.testng.annotations.Test;

public class DcCycleSummaryTest {
@Test
    public void DcCycleSummary() {
        OrderContext orderContext = OrderContext.builder()
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN)
                        .build())
                .isPosOrder(true)
                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                        .country(Countries.IN)
                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                        .franchiseId(106)
                        .build())
                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                .build();

        AuthenticationHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        GetDcCycleSummaryHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }
}

package com.lenskart.pos.config;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Central registry for Pos configurations
 */
@Slf4j
public class PosConfigRegistry {

    // Singleton instance
    private static PosConfigRegistry instance;

    // Configuration caches
    private final Map<String, String> baseUrls = new ConcurrentHashMap<>();
    private final Map<String, PosConfig.CountryPosConfig> countryConfigs = new ConcurrentHashMap<>();

    // Constants
    private static final String DEFAULT_ENVIRONMENT = "preprod";
    private static final String BASE_URLS_SECTION = "baseUrls";

    // Private constructor for singleton
    private PosConfigRegistry() {
        // Initialize configurations
        loadConfigurations();
    }

    /**
     * Get singleton instance
     *
     * @return PosConfigRegistry instance
     */
    public static synchronized PosConfigRegistry getInstance() {
        if (instance == null) {
            instance = new PosConfigRegistry();
        }
        return instance;
    }

    /**
     * Load all configurations
     */
    private void loadConfigurations() {
        loadBaseUrls();
        loadCountryConfigurations();
    }

    /**
     * Load base URLs for services
     */
    @SuppressWarnings("unchecked")
    private void loadBaseUrls() {
        try {
            // Load the Pos configuration
            PosConfig config = PosConfigLoader.loadConfig();

            // Get the environment configuration
            PosConfig.EnvironmentConfig envConfig = config.getEnvironment(DEFAULT_ENVIRONMENT);

            if (envConfig == null) {
                log.warn("Environment not found: {}", DEFAULT_ENVIRONMENT);
                return;
            }

            // Get the base URLs
            Map<String, String> urls = envConfig.getBaseUrls();

            if (urls == null || urls.isEmpty()) {
                log.warn("No base URLs found in environment: {}", DEFAULT_ENVIRONMENT);
                return;
            }

            // Store the base URLs
            baseUrls.putAll(urls);

            log.info("Loaded {} base URLs", baseUrls.size());
        } catch (Exception e) {
            log.error("Failed to load base URLs: {}", e.getMessage(), e);
        }
    }

    /**
     * Load country configurations
     */
    private void loadCountryConfigurations() {
        try {
            // Load the POS configuration
            PosConfig config = PosConfigLoader.loadConfig();

            // Get the environment configuration
            PosConfig.EnvironmentConfig envConfig = config.getEnvironment(DEFAULT_ENVIRONMENT);

            if (envConfig == null) {
                log.warn("Environment not found: {}", DEFAULT_ENVIRONMENT);
                return;
            }

            // Get the country configurations
            Map<String, PosConfig.CountryPosConfig> countries = envConfig.getAllCountryConfigs();

            if (countries == null || countries.isEmpty()) {
                log.warn("No country configurations found in environment: {}", DEFAULT_ENVIRONMENT);
                return;
            }

            // Store the country configurations
            countryConfigs.putAll(countries);

            log.info("Loaded {} country configurations", countryConfigs.size());
        } catch (Exception e) {
            log.error("Failed to load country configurations: {}", e.getMessage(), e);
        }
    }

    /**
     * Get base URL for a service
     *
     * @param serviceName Service name
     * @return Base URL
     */
    public String getBaseUrl(String serviceName) {
        return baseUrls.get(serviceName);
    }

    /**
     * Get all base URLs
     *
     * @return Map of base URLs
     */
    public Map<String, String> getAllBaseUrls() {
        return new HashMap<>(baseUrls);
    }

    /**
     * Get country configuration by country code
     *
     * @param countryCode Country code (e.g., "IN", "SG", "US")
     * @return Country POS configuration
     */
    public PosConfig.CountryPosConfig getCountryConfig(String countryCode) {
        return countryConfigs.get(countryCode);
    }

    /**
     * Get all country configurations
     *
     * @return Map of all country configurations
     */
    public Map<String, PosConfig.CountryPosConfig> getAllCountryConfigs() {
        return new HashMap<>(countryConfigs);
    }

    /**
     * Get POS user by country and username
     *
     * @param countryCode Country code
     * @param username Username
     * @return POS user if found, null otherwise
     */
    public PosConfig.PosUser getPosUser(String countryCode, String username) {
        PosConfig.CountryPosConfig countryConfig = getCountryConfig(countryCode);
        if (countryConfig == null) {
            return null;
        }
        return countryConfig.getPosUser(username);
    }

    /**
     * Get POS user by country and store ID
     *
     * @param countryCode Country code
     * @param storeId Store ID
     * @return POS user if found, null otherwise
     */
    public PosConfig.PosUser getPosUserByStoreId(String countryCode, String storeId) {
        log.info("country code {} and storeId is {}", countryCode, storeId );
        PosConfig.CountryPosConfig countryConfig = getCountryConfig(countryCode);
        log.info("countryConfig {}", countryConfig );
        if (countryConfig == null) {
            return null;
        }
        return countryConfig.getPosUserByStoreId(storeId);
    }

    /**
     * Get all active POS users for a country
     *
     * @param countryCode Country code
     * @return List of active POS users
     */
    public List<PosConfig.PosUser> getActivePosUsers(String countryCode) {
        PosConfig.CountryPosConfig countryConfig = getCountryConfig(countryCode);
        if (countryConfig == null) {
            return List.of();
        }
        return countryConfig.getActivePosUsers();
    }

    /**
     * Refresh all configurations
     */
    public synchronized void refresh() {
        // Clear caches
        baseUrls.clear();
        countryConfigs.clear();

        // Clear the config loader cache
        PosConfigLoader.clearCache();

        // Reload configurations
        loadConfigurations();

        log.info("All configurations refreshed");
    }
}

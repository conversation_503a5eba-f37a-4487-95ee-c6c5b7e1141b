package com.lenskart.pos.endpoints;

import com.lenskart.commons.endpoints.EndpointManager;
import com.lenskart.pos.config.PosConfigProvider;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * Endpoint manager for POS module that provides URL generation
 * and management for all POS endpoints.
 */
@Slf4j
public class PosEndpointManager extends EndpointManager<PosEndpoints> {
    
    // Singleton instance
    private static volatile PosEndpointManager instance;
    
    /**
     * Private constructor for singleton pattern
     */
    private PosEndpointManager() {
        super(PosConfigProvider.getInstance(), PosEndpoints.class);
        log.info("PosEndpointManager initialized");
    }
    
    /**
     * Gets the singleton instance of PosEndpointManager
     *
     * @return The singleton instance
     */
    public static PosEndpointManager getInstance() {
        if (instance == null) {
            synchronized (PosEndpointManager.class) {
                if (instance == null) {
                    instance = new PosEndpointManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * Convenience method to get URL for a POS endpoint
     *
     * @param endpoint The POS endpoint
     * @return Complete URL for the endpoint
     */
    public static String getUrl(PosEndpoints endpoint) {
        return getInstance().getUrl(endpoint);
    }
    
    /**
     * Convenience method to get URL for a POS endpoint with path parameters
     *
     * @param endpoint The POS endpoint
     * @param pathParams Map of path parameters to replace in the URL
     * @return Complete URL with path parameters replaced
     */
    public static String getUrl(PosEndpoints endpoint, Map<String, String> pathParams) {
        return getInstance().getUrl(endpoint, pathParams);
    }
    
    /**
     * Convenience method to refresh all POS endpoint URLs
     */
    public static void refresh() {
        getInstance().refresh();
    }
    
    /**
     * Convenience method to validate all POS endpoints
     *
     * @return true if all endpoints are valid, false otherwise
     */
    public static boolean validate() {
        return getInstance().validateEndpoints();
    }
    
    /**
     * Convenience method to get all POS endpoint URLs
     *
     * @return Map of endpoint names to their full URLs
     */
    public static Map<String, String> getAllUrls() {
        return getInstance().getAllUrls();
    }
}

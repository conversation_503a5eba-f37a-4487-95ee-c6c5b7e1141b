package com.lenskart.pos.endpoints;

import com.lenskart.pos.config.PosConfigRegistry;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum PosEndpoints {

    V1_AUTH("/v1/auth", "sessionService"),
    V2_SESSIONS("/v2/sessions", "junoService"),

    GET_PRODUCT_DETAILS("/v2/products/{$productID}", "webService"),
    GET_PACKAGE_DETAILS("/v1/products/product/{$productID}/packages", "webService"),
    DELETE_ITEMS_FROM_CART("/v1/carts/items/{itemId}/quantity/remove", "webService"),
    ADD_TO_CART("/v1/carts", "webService"),
    PLACE_ORDER("/v1/order", "webService");

    private final String endpoint;
    private final String serviceName;

    // Static maps to store base URLs and full URLs
    private static final Map<String, String> serviceBaseUrls = new HashMap<>();
    private static final Map<PosEndpoints, String> fullUrls = new HashMap<>();

    // Flag to track if initialization has been done
    private static boolean initialized = false;

    // Static initialization block to load all base URLs once
    static {
        initializeUrls();
    }

    PosEndpoints(String endpoint, String serviceName) {
        this.endpoint = endpoint;
        this.serviceName = serviceName;
    }

    /**
     * Initializes all URLs by loading the configuration once
     */
    private static synchronized void initializeUrls() {
        if (initialized) {
            return;
        }

        // Pre-load all service base URLs
        for (PosEndpoints endpoint : values()) {
            if (!serviceBaseUrls.containsKey(endpoint.serviceName)) {
                // Get the base URL from JunoConfigRegistry
                String baseUrl = PosConfigRegistry.getInstance().getBaseUrl(endpoint.serviceName);

                if (baseUrl == null) {
                    throw new IllegalStateException("Base URL not found for service: " + endpoint.serviceName);
                }

                // Remove trailing slash from base URL if present
                if (baseUrl.endsWith("/")) {
                    baseUrl = baseUrl.substring(0, baseUrl.length() - 1);
                }

                serviceBaseUrls.put(endpoint.serviceName, baseUrl);
            }
        }

        // Pre-compute all full URLs
        for (PosEndpoints endpoint : values()) {
            String baseUrl = serviceBaseUrls.get(endpoint.serviceName);

            // Ensure endpoint starts with a slash
            String path = endpoint.endpoint;
            if (!path.startsWith("/")) {
                path = "/" + path;
            }

            // Store the full URL
            fullUrls.put(endpoint, baseUrl + path);
        }

        initialized = true;
    }

    /**
     * Gets the complete URL for this endpoint
     *
     * @return Complete URL for the endpoint
     */
    public String getUrl() {
        // Ensure URLs are initialized
        if (!initialized) {
            initializeUrls();
        }

        // Return the pre-computed URL
        return fullUrls.get(this);
    }

    /**
     * Gets the complete URL for this endpoint with path parameters replaced
     *
     * @param pathParams Map of path parameters to replace in the URL
     * @return Complete URL with path parameters replaced
     */
    public String getUrl(Map<String, String> pathParams) {
        String url = getUrl();

        if (pathParams != null) {
            for (Map.Entry<String, String> entry : pathParams.entrySet()) {
                url = url.replace("{$" + entry.getKey() + "}", entry.getValue());
            }
        }

        return url;
    }

    /**
     * Refreshes the base URLs from the configuration
     * Call this method if the configuration has changed
     */
    public static void refreshUrls() {
        // Refresh the JunoConfigRegistry
        PosConfigRegistry.getInstance().refresh();

        // Clear our caches
        serviceBaseUrls.clear();
        fullUrls.clear();
        initialized = false;

        // Re-initialize
        initializeUrls();
    }


}

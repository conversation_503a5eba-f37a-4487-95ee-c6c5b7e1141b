package com.lenskart.pos.endpoints;

import com.lenskart.commons.endpoints.BaseEndpoint;
import lombok.Getter;

import java.util.Map;

@Getter
public enum PosEndpoints implements BaseEndpoint {

    V1_AUTH("/v1/auth", "posService"),
    V2_SESSIONS("/v2/sessions", "junoService"),

    GET_PRODUCT_DETAILS("/v2/products/{$productID}", "webService"),
    GET_PACKAGE_DETAILS("/v1/products/{$productID}/packages", "webService"),
    DELETE_ITEMS_FROM_CART("/v1/carts/items/{itemId}/quantity/remove", "webService"),
    ADD_TO_CART("/v1/carts", "webService"),
    PLACE_ORDER("/v1/order", "webService"),
    GET_CUSTOMER_DETAILS("/v1/customers/{$phoneNumber}", "webService"),
    GET_BARCODE_DETAILS("/v1/products/{$productID}/barcodes", "webService"),
    VALIDATE_BARCODE_DETAILS("/v1/products/{$productID}/validate-barcode/{$barcodeID}", "webService"),
    FETCH_PRESCRIPTION("v1/prescription/customer", "webService"),
    GET_DELIVERY_ESTIMATES("/v1/carts/item/{$cartID}/estimate", "webService");

    private final String endpoint;
    private final String serviceName;

    PosEndpoints(String endpoint, String serviceName) {
        this.endpoint = endpoint;
        this.serviceName = serviceName;
    }

    @Override
    public String getUrl() {
        return PosEndpointManager.getEndpointUrl(this);
    }

    @Override
    public String getUrl(Map<String, String> pathParams) {
        return PosEndpointManager.getEndpointUrl(this, pathParams);
    }

}

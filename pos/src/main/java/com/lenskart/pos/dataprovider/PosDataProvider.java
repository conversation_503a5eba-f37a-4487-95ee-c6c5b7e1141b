package com.lenskart.pos.dataprovider;

import com.lenskart.commons.model.Client;
import com.lenskart.commons.model.Countries;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.model.PaymentMethod;
import com.lenskart.commons.utils.DateUtils;
import org.testng.annotations.DataProvider;

import java.time.format.DateTimeFormatter;
import java.time.LocalDate;
import java.util.Calendar;
import java.util.Date;

public class PosDataProvider {

    @DataProvider(name = "marginContext")
    public Object[][] marginContext() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("570024")
                                        .build())
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .startDate(DateUtils.formatDateToString(DateUtils.getFirstDayOfCurrentMonth()))
                                        .endDate(DateUtils.formatDateToString(DateUtils.getCurrentDayOfCurrentMonth()))
                                        .storeId("35")
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build()

                }
        };
    }
}


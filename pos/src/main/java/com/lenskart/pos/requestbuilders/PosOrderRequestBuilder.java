package com.lenskart.pos.requestbuilders;

import com.lenskart.commons.model.ItemType;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.juno.schema.v2.cart.Customer;
import com.lenskart.pos.config.PosConfig;
import com.lenskart.pos.model.AddToCartRequest;
import com.lenskart.pos.model.CartResponse;
import com.lenskart.pos.model.OrderRequest;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.*;

import static com.lenskart.commons.model.ProductTypes.CONTACT_LENS;


public class PosOrderRequestBuilder {

    public static JSONObject createPayloadForSession(PosConfig.PosUser user) {
        JSONObject payload = new JSONObject();
        payload.put("userName", user.getUsername());
        payload.put("password", user.getPassword());
        return payload;
    }


    public static AddToCartRequest createPayloadForAddCart(OrderContext.ProductList product) {

        AddToCartRequest request = AddToCartRequest.builder()
                .posMisc(Map.of("jitRuleIdNew", "66e960898a14043075a8bbfb", "isAddedViaDeepLink", false))
                .quantity(product.getQuantity())
                .customer(new JSONObject())
                .productId(Integer.parseInt(product.getProductId()))
                .barCode(product.getBarCode())
                .showLiquidationDiscount(false)
                .addOns("")
                .additionalProducts(new ArrayList<>())
                .build();
        if (product.getProductType().equals(CONTACT_LENS)) {

            JSONObject left = new JSONObject();
            left.put("sph", "-0.50");
            left.put("boxes", "1");

            JSONObject right = new JSONObject();
            right.put("sph", "-0.50");
            right.put("boxes", "1");

            JSONObject prescription = new JSONObject();
            prescription.put("left", left);
            prescription.put("right", right);
            prescription.put("recordedAt", 0);

            request.setPrescription(prescription);
            request.setStoreInventory(0);

            return request;
        } else {
            request.setPackageId(product.getPackageId());
            request.setPowerType(product.getPowerType().getDisplayName());
            request.setComboSwitcherProducts(new ArrayList<>());
            return request;
        }
    }


    public static List<Map<String, Object>> createItemsPayload(CartResponse cartResponse) {
        List<Map<String, Object>> items = new ArrayList<>();

        if (cartResponse.getItems() != null && !cartResponse.getItems().isEmpty()) {
            for (Object itemObj : cartResponse.getItems()) {
                // Convert Object to Map for easier access
                Map<String, Object> item = (Map<String, Object>) itemObj;

                // Extract required fields
                int quantity = (int) item.getOrDefault("quantity", 1);
                String productId = String.valueOf(item.get("productId"));
                String productTypeValue = (String) item.getOrDefault("classification", "eyeframe");

                // Extract prescription type from cart response
                String prescriptionType = null;
                boolean isPowerRequired = true;

                // First check if prescription type exists in the prescription object
                if (item.containsKey("prescription") && item.get("prescription") != null) {
                    Map<String, Object> prescriptionData = (Map<String, Object>) item.get("prescription");
                    if (prescriptionData.containsKey("type")) {
                        prescriptionType = (String) prescriptionData.get("type");

                        // Check if it's zero_power
                        if ("zero_power".equalsIgnoreCase(prescriptionType)) {
                            isPowerRequired = false;
                        }
                    }
                }

                // Check if powerRequired is explicitly set
                if (item.containsKey("powerRequired")) {
                    Object powerRequiredObj = item.get("powerRequired");
                    if (powerRequiredObj instanceof Boolean) {
                        isPowerRequired = (Boolean) powerRequiredObj;
                    } else if (powerRequiredObj instanceof Number) {
                        isPowerRequired = ((Number) powerRequiredObj).intValue() == 1;
                    } else if (powerRequiredObj instanceof String) {
                        isPowerRequired = "1".equals(powerRequiredObj) ||
                                "true".equalsIgnoreCase((String) powerRequiredObj) ||
                                "POWER_REQUIRED".equalsIgnoreCase((String) powerRequiredObj);
                    }
                }

                // Extract options data
                List<Map<String, Object>> options = new ArrayList<>();
                if (item.containsKey("options") && item.get("options") != null) {
                    List<Object> optionsList = (List<Object>) item.get("options");
                    for (Object optObj : optionsList) {
                        Map<String, Object> option = (Map<String, Object>) optObj;
                        String oid = (String) option.get("oid");
                        String type = (String) option.get("type");

                        // If prescription type is not set yet, get it from options
                        if (prescriptionType == null) {
                            prescriptionType = type;

                            // Check if it's zero_power
                            if ("zero_power".equalsIgnoreCase(prescriptionType)) {
                                isPowerRequired = false;
                            }
                        }

                        options.add(Map.of(
                                "oid", oid,
                                "type", type
                        ));
                    }
                }

                // If prescription type is still null, try to determine from product type
                if (prescriptionType == null) {
                    // Try to determine based on product type and power required flag
                    if (!isPowerRequired) {
                        prescriptionType = "zero_power";
                    } else if (productTypeValue.equalsIgnoreCase("eyeframe")) {
                        prescriptionType = "single_vision";
                    } else if (productTypeValue.equalsIgnoreCase("sunglasses")) {
                        prescriptionType = "sunglasses";
                    } else {
                        // Last resort fallback
                        prescriptionType = "single_vision";
                    }
                }

                // Create prescription data
                Map<String, Object> prescription = new HashMap<>();
                prescription.put("type", prescriptionType);
                prescription.put("category", productTypeValue);

                // Create the item map
                Map<String, Object> itemMap = new HashMap<>();
                itemMap.put("deliveryOption", "LOCAL_FITTING");
                itemMap.put("quantity", quantity);
                itemMap.put("productTypeValue", productTypeValue);
                itemMap.put("productId", productId);
                itemMap.put("options", options);
                itemMap.put("prescription", prescription);
                itemMap.put("powerRequired", isPowerRequired ? 1 : 0);

                items.add(itemMap);
            }
        }

        return items;
    }

    public static OrderRequest createPayloadForOrderPayment(OrderContext orderContext, CartResponse cartResponse) {
        List<Map<String, Object>> itemsList = createItemsPayload(cartResponse);

        // Calculate delivery date (current date + 2 days in milliseconds)
        long deliveryDateMillis = System.currentTimeMillis() + (2 * 24 * 60 * 60 * 1000);

        return OrderRequest.builder()
                .items(Collections.unmodifiableList(itemsList))
                .deliveryOption("STANDARD")
                .isNewLensOnlyFlow(false)
                .loggedInUserId(106)
                .powerSkipped(0)
                .frameSelector(Map.of(
                    "id", "1519756",
                    "name", "Automation User"
                ))
                .communication(Map.of(
                    "phoneCode", orderContext.getCountryCodeMapper().getCountry().getDialingCode(),
                    "mobileNumber", orderContext.getCountryCodeMapper().getCountry().getDefaultPhoneNumber(),
                    "firstName", "Automation User",
                    "lastName", "User"
                ))
                .orderBeforePayment(false)
                .customerComment("")
                .deliveryDate(deliveryDateMillis)
                .shipToCust(0)
                .deliveryTat("Estimated Delivery Date : " + new SimpleDateFormat("dd-MMM-yyyy").format(new Date(deliveryDateMillis)))
                .cartId(Integer.parseInt(String.valueOf(orderContext.getCartId())))
                .customer(Map.of(
                    "firstName", "Automation",
                        "phoneCode", orderContext.getCountryCodeMapper().getCountry().getDialingCode(),
                    "email", "<EMAIL>",
                    "lastName", "User",
                    "gender", "male",
                    "telephone", orderContext.getCountryCodeMapper().getCountry().getDefaultPhoneNumber()
                ))
                .paymentDetails(Map.of(
                    "authCodePo", "",
                    "prepaidAmount", orderContext.getFinalOrderAmount(),
                    "paymentMethod", "offlinecash",
                    "referenceId", "" // Added referenceId
                ))
                .build();
    }


    public static JSONObject createPayloadForDeliveryEstimate(OrderContext orderContext) {
        JSONObject estimatePayload = new JSONObject();
        estimatePayload.put("trueLastPiece", false);
        estimatePayload.put("lastPiece", true);
        estimatePayload.put("lensOnly", false);
        estimatePayload.put("shippingCountry", orderContext.getCountryCodeMapper().getCountry());
        estimatePayload.put("deliveryOptions", Arrays.asList("LOCAL_FITTING", "STANDARD"));
        estimatePayload.put("pincode", orderContext.getCountryCodeMapper().getPinCode());
        
        return estimatePayload;
    }
}

package com.lenskart.pos.requestbuilders;

import com.lenskart.commons.model.ItemType;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.juno.schema.v2.cart.Customer;
import com.lenskart.pos.config.PosConfig;
import com.lenskart.pos.model.AddToCartRequest;
import com.lenskart.pos.model.OrderRequest;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Date;
import java.util.Map;

import static com.lenskart.commons.model.ProductTypes.CONTACT_LENS;


public class PosOrderRequestBuilder {

    public static JSONObject createPayloadForSession(PosConfig.PosUser user) {
        JSONObject payload = new JSONObject();
        payload.put("userName", user.getUsername());
        payload.put("password", user.getPassword());
        return payload;
    }


    public static AddToCartRequest createPayloadForAddCart(OrderContext.ProductList product) {

        AddToCartRequest request = AddToCartRequest.builder()
                .posMisc(Map.of("jitRuleIdNew", "66e960898a14043075a8bbfb", "isAddedViaDeepLink", false))
                .quantity(product.getQuantity())
                .customer(new JSONObject())
                .productId(Integer.parseInt(product.getProductId()))
                .showLiquidationDiscount(false)
                .addOns("")
                .additionalProducts(new ArrayList<>())
                .build();
        if (product.getProductType().equals(CONTACT_LENS)) {

            JSONObject left = new JSONObject();
            left.put("sph", "-0.50");
            left.put("boxes", "1");

            JSONObject right = new JSONObject();
            right.put("sph", "-0.50");
            right.put("boxes", "1");

            JSONObject prescription = new JSONObject();
            prescription.put("left", left);
            prescription.put("right", right);
            prescription.put("recordedAt", 0);

            request.setPrescription(prescription);
            request.setStoreInventory(0);

            return request;
        } else {
            request.setPackageId(product.getPackageId());
            request.setPowerType(product.getPowerType().getDisplayName());
            request.setComboSwitcherProducts(new ArrayList<>());
            return request;
        }
    }

    public static OrderRequest createPayloadForOrderPayment(OrderContext orderContext) {
        return OrderRequest.builder()
                .deliveryOption("STANDARD")
                .isNewLensOnlyFlow(false)
                .loggedInUserId(126) // is this required?
                .powerSkipped(1)
                .frameSelector(Map.of("id", "1519756", "name", "Automation User"))
                .communication(Map.of("phoneCode", orderContext.getCountryCodeMapper().getCountry().getDialingCode(),
                        "mobileNumber", orderContext.getCountryCodeMapper().getCountry().getDefaultPhoneNumber(), // what number goes here?
                        "firstName", "Automation User"))
                .orderBeforePayment(false)
                .customerComment(null)
                .deliveryDate(new Date()) // is this required new Date + 2 days
                .shipToCust(true)
                .deliveryTat("Estimated Delivery Date : Fri, 06 June 2025")
                .cartId((int) orderContext.getCartId())
                .customer(Map.of("firstName", "Automation",
                        "phoneCode", orderContext.getCountryCodeMapper().getCountry().getDialingCode(),
                        "email", "<EMAIL>",
                        "lastName", "User",
                        "gender", "male",
                        "telephone", orderContext.getCountryCodeMapper().getCountry().getDefaultPhoneNumber())) // what number goes here?
                .paymentDetails(Map.of("authCodePo", "",
                        "prepaidAmount", orderContext.getFinalOrderAmount(),
                        "paymentMethod", "offlinecash"))
                .build();
    }
}

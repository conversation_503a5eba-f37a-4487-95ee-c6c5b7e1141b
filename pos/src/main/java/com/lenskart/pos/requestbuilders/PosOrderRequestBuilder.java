package com.lenskart.pos.requestbuilders;

import com.lenskart.commons.model.OrderContext;
import com.lenskart.juno.schema.v2.cart.Customer;
import com.lenskart.pos.config.PosConfig;
import com.lenskart.pos.model.AddToCartRequest;
import com.lenskart.pos.model.OrderRequest;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Date;
import java.util.Map;


public class PosOrderRequestBuilder {

    public static JSONObject createPayloadForSession(PosConfig.PosUser user) {
        JSONObject payload = new JSONObject();
        payload.put("userName", user.getUsername());
        payload.put("password", user.getPassword());
        return payload;
    }


    public static AddToCartRequest createPayloadForAddCart(OrderContext.ProductList product) {

        return AddToCartRequest.builder()
                .comboSwitcherProducts(new ArrayList<>())
                .customer(new JSONObject())
                .packageId(product.getPackageId())
                .showLiquidationDiscount(true)
                .powerType(product.getPowerType().getDisplayName())
                .posMisc(Map.of("jitRuleIdNew", "66e960898a14043075a8bbfb", "isAddedViaDeepLink", false))
                .productId(Integer.parseInt(product.getProductId()))
                .quantity(1)
                .addOns("")
                .additionalProducts(new ArrayList<>())
                .build();

    }

    public static OrderRequest createPayloadForOrderPayment(OrderContext orderContext) {
        return OrderRequest.builder()
                .deliveryOption("STANDARD")
                .isNewLensOnlyFlow(false)
                .loggedInUserId(126) // is this required?
                .powerSkipped(1)
                .frameSelector(Map.of("id", "1519756", "name", "Automation User"))
                .communication(Map.of("phoneCode", orderContext.getCountryCodeMapper().getCountry().getDialingCode() ,
                        "mobileNumber", orderContext.getCountryCodeMapper().getCountry().getDefaultPhoneNumber(), // what number goes here?
                        "firstName", "Automation User"))
                .orderBeforePayment(false)
                .customerComment(null)
                .deliveryDate(new Date()) // is this required?
                .shipToCust(false)
                .deliveryTat("Estimated Delivery Date : Fri, 30 May 2025")
                .cartId((int) orderContext.getCartId())
                .customer(Map.of("firstName", "Automation",
                        "phoneCode", orderContext.getCountryCodeMapper().getCountry().getDialingCode(),
                        "email", "<EMAIL>",
                        "lastName", "User",
                        "gender", "male",
                        "telephone", orderContext.getCountryCodeMapper().getCountry().getDefaultPhoneNumber())) // what number goes here?
                .paymentDetails(Map.of("authCodePo", "",
                        "prepaidAmount", orderContext.getFinalOrderAmount(),  // can this amount be more then the cart amount?
                        "paymentMethod", "offlinecash"))
                .build();
    }
}

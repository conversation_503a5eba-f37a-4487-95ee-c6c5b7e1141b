package com.lenskart.pos.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import groovy.util.logging.Slf4j;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import java.util.Map;

import static com.lenskart.commons.constants.Constants.CART_ID;
import static com.lenskart.pos.endpoints.PosEndpoints.GET_DELIVERY_ESTIMATES;

@SuperBuilder
@Slf4j
public class DeliveryEstimateHelper extends PosBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    Response response;


    @Override
    public ServiceHelper init() {
        statusCode = 200;
        headers = getHeadersWithSessionToken(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(GET_DELIVERY_ESTIMATES.getUrl(Map.of(CART_ID, String.valueOf(orderContext.getCartId()))),
                headers, null, 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

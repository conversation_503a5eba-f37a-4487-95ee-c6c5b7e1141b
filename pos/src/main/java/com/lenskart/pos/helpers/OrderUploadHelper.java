package com.lenskart.pos.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import static com.lenskart.pos.endpoints.PosEndpoints.BULK_ORDER_UPLOAD;


@SuperBuilder
@Slf4j
@Getter
public class OrderUploadHelper extends PosBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    Response response;
    String filePath;

    @Override
    public ServiceHelper init() {
        headers = getHeadersWithPosSessionToken(orderContext);
        // Set the file path to the CSV file
        if (filePath == null) {
            filePath = getClass().getClassLoader().getResource("bulk_order_upload1.csv").getPath();
            log.info("Using default CSV file path: {}", filePath);
        }
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.postWithMultipartFile(
                BULK_ORDER_UPLOAD.getUrl(), headers, filePath, "file", 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

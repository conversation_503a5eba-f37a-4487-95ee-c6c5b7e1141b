package com.lenskart.pos.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.model.PosContext;
import com.lenskart.pos.requestbuilders.FiservDeviceRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import static com.lenskart.pos.endpoints.PosEndpoints.INSERT_FISERV_DEVICE_DETAILS;

@SuperBuilder
public class InsertFiservDeviceDetailsHelper extends PosBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    String insertPayload;
    PosContext posContext;
    Response response;

    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext);
        insertPayload = FiservDeviceRequestBuilder.fiservDeviceInsertPayload(posContext.getFiservDeviceInsertMapper());
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(INSERT_FISERV_DEVICE_DETAILS.getUrl(),headers,insertPayload,200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

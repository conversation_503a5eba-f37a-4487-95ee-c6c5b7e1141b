package com.lenskart.pos.helpers.order;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.juno.helpers.ClearCartHelper;
import com.lenskart.pos.helpers.*;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

@SuperBuilder
public class OrderHelper extends PosBaseHelper implements ExecutionHelper {


    OrderContext orderContext;
    JSONObject payload;
    Response response;


    @Override
    public ExecutionHelper init() {
        return this;
    }

    @Override
    public ExecutionHelper orchestrateFlow() {

        /* Authenticate User */
        AuthenticationHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        /* Search for product and get package id */
        GetProductAndPackageHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        /* Clear Cart from juno module */
        ClearCartHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        /* Create Cart */
        CreateCartHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        /* Create Order Payment */
        CreateOrderPaymentHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        return this;
    }

    @Override
    public ExecutionHelper test() {
        init();
        orchestrateFlow();
        return this;
    }
}

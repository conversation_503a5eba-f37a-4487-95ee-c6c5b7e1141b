package com.lenskart.pos.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.model.AddToCartRequest;
import com.lenskart.pos.model.CartResponse;
import com.lenskart.pos.requestbuilders.PosOrderRequestBuilder;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import static com.lenskart.pos.endpoints.PosEndpoints.ADD_TO_CART;

@SuperBuilder
@Slf4j
@Getter
public class CreateCartHelper extends PosBaseHelper implements ServiceHelper {

    AddToCartRequest payload;
    OrderContext orderContext;
    Response response;
    CartResponse cartResponse;


    @Override
    public ServiceHelper init() {
        statusCode = 200;
        headers = getHeadersWithSessionToken(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        if (orderContext.getProductLists().isEmpty()) {
            throw new RuntimeException("No products added to the order context");
        } else {
            List<OrderContext.ProductList> productLists = orderContext.getProductLists();
            for (OrderContext.ProductList productList : productLists) {
                payload = PosOrderRequestBuilder.createPayloadForAddCart(productList);
                response = RestUtils.post(ADD_TO_CART.getUrl(), headers, JsonUtils.convertObjectToJsonString(payload), 200);
                orderContext.setCartId( (int) RestUtils.getValueFromResponse(response, "cartId"));
                cartResponse = parseResponse(response.asPrettyString(), CartResponse.class);
                orderContext.setFinalOrderAmount(cartResponse.getFinalTotal().get(1).getValue());
            }
        }
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

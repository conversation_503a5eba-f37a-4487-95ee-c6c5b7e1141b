package com.lenskart.pos.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.model.CartResponse;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import static com.lenskart.pos.endpoints.PosEndpoints.GET_CART;

@SuperBuilder
@Slf4j
@Getter
public class GetCartHelper extends PosBaseHelper implements ServiceHelper {

    private OrderContext orderContext;
    private Response response;
    private CartResponse cartResponse;

    @Override
    public ServiceHelper init() {
        statusCode = 200; // Initialize statusCode to 200
        headers = getHeadersWithSessionToken(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(GET_CART.getUrl(), headers, null, 200);
        cartResponse = parseResponse(response.asPrettyString(), CartResponse.class);
        orderContext.setFinalOrderAmount(cartResponse.getFinalTotal().get(1).getValue());
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
package com.lenskart.pos.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.requestbuilders.PosOrderRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import java.util.stream.Collectors;
import static com.lenskart.pos.endpoints.PosEndpoints.NAV_COMMISSION;

@Slf4j
@SuperBuilder
public class ProcessStoreCommissionPayoutHelper extends PosBaseHelper implements ServiceHelper{
    String queryString;
    JSONObject payload;
    private OrderContext orderContext;
    Response response;


    @Override
    public ServiceHelper init() {
        headers = getHeaders(orderContext);
        queryParams = getQueryParamsForStoreId(orderContext);
        payload = PosOrderRequestBuilder.createPayloadForStoreId(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
    response = RestUtils.put(NAV_COMMISSION.getUrl()+ toQueryString(queryParams), headers, null, 200);
    log.info(JsonUtils.convertObjectToJsonString(response.asPrettyString()));
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

}

package com.lenskart.pos.helpers;

import com.lenskart.commons.base.BaseHelper;
import com.lenskart.commons.constants.HeaderMapper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.model.PowerTypes;
import com.lenskart.pos.exceptions.PosExcetionStates;
import lombok.experimental.SuperBuilder;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@SuperBuilder
public class PosBaseHelper extends BaseHelper<PosExcetionStates, Object> {

    public Map<String, String> getHeaders(OrderContext orderContext) {
        headers = new HashMap<>();
        headers.put(HeaderMapper.X_COUNTRY_CODE.getHeaderName(), orderContext.getCountryCodeMapper().getCountry().name());
        headers.put(HeaderMapper.X_API_CLIENT.getHeaderName(), orderContext.getHeaders().getClient().getDisplayName());
        headers.put(HeaderMapper.X_LENSKART_APP_ID.getHeaderName(), "connect");
        headers.put(HeaderMapper.X_LENSKART_API_KEY.getHeaderName(), "valyoo123");
        return headers;
    }

    public Map<String, String> getHeadersWithSessionToken(OrderContext orderContext) {
        headers = getHeaders(orderContext);
        headers.put(HeaderMapper.X_LENSKART_SESSION_TOKEN.getHeaderName(), orderContext.getHeaders().getPosSessionToken());
        headers.put(HeaderMapper.X_SESSION_TOKEN.getHeaderName(), orderContext.getHeaders().getSessionToken());
        return headers;
    }

    public Map<String, Object> getQueryParams(String frameType, PowerTypes powerType) {
        queryParams = new HashMap<>();
        queryParams.put("frame_type", frameType);
        queryParams.put("power_type", Objects.isNull(powerType) ? PowerTypes.SINGLE_VISION.getDisplayName() : powerType.getDisplayName());
        return queryParams;
    }

    public Map<String, Object> getQueryParams(OrderContext orderContext) {
        queryParams = new HashMap<>();
        queryParams.put("phoneCode", orderContext.getCountryCodeMapper().getCountry().getDialingCode());
        return queryParams;
    }

    public Map<String, Object> getQueryParamsForPhoneNumber(OrderContext orderContext) {
        queryParams = getQueryParams(orderContext);
        queryParams.put("customerPhone", orderContext.getPhoneNumber());
        return queryParams;
    }
}

package com.lenskart.pos.helpers;

import com.lenskart.commons.base.BaseHelper;
import com.lenskart.commons.constants.HeaderMapper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.model.PowerTypes;
import com.lenskart.commons.utils.DateUtils;
import com.lenskart.pos.dataprovider.PosDataProvider;
import com.lenskart.pos.exceptions.PosExcetionStates;
import com.lenskart.pos.model.PosContext;
import lombok.experimental.SuperBuilder;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@SuperBuilder
public class PosBaseHelper extends BaseHelper<PosExcetionStates, Object> {

    public Map<String, String> getHeaders(OrderContext orderContext) {
        headers = new HashMap<>();
        headers.put(HeaderMapper.X_COUNTRY_CODE.getHeaderName(), orderContext.getCountryCodeMapper().getCountry().name());
        headers.put(HeaderMapper.X_API_CLIENT.getHeaderName(), orderContext.getHeaders().getClient().getDisplayName());
        headers.put(HeaderMapper.X_LENSKART_APP_ID.getHeaderName(), "connect");
        headers.put(HeaderMapper.X_LENSKART_API_KEY.getHeaderName(), "valyoo123");
        return headers;
    }

    public Map<String, String> getHeadersWithSessionToken(OrderContext orderContext) {
        headers = getHeaders(orderContext);
        headers.put(HeaderMapper.X_LENSKART_SESSION_TOKEN.getHeaderName(), orderContext.getHeaders().getPosSessionToken());
        headers.put(HeaderMapper.X_SESSION_TOKEN.getHeaderName(), orderContext.getHeaders().getSessionToken());
        return headers;
    }

    public Map<String, String> getHeadersWithSession(OrderContext orderContext) {
        headers = new HashMap<>();
        headers.put(HeaderMapper.X_LENSKART_SESSION_TOKEN.getHeaderName(), orderContext.getHeaders().getPosSessionToken());
        headers.put(HeaderMapper.X_LENSKART_APP_ID.getHeaderName(), "connect");
        headers.put(HeaderMapper.X_LENSKART_API_KEY.getHeaderName(), "valyoo123");
        return headers;
    }

    public Map<String, String> getHeadersWithApiClient(OrderContext orderContext) {
        headers = getHeaders(orderContext);
        headers.put(HeaderMapper.X_LENSKART_SESSION_TOKEN.getHeaderName(), orderContext.getHeaders().getPosSessionToken());
        headers.put(HeaderMapper.X_SESSION_TOKEN.getHeaderName(), orderContext.getHeaders().getSessionToken());
        headers.put(HeaderMapper.X_API_CLIENT.getHeaderName(), orderContext.getHeaders().getClient().getDisplayName());
        return headers;
    }
    public Map<String, Object> getQueryParams(String frameType, PowerTypes powerType) {
        queryParams = new HashMap<>();
        queryParams.put("frame_type", frameType);
        queryParams.put("power_type", Objects.isNull(powerType) ? PowerTypes.SINGLE_VISION.getDisplayName() : powerType.getDisplayName());
        return queryParams;
    }

    public Map<String, Object> getQueryParams(OrderContext orderContext) {
        queryParams = new HashMap<>();
        queryParams.put("phoneCode", orderContext.getCountryCodeMapper().getCountry().getDialingCode());
        return queryParams;
    }

    public Map<String, Object> getQueryParamsForPhoneNumber(OrderContext orderContext) {
        queryParams = getQueryParams(orderContext);
        queryParams.put("customerPhone", orderContext.getPhoneNumber());
        return queryParams;
    }

    public Map<String, Object> getQueryParamsWithBarCode(OrderContext orderContext, String barcode) {
        queryParams = new HashMap<>();
        queryParams.put("productId", orderContext.getProductLists().get(0).getProductId());
        queryParams.put("barcode", barcode);
        return queryParams;
    }

    public Map<String, Object> getQueryParamsForMonthlyMargin(OrderContext orderContext) {
        queryParams = new HashMap<>();
        queryParams.put("start_date", DateUtils.formatDateToString(LocalDate.parse(orderContext.getPosStoreMapper().getStartDate())));
        queryParams.put("end_date", DateUtils.formatDateToString(LocalDate.parse(orderContext.getPosStoreMapper().getEndDate())));
        queryParams.put("store_ids", orderContext.getPosStoreMapper().getStoreId());
        return queryParams;
    }

    public Map<String, Object> getQueryParamsForStoreId(OrderContext orderContext) {
        queryParams = new HashMap<>();
        queryParams.put("store_id", orderContext.getPosStoreMapper().getStoreId());
        return queryParams;
    }


    public String toQueryString(Map<String, Object> params) {
        if (params == null || params.isEmpty()) {
            return "";
        }

        StringBuilder queryString = new StringBuilder("?");
        for (Map.Entry<String, Object> entry : params.entrySet()) {
            queryString.append(entry.getKey())
                    .append("=")
                    .append(entry.getValue())
                    .append("&");
        }

        // Remove the last "&"
        queryString.setLength(queryString.length() - 1);

        return queryString.toString();
    }


    public Map<String, Object> getQueryParamsForOrderSummaryList(String fromDate, String toDate, int page, int size, int franchiseId) {
        queryParams = new HashMap<>();
        queryParams.put("fromDate", fromDate);
        queryParams.put("toDate", toDate);
        queryParams.put("page", page);
        queryParams.put("size", size);
        queryParams.put("franchiseId", franchiseId);
        return queryParams;
    }

    public Map<String, Object> getQueryParamsForExchangeList(OrderContext orderContext, int size) {
        queryParams = new HashMap<>();
        queryParams.put("query", orderContext.getOrderId());
        queryParams.put("Page", size);
        return queryParams;
    }

    public Map<String, Object> getQueryParamsForCustomerProfile(OrderContext orderContext) {
        queryParams = new HashMap<>();
        queryParams.put("getPrescriptionCount", orderContext.isPrescriptionCount());
        queryParams.put("phone" , orderContext.getPhoneNumber());
        queryParams.put("phoneCode", orderContext.getCountryCodeMapper().getCountry().getDialingCode());
        return queryParams;
    }

    public  Map<String, Object> getQueryParamsForDeleteFiservDeviceDetails(String deviceSerialNumber) {
        queryParams = new HashMap<>();
        queryParams.put("deviceSerialNumber", deviceSerialNumber);
        return queryParams;
    }

    public Map<String, Object> getQueryParamsWithUserId(int user_Id , int page_Size, int pages, String facility) {
        queryParams = new HashMap<>();
        queryParams.put("userId", user_Id);
        queryParams.put("pageSize", page_Size);
        queryParams.put("page", pages);
        queryParams.put("query", facility);
        return queryParams;
    }

    public Map<String, Object> getQueryParamsWithUserId1(int page_Size, int pages) {
        queryParams = new HashMap<>();
        queryParams.put("pageSize", page_Size);
        queryParams.put("page", pages);
        return queryParams;
    }
}


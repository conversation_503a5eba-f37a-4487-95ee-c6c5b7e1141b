package com.lenskart.pos.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.constants.Constants;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import java.util.IdentityHashMap;
import java.util.Map;

import static com.lenskart.pos.endpoints.PosEndpoints.SALESMEN_DETAILS;

@SuperBuilder
public class GetSalesmanDetailsHelper extends PosBaseHelper implements ServiceHelper {
    Response response;
    OrderContext orderContext;

    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(SALESMEN_DETAILS.getUrl(Map.of("salesmenId", String.valueOf(orderContext.getPosStoreMapper().getSalesmenId()))), headers, null, 200);
        return this;
    }


    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

package com.lenskart.pos.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.model.PosContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import static com.lenskart.pos.endpoints.PosEndpoints.GET_ORDERSUMMARY_ITEMS_LIST;

@SuperBuilder
public class OrdersummaryListHelper extends PosBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    PosContext posContext;
    Response response;


    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext);
        String fromDate = posContext.getOrderSummaryMapper().getFromDate();
        String toDate = posContext.getOrderSummaryMapper().getToDate();
        int size = posContext.getOrderSummaryMapper().getSize();
        int page = posContext.getOrderSummaryMapper().getPage();
        int franchiseId = posContext.getOrderSummaryMapper().getFranchiseId();

        queryParams = getQueryParamsForOrderSummaryList(fromDate,toDate,page, size, franchiseId);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(GET_ORDERSUMMARY_ITEMS_LIST.getUrl(),headers,queryParams,200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

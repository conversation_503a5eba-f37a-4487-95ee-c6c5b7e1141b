package com.lenskart.pos.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.model.PosContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import java.util.Map;

import static com.lenskart.pos.endpoints.PosEndpoints.GET_ORDER_DETAILS;

@SuperBuilder
public class OrderDetailsHelper extends PosBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    Response response;
    PosContext posContext;
    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(GET_ORDER_DETAILS.getUrl(Map.of("incrementId",String.valueOf(posContext.getIncrementId()))),headers,queryParams,200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

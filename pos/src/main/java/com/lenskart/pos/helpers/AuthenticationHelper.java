package com.lenskart.pos.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.config.PosConfig;
import com.lenskart.pos.config.PosConfigRegistry;
import com.lenskart.pos.requestbuilders.PosOrderRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import static com.lenskart.pos.endpoints.PosEndpoints.V1_AUTH;
import static com.lenskart.pos.endpoints.PosEndpoints.V2_SESSIONS;

@SuperBuilder
@Slf4j
public class AuthenticationHelper extends PosBaseHelper implements ServiceHelper {

    JSONObject payload;
    OrderContext orderContext;
    Response response;
    OrderContext.Headers orderContextHeader;
    PosConfigRegistry registry;



    @Override
    public ServiceHelper init() {
        registry= PosConfigRegistry.getInstance();
        headers = getHeaders(orderContext);
        log.info("Order Context: {}", JsonUtils.convertObjectToJsonString(orderContext));
        PosConfig.PosUser user = registry.getPosUserByStoreId(orderContext.getPosStoreMapper().getCountry().name(),
                orderContext.getPosStoreMapper().getStoreId());
        payload = PosOrderRequestBuilder.createPayloadForSession(user);
        orderContextHeader = orderContext.getHeaders();
        return this;
    }

    @Override
    public ServiceHelper process() {

        /* Fetch session Token */
        response = RestUtils.post(V1_AUTH.getUrl(), headers, payload.toString(), 200);
        orderContextHeader.setPosSessionToken((String) RestUtils.getValueFromResponse(response, "sessionToken"));


        /* Fetch session Token */
        headers = getHeadersWithSessionToken(orderContext);
        response = RestUtils.post(V2_SESSIONS.getUrl(), headers, null, 200);
        orderContextHeader.setSessionToken((String) RestUtils.getValueFromResponse(response, "result.id"));

        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

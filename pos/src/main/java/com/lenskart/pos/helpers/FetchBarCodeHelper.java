package com.lenskart.pos.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import groovy.util.logging.Slf4j;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Map;

import static com.lenskart.commons.constants.Constants.PRODUCT_ID;
import static com.lenskart.pos.endpoints.PosEndpoints.GET_BARCODE_DETAILS;

@SuperBuilder
@Slf4j
public class FetchBarCodeHelper extends PosBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    Response response;


    @Override
    public ServiceHelper init() {
        statusCode = 200;
        headers = getHeadersWithSessionToken(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {

        if (orderContext.getProductLists().isEmpty()) {
            throw new RuntimeException("No products added to the order context");
        } else {
            List<OrderContext.ProductList> productLists = orderContext.getProductLists();
            for (OrderContext.ProductList productList : productLists) {
                response = RestUtils.get(GET_BARCODE_DETAILS.getUrl(Map.of(PRODUCT_ID, productList.getProductId())),
                        headers, null, 200);
                Map<String, List<String>> barcodeDetails = response.as(Map.class);
                if (barcodeDetails.get(productList.getProductId()).isEmpty()) {
                    throw new RuntimeException("No barcodes found for product id: " + orderContext.getProductLists().get(0).getProductId());
                } else {
                    productList.setBarCode(barcodeDetails.get(0).toString());
                }
            }
            return this;
        }
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

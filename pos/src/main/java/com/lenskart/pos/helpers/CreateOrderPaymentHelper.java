package com.lenskart.pos.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.model.CartResponse;
import com.lenskart.pos.model.OrderRequest;
import com.lenskart.pos.requestbuilders.PosOrderRequestBuilder;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import static com.lenskart.pos.endpoints.PosEndpoints.PLACE_ORDER;

@SuperBuilder
@Slf4j
@Getter
@Setter
public class CreateOrderPaymentHelper extends PosBaseHelper implements ServiceHelper {

    OrderRequest payload;
    OrderContext orderContext;
    Response response;
    CreateCartHelper cartHelper;
    CartResponse cartResponse;

    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext);
        // Use provided cartResponse if available, otherwise get it from cartHelper
        CartResponse cartResponseToUse = cartResponse != null ? cartResponse : cartHelper.getCartResponse();
        payload = PosOrderRequestBuilder.createPayloadForOrderPayment(orderContext, cartResponseToUse);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(PLACE_ORDER.getUrl(), headers, JsonUtils.convertObjectToJsonString(payload), 200);
        orderContext.setOrderId((int) RestUtils.getValueFromResponse(response, "order.id"));
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
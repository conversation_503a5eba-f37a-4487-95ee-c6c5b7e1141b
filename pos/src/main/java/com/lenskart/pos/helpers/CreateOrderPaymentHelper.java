package com.lenskart.pos.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.model.OrderRequest;
import com.lenskart.pos.requestbuilders.PosOrderRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import static com.lenskart.pos.endpoints.PosEndpoints.PLACE_ORDER;


@SuperBuilder
@Slf4j
public class CreateOrderPaymentHelper extends PosBaseHelper implements ServiceHelper {

    OrderRequest payload;
    OrderContext orderContext;
    Response response;
    CreateCartHelper cartHelper;


    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext);
        payload = PosOrderRequestBuilder.createPayloadForOrderPayment(orderContext, cartHelper.getCartResponse());
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(PLACE_ORDER.getUrl(), headers, JsonUtils.convertObjectToJsonString(payload), 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

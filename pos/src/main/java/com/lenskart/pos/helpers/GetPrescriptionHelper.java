package com.lenskart.pos.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.pos.model.ItemPrescriptionResponse;
import groovy.util.logging.Slf4j;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;


import static com.lenskart.pos.endpoints.PosEndpoints.FETCH_PRESCRIPTION;

@SuperBuilder
@Slf4j
public class GetPrescriptionHelper extends PosBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    Response response;
    ItemPrescriptionResponse itemPrescriptionResponse;


    @Override
    public ServiceHelper init() {
        statusCode = 200;
        headers = getHeadersWithSessionToken(orderContext);
        queryParams = getQueryParamsForPhoneNumber(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {

        response = RestUtils.get(FETCH_PRESCRIPTION.getUrl(), headers, queryParams, 200);
        itemPrescriptionResponse = parseResponse(response.asPrettyString(), ItemPrescriptionResponse.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

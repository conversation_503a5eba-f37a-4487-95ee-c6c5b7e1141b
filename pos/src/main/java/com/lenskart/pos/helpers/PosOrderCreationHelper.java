package com.lenskart.pos.helpers;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.juno.helpers.ClearCartHelper;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.util.List;


@SuperBuilder
@Slf4j
public class PosOrderCreationHelper extends PosBaseHelper implements ExecutionHelper {

    OrderContext orderContext;
    JSONObject payload;
    Response response;


    /*
     * method to create order
     * call all the apis in sequential order
     * and store intermediate state and headers in
     * the order context
     *  */


    @Override
    public ExecutionHelper init() {
        return this;
    }

    @Override
    public ExecutionHelper orchestrateFlow() {

        /* Authenticate User */
        AuthenticationHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        /* Search for product and get package id */
        GetProductAndPackageHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        /* Clear Cart from juno module */
        ClearCartHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        /* Create Cart */
        CreateCartHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        /* Add Shipping Address */
       // Not required to be added in the v1 order API


        /* Create Prescription - @Ashit to check this */
        List<OrderContext.ProductList> productLists = orderContext.getProductLists();
        for(OrderContext.ProductList productList : productLists){
            if(productList.isPrescriptionRequired()){
                // is this not required?
            }
        }


        /* Create Order Payment */
        CreateOrderPaymentHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();


        return this;
    }

    @Override
    public ExecutionHelper test() {
        init();
        orchestrateFlow();
        return this;
    }


}

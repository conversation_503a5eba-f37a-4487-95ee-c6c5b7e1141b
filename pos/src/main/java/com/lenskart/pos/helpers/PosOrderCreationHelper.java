package com.lenskart.pos.helpers;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.commons.model.ItemType;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.pos.helpers.order.*;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.util.List;


@SuperBuilder
@Slf4j
public class PosOrderCreationHelper extends PosBaseHelper implements ExecutionHelper {

    OrderContext orderContext;
    JSONObject payload;
    Response response;


    @Override
    public ExecutionHelper init() {
        return this;
    }

    @Override
    public ExecutionHelper orchestrateFlow() {

        List<OrderContext.ProductList> productLists = orderContext.getProductLists();
        for (OrderContext.ProductList product : productLists) {

            if (product.getItemType().equals(ItemType.LOCAL_FITTING)) {
                LocalFittingHelper.builder()
                        .orderContext(orderContext)
                        .build()
                        .test();
            } else if (product.getItemType().equals(ItemType.BULK_ORDER)) {
                BulkOrderHelper.builder()
                        .orderContext(orderContext)
                        .build()
                        .test();
            } else if (product.getItemType().equals(ItemType.LAST_PIECE_WAREHOUSE)) {
                LastPieceWareHouseHelper.builder()
                        .orderContext(orderContext)
                        .build()
                        .test();
            } else if (product.getItemType().equals(ItemType.CONTACT_LENS)) {
                ContactLensHelper.builder()
                        .orderContext(orderContext)
                        .build()
                        .test();

            } else {
                OrderHelper.builder()
                        .orderContext(orderContext)
                        .build()
                        .test();
            }
        }
        return this;
    }

    @Override
    public ExecutionHelper test() {
        init();
        orchestrateFlow();
        return this;
    }


}

package com.lenskart.pos.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class PrescriptionData {
    private String id;
    private String powerType;
    private String userName;
    private long recordedAt;
    private EyeData left;
    private EyeData right;
    private RelationshipData relationship;
    private String queueId;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class EyeData {
        private String pd;
        private String sph;
        private String cyl;
        private String axis;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class RelationshipData {
        private String gender;
        private String yearOfBirth;
        private String name;
        private String telephone;
        private String phoneCode;
        private String type;
    }
}
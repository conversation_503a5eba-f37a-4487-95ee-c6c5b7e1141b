package com.lenskart.pos.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.juno.schema.product.BogoPitch;
import com.lenskart.juno.schema.product.HtoServiceabilityDetails;
import com.lenskart.juno.schema.v2.common.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.*;

@Data
@Getter
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class CartResponse {

    private List<Integer> appliedRuleIds;
    private List<String> bogoOffer;
    private int cartId;
    private int cartVersion;
    private String currencyCode;
    private Date deliveryDate;
    private String deliveryOption;
    private List<Object> deliveryOptions;
    private String discountDescription;
    private Date dispatchDate;
    private GiftMessage giftMessage;
    private boolean shipToCustomerEnabled;
    private boolean shipToCustomerLocalFittingEnabled;
    private boolean shipToStoreEnabled;
    private Object shipToCustomerRestriction;
    private List<Object> items;
    private int itemsCount;
    private int itemsQty;
    private String paymentMethod;
    private Object totals;
    private String promotionalMessage;
    private boolean canApplyWalletWithGv;
    private Object offer;
    private HashMap<String, Object> offers;
    private Object offerDetail;
    private boolean bogoApplied;
    private boolean canBogoBeApplied;
    private boolean hasBogoLimitExceeded;
    private String bogoNotAppliedMessage;
    private Object customer;
    private String thumbnail;
    private String productUrl;
    private String junoSessionToken;
    @JsonProperty(defaultValue = "false")
    private boolean studioFlow;
    private AppliedPaymentOfferDetails appliedPaymentOfferDetails;
    @JsonProperty("finalTotal")
    private List<PriceBreakupItem> finalTotal;

    @JsonProperty("increasedPrice")
    private float increasedPrice;

    @JsonProperty("expressDeliveryCharges")
    private float expressDeliveryCharges;

    private List<Object> staticItems;
    private List<Object> upSellItems;

    private List<Object> applicableGvs = new ArrayList<>();


    private Boolean showCodRefundBox = false;

    private String genericGvMessage;
    private Boolean isExpressCheckout;
    private String shipToCustomerChargeLabel;
    private List<Object> comboItems;
    private Boolean isMediBuddyFlow = Boolean.FALSE;
    private Boolean isInsuranceFlow = Boolean.FALSE;
    private Boolean isInsuranceBenefitFlow = Boolean.FALSE;
    private InsuranceBenefitDetails insuranceBenefitDetails;
    private InsuranceFlowDetails insuranceFlowDetails;
    private Object giftCardBalance;
    private Boolean isSbrtEnabled=Boolean.FALSE;
    private Object freebiePitch;
    private String cashbackExclusiveOfferTitle;
    private String cashbackExclusiveOfferSubtitle;
    private Boolean allOtcItems;
    private BigDecimal cashback;
    private List<Object> goldMaxBenefitsList;
    private List<Object> couponPopupInfo = new ArrayList<>();
    private BigDecimal insuranceBenefitDiscount;
    private Boolean isInsuranceBenefitRemoved=Boolean.FALSE;
    private Map<String,String> insuranceBenefitCta;
    private Map<String,String> insuranceBenefitSection;
    private Map<String, Boolean> cartFlags = new HashMap<>();
    private HtoServiceabilityDetails htoServiceabilityDetails;
    private Boolean isSalesOrder;
    private Boolean isSalesGvRequired;
    private Boolean isGlobalExpressPlantFitting;
    private String expressFittingTitle;
    private boolean shipToCustomerRecommendation;
    private String exchangeProductMessage;
    private Boolean isSkipShippingPage = Boolean.FALSE;

    //BUY 2ND PAIR LATER CONTRACT
    private BogoPitch bogoPitch;
    private Boolean isBuyLater;

    private Boolean showMembershipWidgets = Boolean.FALSE;
    private List<Object> membershipWidgets = new ArrayList<>();
    private Integer installmentPrice;
    private Integer tenure;
}

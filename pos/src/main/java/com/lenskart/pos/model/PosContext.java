package com.lenskart.pos.model;
import com.lenskart.commons.model.Countries;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;

@Builder
@Data
public class PosContext {

    private int incrementId;
    private LocalDate startDate;
    private LocalDate endtDate;
    FiservDeviceInsertMapper fiservDeviceInsertMapper;
    FiservDeviceUpdateMapper fiservDeviceUpdateMapper;
    OrderSummaryMapper orderSummaryMapper;

    @Builder
    @Data
    public static class OrderSummaryMapper{
        private String fromDate;
        private String toDate;
        private int page;
        private int size;
        private int franchiseId;
    }


    @Builder
    @Data
    public static class FiservDeviceInsertMapper {
        public String colorCode;
        public String deviceColor;
        public String serialNumber;
    }

    @Builder
    @Data
    public static class FiservDeviceUpdateMapper {
        private String colorCode;
        private String deviceColor;
        private String serialNumber;
    }

}

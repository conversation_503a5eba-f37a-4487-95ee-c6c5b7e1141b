package com.lenskart.pos.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.lenskart.juno.schema.cart.Item;
import com.lenskart.juno.schema.common.Address;
import com.lenskart.juno.schema.v2.common.Profile;
import com.lenskart.juno.schema.v2.customer.Customer;
import com.lenskart.juno.schema.v2.money.GiftVoucher;
import com.lenskart.juno.schema.v2.order.Salesman;
import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

@Builder
@Data
public class OrderRequest {

    // TODO - get rid of these models use the models exposed by the service

    private String customerComment;
    private Object franchise;
    private Salesman salesman;
    private Object optometrist;
    private Object paymentDetails;
    private Object customer;
    private Object discountCoupon;
    private GiftVoucher giftVoucher = new GiftVoucher();
    private List<Object> items = new LinkedList<>();
    private boolean shipToCust;
    private String deliveryTat;
    private Address address;
    private int reorderId;
    private int failedOrderId;
    private int loggedInUserId;
    private int shipToCustomerCriteria;
    private int fullAdvancePaymentCriteria;
    private Object lensonly;
    private Map<String, String> barcodesInfo;
    private Map<String, String> barcodeProductMapping;
    private Object fittingDetails;
    private String localFittingFacility;
    private int powerSkipped;
    private String deliveryOption;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "EEE MMM dd HH:mm:ss zzz yyyy")
    private Date deliveryDate;
    private int cartId;
    private int cartVersion;
    private String junoSessionToken;
    private Object paymentVoucher;
    private String orderId; // Order Id for which exchange has been done
    private String itemId;
    private Long shipToFranchiseId;
    private Boolean isLensOnlyExchange;
    @JsonProperty("isExchangeEnabled")
    private boolean isExchangeEnabled;

    @JsonProperty("communication")
    private Object communication;

    @JsonProperty("isTeleSalesOrder")
    private boolean isTeleSalesOrder;

    private Object paymentPartnerDetails;

    private String countryCode;

    private Boolean isChildPrescriptionAdded;

    private String htoTaskId;

    private Boolean orderBeforePayment;

    private Boolean isNewLensOnlyFlow=Boolean.FALSE;

    private Boolean isAssistedSaleFlow=Boolean.FALSE;

    private String assistedFacilityCode;

    private Object frameSelector;

    private Object lensSelector;

    private String customerTaxId;

    private Profile profile;
}

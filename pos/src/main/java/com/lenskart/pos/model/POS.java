package com.lenskart.pos.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum POS {


    IN_MUMBAI_STORE("STR_MUM_001", "Mumbai Store"),
    IN_FOFO_STORE("STR_FOFO", "Bangalore FOFO Store"),
    IN_ADMIN_STORE("STR_ADMIN", "Delhi Admin Store"),
    SG_CSM_STORE("STR_SG_001", "Central Singapore Mall Store");

    private final String storeId;
    private final String storeName;

}

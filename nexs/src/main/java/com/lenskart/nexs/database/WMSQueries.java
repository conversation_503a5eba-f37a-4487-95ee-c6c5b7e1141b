package com.lenskart.nexs.database;

public class WMSQueries {

    public static final String ITEM_TYPE_FOR_STATUS ="select distinct item_type from wms.order_items where shipping_package_id=? and `item_type` not in ('RIGHTLENS','LEFTLENS','LOYALTY')";
    public static final String ITEM_STATUS ="select distinct `status` from wms.order_items where item_type=? and nexs_order_id=? and shipping_package_id=?";
    public static final  String GET_ORDER_ITEM_STATUS = "select `status` from order_items where `barcode`= ? and `location_id` = ?";
    public static final String MEI_SYNC_DETAILS = "select * from wms.mei_sync where nexs_order_id =? and  fitting_id=?";
    public static final String FITTING_STATUS = "select `fitting_status` from wms.fitting_detail where fitting_id=?";
    public static final String GET_FACILITY_CODE = "select distinct facility_code from wms.order_items where shipping_package_id=?";
    public static final String DISTINCT_STATUS = "select distinct `status` from wms.order_items where shipping_package_id=?";
    public static final String ITEM_STATUS_BY_SHIPPING_ID="select status from wms.order_items where shipping_package_id in (?);";
    public static final String REASSIGNMENT_STATUS_BY_SHIPPING_ID="select * from wms.shipment_reassignment where shipping_package_id in (?);";
    public static final String GET_ORDER_ITEM_HEADER = "select * from order_item_header where `shipping_package_id`=?";
}

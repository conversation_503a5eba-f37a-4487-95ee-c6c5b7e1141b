package com.lenskart.nexs.database;

import com.lenskart.commons.database.mysql.MySQLQueryExecutor;
import com.lenskart.commons.model.Cluster;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.model.NexsOrderContext;
import org.testng.Assert;

import java.util.List;
import java.util.Map;

public class ImsDbUtils {
    public static void getBarcodeItemDetails(NexsOrderContext nexsOrderContext, String barcode, String condition, String availability,
                                             String status) {
        List<Map<String, Object>> barcodeItemDetails = MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                        Constants.IMS_DB,
                        ImsQueries.GET_BARCODE_ITEM_DETAILS,
                        barcode, nexsOrderContext.getLegalOwner());
        Assert.assertEquals(barcodeItemDetails.getFirst().get("condition").toString(), condition);
        Assert.assertEquals(barcodeItemDetails.getFirst().get("availability").toString(), availability);
        Assert.assertEquals(barcodeItemDetails.getFirst().get("status").toString(), status);
    }

    public static List<Map<String, Object>> getGAABarcodes(NexsOrderContext nexsOrderContext, int limit) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                        Constants.IMS_DB,
                        ImsQueries.GET_GAA_BARCODES,
                        nexsOrderContext.getProductId(),nexsOrderContext.getFacilityCode(),nexsOrderContext.getLegalOwner(),limit);
    }
}

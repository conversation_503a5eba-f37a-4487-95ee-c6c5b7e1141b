package com.lenskart.nexs.database;

import com.lenskart.commons.database.mysql.MySQLQueryExecutor;
import com.lenskart.commons.model.MySQLCluster;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.model.NexsOrderContext;

import java.util.List;
import java.util.Map;

public class WMSDbUtils {

    public static String getStatusOfShipment(NexsOrderContext nexsOrderContext) {
        List<Map<String, Object>> itemTypeForStatus = MySQLQueryExecutor
                .executeQuery(MySQLCluster.NEXS_CLUSTER.getClusterName(),
                        Constants.WMS_DB,
                        WMSQueries.ITEM_TYPE_FOR_STATUS,
                        nexsOrderContext.getShippingId());

        List<Map<String, Object>>   itemStatus = MySQLQueryExecutor
                .executeQuery(MySQLCluster.NEXS_CLUSTER.getClusterName(),
                        Constants.WMS_DB,
                        WMSQueries.ITEM_STATUS,
                        itemTypeForStatus.getFirst().get("item_type"),nexsOrderContext.getNexsOrderId(),
                        nexsOrderContext.getShippingId());

            return itemStatus.getFirst().get("status").toString();
    }

    public static String getStausBasedOnBarcode(NexsOrderContext nexsOrderContext, String barcode) {
        List<Map<String, Object>> leftLensStatus = MySQLQueryExecutor
                .executeQuery(MySQLCluster.NEXS_CLUSTER.getClusterName(),
                        Constants.WMS_DB,
                        WMSQueries.GET_ORDER_ITEM_STATUS,
                        barcode, nexsOrderContext.getTrayBarcode());
        return leftLensStatus.getFirst().get("status").toString();
    }
}

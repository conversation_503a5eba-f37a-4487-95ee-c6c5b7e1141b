package com.lenskart.nexs.database;

public class PickingQueries {

    public static String GET_PICKING_STATUS = "select distinct `status` from picking.picklist_order_item where `shipment_id`=?";
    public static String WAVE_ID = "select distinct(picking_summary_id) from picking.picking_detail where shipment_id=?";
    public static String ORDER_ITEM_ID = "select id,product_id from picking.picking_detail where `shipment_id`=?";
    public static String PICKING_DETAILS_STATUS = "select `status` from picking.picking_detail where shipment_id =?";


}

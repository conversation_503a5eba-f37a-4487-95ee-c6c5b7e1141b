package com.lenskart.nexs.database;

import com.lenskart.commons.database.mysql.MySQLQueryExecutor;
import com.lenskart.commons.model.Cluster;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.model.NexsOrderContext;

import java.util.List;
import java.util.Map;

public class PickingDbutils {

    public static List<Map<String, Object>> getpickingDetailsStatus (NexsOrderContext nexsOrderContext) {
        return  MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(),
                        Constants.PICKING_DB,
                        PickingQueries.PICKING_DETAILS_STATUS,
                        nexsOrderContext.getShippingId());
    }
}

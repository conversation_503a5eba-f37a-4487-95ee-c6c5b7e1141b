package com.lenskart.nexs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.NexsRequestBuilder;
import io.restassured.response.Response;
import lombok.SneakyThrows;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.Map;

import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.*;

@SuperBuilder
@Slf4j
public class ManifestHelper extends NexsBaseHelper implements ServiceHelper {
    NexsOrderContext orderContext;
    Response response;
    JSONObject payload;
    String shippingProvider;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(orderContext);
        return this;
    }

    @SneakyThrows
    @Override
    public ServiceHelper process() {

        response = RestUtils.get(MANIFEST_FETCH_CHANNEL.getUrl(), headers, null, 200);
        String channelType = orderContext.getIsShipToStore() ? "STS" : "STC";
        orderContext.setChannelType(channelType);

        queryParams = Map.of("page", "0", "size", "1000");


        response = RestUtils.get(MANIFEST_FETCH_SHIPPING_PROVIDER.getUrl(Map.of("channel", orderContext.getChannelType())),
                headers, queryParams, 200);

        JSONArray shippingProviderArray = new JSONObject(response.asString()).getJSONArray("data");

        shippingProvider = null;
        for (int i = 0; i < shippingProviderArray.length(); i++) {
            if (orderContext.getShippingProviderCode()
                    .equals(shippingProviderArray.getJSONObject(i).getString("shippingProviderCode"))) {
                shippingProvider = shippingProviderArray.getJSONObject(i).getString("shippingProvider");
                break;
            }
        }
        orderContext.setShippingProvider(shippingProvider);
        log.info("Shipping provider code: {}", orderContext.getShippingProviderCode());
        log.info("Shipping provider: {}", orderContext.getShippingProvider());

        payload = NexsRequestBuilder.getSaveManifestPayload(orderContext);
        response = RestUtils.post(MANIFEST_SAVE.getUrl(), headers, payload.toString(), 200);
        orderContext.setManifestId((String) RestUtils.getValueFromResponse(response, "data.manifestId"));
        log.info("Manifest id: {}", orderContext.getManifestId());


        response = RestUtils.get(MANIFEST_FETCH_MANIFEST.getUrl(Map.of("manifestNumber", orderContext.getManifestId())),
                headers, null, 200);
        Thread.sleep(6000);
        response = RestUtils.post(MANIFEST_ADD_SHIPMENT.getUrl(Map.of("manifestNumber", orderContext.getManifestId(),
                "awbNumber", orderContext.getShippingId())), headers, null, 200);
        Thread.sleep(3000);
        response = RestUtils.get(MANIFEST_FETCH_MANIFEST.getUrl(Map.of("manifestNumber", orderContext.getManifestId())),
                headers, null, 200);
        Thread.sleep(3000);
        response = RestUtils.post(MANIFEST_CLOSE.getUrl(Map.of("manifestNumber", orderContext.getManifestId())),
                headers, null, 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

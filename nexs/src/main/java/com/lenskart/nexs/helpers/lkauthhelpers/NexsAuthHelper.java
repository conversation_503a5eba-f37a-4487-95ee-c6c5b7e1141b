package com.lenskart.nexs.helpers.lkauthhelpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.GenericUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.config.NexsConfig;
import com.lenskart.nexs.config.NexsConfigRegistry;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.NexsRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.LKAUTH_LOGIN;

@SuperBuilder
@Slf4j
public class NexsAuthHelper extends NexsBaseHelper implements ServiceHelper {

    JSONObject payload;
    NexsOrderContext nexsOrderContext;
    Response response;
    NexsOrderContext.Headers nexsOrderContextHeader;

    @Override
    public ServiceHelper init() {
        headers = getloginHeaders(nexsOrderContext);
        // Get the machine's serial number
        String serialNumber = GenericUtils.getSerialNumber();
        log.info("Using machine serial number for credentials: {}", serialNumber);

        // Get credentials from config based on serial number
        NexsConfig.SerialNoConfig credentials = NexsConfigRegistry.getInstance().getCredentials(serialNumber);

        if (credentials != null) {
            log.info("Found credentials for serial number: {}", serialNumber);
        } else {
            log.warn("No credentials found for serial number: {}", serialNumber);
        }

        // Use credentials or fall back to defaults if not found
        String username = credentials != null ? credentials.getUsername() : "LSP01355";
        String password = credentials != null ? credentials.getPassword() : "Sandeep@1234";
        log.info("Using username: {}", username);

        payload = NexsRequestBuilder.getLoginPayload(nexsOrderContext, username, password);
        nexsOrderContextHeader = nexsOrderContext.getHeaders();
        return this;
    }

    @Override
    public ServiceHelper process() {
        // Call authentication API
        response = RestUtils.post(LKAUTH_LOGIN.getUrl(), headers, payload.toString(), 200);
        nexsOrderContextHeader.setJwtToken((String) RestUtils.getValueFromResponse(response, "content"));
        nexsOrderContextHeader.setDateTime(LocalDateTime.now().atZone(ZoneId.of("Asia/Kolkata"))
                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        nexsOrderContextHeader.setWorkstationId("test-workstation");
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

}

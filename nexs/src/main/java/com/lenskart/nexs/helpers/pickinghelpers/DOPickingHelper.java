package com.lenskart.nexs.helpers.pickinghelpers;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.helpers.lkauthhelpers.NexsAuthHelper;
import com.lenskart.nexs.helpers.lkauthhelpers.NexsLogoutHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

@SuperBuilder
@Slf4j
public class DOPickingHelper extends NexsBaseHelper implements ExecutionHelper {
    NexsOrderContext nexsOrderContext;

    @Override
    public ExecutionHelper init() {
        return this;
    }

    @Override
    public ExecutionHelper orchestrateFlow() {
        /* Authenticate User */
        NexsAuthHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        CreatePickingSummaryDOHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

         GetPickingSummaryDOHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        PickBarcodeForDOHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        /* Logout User */
        NexsLogoutHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        return this;
    }

    @Override
    public ExecutionHelper test() {
        init();
        orchestrateFlow();
        return this;
    }
}

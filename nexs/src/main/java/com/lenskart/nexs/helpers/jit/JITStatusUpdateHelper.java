package com.lenskart.nexs.helpers.jit;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.model.jit.JITStatusUpdateModel;
import com.lenskart.nexs.requestBuilder.jit.JitRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;


import static com.lenskart.nexs.endpoints.NexsEndpoints.LENSLAB_STATUS_UPDATE;

@SuperBuilder
public class JITStatusUpdateHelper extends NexsBaseHelper implements ServiceHelper {

    private NexsOrderContext nexsOrderContext;
    private JITStatusUpdateModel jitStatusUpdateModel;
    private Response response;
    private JSONObject payload;

    @Override
    public ServiceHelper init() {
        payload = JitRequestBuilder.jitStatusUpdateRequestBuilder(jitStatusUpdateModel);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(LENSLAB_STATUS_UPDATE.getUrl(), null, payload.toString(), 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
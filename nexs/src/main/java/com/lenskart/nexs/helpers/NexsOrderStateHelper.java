package com.lenskart.nexs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.NexsOrderState;
import com.lenskart.nexs.manager.OrderStateTransitionManager;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * Main orchestrator for NEXS order state management.
 * Similar to JunoOrderCreationHelper but for state transitions.
 * Provides a high-level interface for managing order state transitions.
 */

@SuperBuilder
@Slf4j
public class NexsOrderStateHelper extends NexsBaseHelper implements ServiceHelper {
    
    private NexsOrderContext orderContext;
    private NexsOrderState targetState;
    private boolean sequentialTransition;
    
    @Override
    public ServiceHelper init() {
        log.info("Initializing NEXS Order State Manager for order: {}", orderContext.getOrderId());
        
        // Validate order context
        validateOrderContext();
        
        // Set default target state if not specified
        if (targetState == null) {
            targetState = orderContext.getCurrentState().getNextState()
                    .orElseThrow(() -> new IllegalStateException("Order is already in final state"));
        }
        
        log.info("Target state set to: {}", targetState.getDisplayName());
        return this;
    }
    
    @Override
    public ServiceHelper process() {
        log.info("Starting order state orchestration for order: {}", orderContext.getOrderId());
        
        NexsOrderState currentState = orderContext.getCurrentState();
        log.info("Current state: {}, Target state: {}", currentState.getDisplayName(), targetState.getDisplayName());
        
        try {
            if (sequentialTransition) {
                // Transition through all intermediate states
                orderContext = OrderStateTransitionManager.transitionToStateSequentially(orderContext, targetState);
            } else {
                // Direct transition to next state only
                orderContext = OrderStateTransitionManager.transitionToState(orderContext, targetState);
            }
            
            log.info("Successfully completed state orchestration for order: {}", orderContext.getOrderId());
            
        } catch (Exception e) {
            log.error("State orchestration failed for order {}: {}", orderContext.getOrderId(), e.getMessage());
            throw new RuntimeException("Order state orchestration failed", e);
        }
        
        return this;
    }
    
    @Override
    public ServiceHelper validate() {
        // Validate that the transition was successful
        if (orderContext.getCurrentState() != targetState) {
            throw new IllegalStateException("Order state transition validation failed. Expected: " + 
                targetState.getDisplayName() + ", Actual: " + orderContext.getCurrentState().getDisplayName());
        }
        
        log.info("Order state transition validation successful for order: {}", orderContext.getOrderId());
        return this;
    }
    
    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
    
    /**
     * Transition order to the next state in sequence
     * 
     * @return Updated NexsOrderStateManager instance
     */
    public NexsOrderStateHelper transitionToNextState() {
        log.info("Transitioning order {} to next state", orderContext.getOrderId());
        
        try {
            orderContext = OrderStateTransitionManager.transitionToNextState(orderContext);
            log.info("Order {} successfully transitioned to: {}", 
                orderContext.getOrderId(), orderContext.getCurrentState().getDisplayName());
        } catch (Exception e) {
            log.error("Failed to transition order {} to next state: {}", orderContext.getOrderId(), e.getMessage());
            throw e;
        }
        
        return this;
    }
    
    /**
     * Transition order to a specific state
     * 
     * @param state The target state
     * @return Updated NexsOrderStateManager instance
     */
    public NexsOrderStateHelper transitionToState(NexsOrderState state) {
        log.info("Transitioning order {} to state: {}", orderContext.getOrderId(), state.getDisplayName());
        
        try {
            orderContext = OrderStateTransitionManager.transitionToState(orderContext, state);
            log.info("Order {} successfully transitioned to: {}", 
                orderContext.getOrderId(), orderContext.getCurrentState().getDisplayName());
        } catch (Exception e) {
            log.error("Failed to transition order {} to state {}: {}", 
                orderContext.getOrderId(), state.getDisplayName(), e.getMessage());
            throw e;
        }
        
        return this;
    }
    
    /**
     * Transition order through multiple states to reach final state
     * 
     * @param finalState The final target state
     * @return Updated NexsOrderStateManager instance
     */
    public NexsOrderStateHelper transitionToFinalState(NexsOrderState finalState) {
        log.info("Transitioning order {} sequentially to final state: {}", 
            orderContext.getOrderId(), finalState.getDisplayName());
        
        try {
            orderContext = OrderStateTransitionManager.transitionToStateSequentially(orderContext, finalState);
            log.info("Order {} successfully reached final state: {}", 
                orderContext.getOrderId(), orderContext.getCurrentState().getDisplayName());
        } catch (Exception e) {
            log.error("Failed to transition order {} to final state {}: {}", 
                orderContext.getOrderId(), finalState.getDisplayName(), e.getMessage());
            throw e;
        }
        
        return this;
    }
    
    /**
     * Get the current order context
     * 
     * @return The current order context
     */
    public NexsOrderContext getOrderContext() {
        return orderContext;
    }
    
    /**
     * Get the state transition history
     * 
     * @return List of state transition records
     */
    public List<NexsOrderContext.StateTransitionRecord> getStateHistory() {
        return orderContext.getStateHistory();
    }
    
    /**
     * Check if order is in a specific state
     * 
     * @param state The state to check
     * @return true if order is in the specified state
     */
    public boolean isInState(NexsOrderState state) {
        return orderContext.getCurrentState() == state;
    }
    
    /**
     * Check if order has completed all transitions (reached final state)
     * 
     * @return true if order is in final state
     */
    public boolean isCompleted() {
        return orderContext.getCurrentState().isFinalState();
    }
    
    /**
     * Get time spent in current state
     * 
     * @return Time in milliseconds
     */
    public long getTimeInCurrentState() {
        return orderContext.getTimeInCurrentState();
    }
    
    /**
     * Get the target state for this manager instance
     * 
     * @return The target state
     */
    public NexsOrderState getTargetState() {
        return targetState;
    }
    
    /**
     * Check if sequential transition mode is enabled
     * 
     * @return true if sequential transition is enabled
     */
    public boolean isSequentialTransitionEnabled() {
        return sequentialTransition;
    }
    
    /**
     * Get a summary of the current state management session
     * 
     * @return Summary string with order details and current state
     */
    public String getSessionSummary() {
        return String.format("Order: %s | Current State: %s | Target State: %s | Sequential: %s | Completed: %s",
                orderContext.getOrderId(),
                orderContext.getCurrentState().getDisplayName(),
                targetState != null ? targetState.getDisplayName() : "None",
                sequentialTransition,
                isCompleted());
    }
    
    /**
     * Validate the order context
     */
    private void validateOrderContext() {
        if (orderContext == null) {
            throw new IllegalArgumentException("Order context cannot be null");
        }
        
        if (orderContext.getOrderId() == null || orderContext.getOrderId().trim().isEmpty()) {
            throw new IllegalArgumentException("Order ID cannot be null or empty");
        }
        
        if (orderContext.getCurrentState() == null) {
            throw new IllegalArgumentException("Current state cannot be null");
        }
        
        log.debug("Order context validation successful for order: {}", orderContext.getOrderId());
    }

}

package com.lenskart.nexs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.pojo.packing.GetItemDetailByIdResponse;
import io.restassured.response.Response;
import lombok.SneakyThrows;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.json.JSONObject;
import org.testng.Assert;

import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.PACKING_GET_ITEM_DETAIL_BY_ID;

@SuperBuilder
@Slf4j
public class PackingDetailsHelper extends NexsBaseHelper implements ServiceHelper {

    NexsOrderContext nexsOrderContext;
    Response response;
    JSONObject payload;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        return this;
    }

    @SneakyThrows
    @Override
    public ServiceHelper process() {

        // Get item detail by id
        queryParams = getQueryParams(nexsOrderContext.getBarcode());
        response = RestUtils.get(PACKING_GET_ITEM_DETAIL_BY_ID.getUrl(), headers, queryParams);
        try {
            Assert.assertEquals(response.statusCode(), HttpStatus.SC_OK);
        } catch (AssertionError e) {
            Thread.sleep(2000);
            response = RestUtils.get(PACKING_GET_ITEM_DETAIL_BY_ID.getUrl(), headers, queryParams, 201);
        }
        GetItemDetailByIdResponse responseGetPackaingDetailsById = response.as(GetItemDetailByIdResponse.class);
        nexsOrderContext.setUnicomOrderCode(String.valueOf(responseGetPackaingDetailsById.getData().getUnicomOrderCode()));
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

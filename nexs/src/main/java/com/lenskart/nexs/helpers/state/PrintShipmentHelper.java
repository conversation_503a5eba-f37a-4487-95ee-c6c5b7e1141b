package com.lenskart.nexs.helpers.state;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.NexsOrderState;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.base.BaseStateTransitionHelper;
import com.lenskart.nexs.base.StateTransitionHelper;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.SneakyThrows;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.testng.Assert;

import java.time.Duration;

import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.WMS_EXPORT_PDF;
import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.WMS_PRINT_SHIPMENT;

@SuperBuilder
@Slf4j
public class PrintShipmentHelper extends BaseStateTransitionHelper implements ServiceHelper, StateTransitionHelper {

    Response response;

    @Override
    public ServiceHelper init() {
        logOperationStart("Print Shipment Started");
        headers = getHeaders(nexsOrderContext);
        return this;
    }

    @SneakyThrows
    @Override
    public ServiceHelper process() {
        queryParams = getQueryParamsForInvoice(nexsOrderContext.getShippingId());

        // Use AwaitUtils polling approach to wait for successful Print Shipment
        boolean success = AwaitUtils.retryOperation(
                () -> {
                    try {
                        log.debug("Attempting to Print Shipment for shipping ID: {}", nexsOrderContext.getShippingId());
                        response = RestUtils.get(WMS_PRINT_SHIPMENT.getUrl(), headers, queryParams);

                        if (response.statusCode() == HttpStatus.SC_OK) {
                            log.info("✅ Print Shipment successful for shipping ID: {}", nexsOrderContext.getShippingId());
                            return true;
                        } else {
                            log.warn("Print Shipment failed with status: {} for shipping ID: {}",
                                    response.statusCode(), nexsOrderContext.getShippingId());
                            return false;
                        }
                    } catch (Exception e) {
                        log.warn("Exception during Print Shipment attempt: {}", e.getMessage());
                        return false;
                    }
                },
                "Print Shipment Retry",
                3, // max attempts
                Duration.ofSeconds(10) // 10 second poll interval
        );
        if (!success) {
            log.error("❌ Print Shipment failed after polling timeout for shipping ID: {}",
                    nexsOrderContext.getShippingId());
            Assert.fail("Print Shipment after multiple attempts for shipping ID: " +
                    nexsOrderContext.getShippingId());
        }

        nexsOrderContext.setShippingProviderCode(response.jsonPath().getString("data.shippingProviderCode"));
        nexsOrderContext.setAwdNumber(response.jsonPath().getString("data.awbNumber"));
        logOperationComplete("Print Shipment Completed");
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

    @Override
    public NexsOrderState getExpectedSourceState() {
        return NexsOrderState.INVOICED;
    }

    @Override
    public NexsOrderState getTargetState() {
        return NexsOrderState.AWB_CREATED;
    }

    @Override
    public void executeTransition() {
        validateTransition();
        test();
    }

    @Override
    public NexsOrderContext getOrderContext() {
        return nexsOrderContext;
    }

}

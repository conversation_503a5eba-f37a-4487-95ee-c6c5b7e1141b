package com.lenskart.nexs.helpers.pickinghelpers;

import com.google.gson.JsonObject;
import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.PICKING_GET_PICKING_SUMMARY_DO;

@SuperBuilder
@Slf4j
public class GetPickingSummaryDOHelper extends NexsBaseHelper implements ServiceHelper {
    NexsOrderContext nexsOrderContext;
    @lombok.Getter
    Response response;

    @Override
    public ServiceHelper init() {
        queryParams = getQueryParamsForDO(nexsOrderContext.getShippingId());
        headers = getHeaders(nexsOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(PICKING_GET_PICKING_SUMMARY_DO.getUrl(), headers, queryParams, 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

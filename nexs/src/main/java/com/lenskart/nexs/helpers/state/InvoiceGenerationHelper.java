package com.lenskart.nexs.helpers.state;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.nexs.base.BaseStateTransitionHelper;
import com.lenskart.nexs.base.StateTransitionHelper;
import com.lenskart.commons.model.NexsOrderState;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

/**
 * Helper class to transition order from QC_DONE to INVOICED state.
 * Handles the business logic for generating invoice.
 */
@SuperBuilder
@Slf4j
public class InvoiceGenerationHelper extends BaseStateTransitionHelper implements ServiceHelper, StateTransitionHelper {

    private Response response;
    private JSONObject payload;

    @Override
    public ServiceHelper init() {
        logOperationStart("Invoice generation");

        // Initialize headers with authentication
        headers = getHeadersWithAuth(nexsOrderContext);

        // Prepare payload for invoice generation
//        payload = create payload

        logOperationComplete("Invoice generation");
        return this;
    }

    @Override
    public ServiceHelper process() {
        logOperationStart("Invoice generation process");

        try {
            // Call API to generate invoice

            logOperationComplete("Invoice generation process");

        } catch (Exception e) {
            logOperationError("Invoice generation process", e.getMessage());
            throw new RuntimeException("Failed to generate invoice", e);
        }

        return this;
    }

    @Override
    public ServiceHelper validate() {
        logOperationStart("Invoice generation validation");

        try {
            // Validate response
            if (response == null) {
                throw new IllegalStateException("No response received from invoice generation API");
            }

            // Validate response status
            if (response.getStatusCode() != 200) {
                throw new IllegalStateException("Invoice generation API returned error: " + response.getStatusCode());
            }

            // Additional business validations

            logOperationComplete("Invoice generation validation");

        } catch (Exception e) {
            logOperationError("Invoice generation validation", e.getMessage());
            throw new RuntimeException("Invoice generation validation failed", e);
        }

        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

    @Override
    public NexsOrderState getExpectedSourceState() {
        return NexsOrderState.QC_DONE;
    }

    @Override
    public NexsOrderState getTargetState() {
        return NexsOrderState.INVOICED;
    }


    @Override
    public void executeTransition() {
        validateTransition();
        test();
    }

    @Override
    public NexsOrderContext getOrderContext() {
        return nexsOrderContext;
    }
}

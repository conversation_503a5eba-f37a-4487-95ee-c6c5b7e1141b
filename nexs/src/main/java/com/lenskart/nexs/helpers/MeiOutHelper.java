package com.lenskart.nexs.helpers;
import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.NexsRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import static com.lenskart.nexs.constants.Constants.UAE_LEGAL_OWNER;
import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.WMS_HAND_EDGING_SCAN;
import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.WMS_LOCATION_SCAN;

@SuperBuilder
@Slf4j
public class MeiOutHelper extends NexsBaseHelper implements ServiceHelper {
    NexsOrderContext orderContext;
    Response response;
    JSONObject payload;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(orderContext);
        payload = NexsRequestBuilder.getMeiPayload(orderContext, "MEI_EXIT");
        return this;
    }

    @Override
    public ServiceHelper process() {
        if (orderContext.getLegalOwner().equals(UAE_LEGAL_OWNER))
            response = RestUtils.post(WMS_HAND_EDGING_SCAN.getUrl(), headers, payload.toString(), 200);
        else
            response = RestUtils.post(WMS_LOCATION_SCAN.getUrl(), headers, payload.toString(), 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

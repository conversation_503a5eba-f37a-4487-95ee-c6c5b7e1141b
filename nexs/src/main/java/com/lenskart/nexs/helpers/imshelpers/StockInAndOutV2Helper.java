package com.lenskart.nexs.helpers.imshelpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.ims.request.UpdateStocksRequestV2;
import com.lenskart.nexs.requestBuilder.ImsRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;

import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.IMS_STOCK_IN_AND_OUT_V2;

@SuperBuilder
@Slf4j
public class StockInAndOutV2Helper extends NexsBaseHelper implements ServiceHelper {

    private String operation;
    private String facility;
    private String location;
    private String legalOwner;
    private String updatedBy;
    private int pid;
    private String barcode;
    private Response response;
    private UpdateStocksRequestV2 payload;

    @Override
    public ServiceHelper init() {
        log.info("Initializing stock update for PID: {}, Barcode: {}", pid, barcode);
         payload = ImsRequestBuilder.stockInAndOutV2(barcode, facility, location, operation, pid, legalOwner, updatedBy);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(
                IMS_STOCK_IN_AND_OUT_V2.getUrl(),
                null,
                JsonUtils.convertObjectToJsonString(payload),
                200);
    return this;
    }

    @Override
    public ServiceHelper validate() {
        var success =  (Boolean) RestUtils.getValueFromResponse(response,"data.itemStockUpdateResponseV2List[0].success");
        log.info("Stock update status: {}", success);
        Assert.assertTrue(success, "Stock update failed in IMS.");
        return this;
    }

    @Override
    public ServiceHelper test() {
        return init().process().validate();
    }
}

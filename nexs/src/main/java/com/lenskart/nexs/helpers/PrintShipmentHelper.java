package com.lenskart.nexs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.SneakyThrows;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.testng.Assert;

import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.WMS_PRINT_SHIPMENT;

@SuperBuilder
@Slf4j
public class PrintShipmentHelper extends NexsBaseHelper implements ServiceHelper {
    NexsOrderContext orderContext;
    Response response;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(orderContext);
        return this;
    }

    @SneakyThrows
    @Override
    public ServiceHelper process() {
        queryParams = getQueryParamsForInvoice(orderContext.getShippingId());
        response = RestUtils.get(WMS_PRINT_SHIPMENT.getUrl(), headers, queryParams);
        Thread.sleep(7000);
        try {
            Assert.assertEquals(response.statusCode(), HttpStatus.SC_OK);
            Thread.sleep(5000);
        } catch (AssertionError e) {
            Thread.sleep(7000);
            response = RestUtils.get(WMS_PRINT_SHIPMENT.getUrl(), headers, queryParams);
            try {
                Assert.assertEquals(response.statusCode(), HttpStatus.SC_OK);
            } catch (AssertionError ee) {
                Thread.sleep(7000);
                response = RestUtils.get(WMS_PRINT_SHIPMENT.getUrl(), headers, queryParams);
                try {
                    Assert.assertEquals(response.statusCode(), HttpStatus.SC_OK);
                } catch (AssertionError eee) {
                    response = RestUtils.get(WMS_PRINT_SHIPMENT.getUrl(), headers, queryParams);
                    Assert.assertEquals(response.statusCode(), HttpStatus.SC_OK);
                }
            }
        }
        orderContext.setShippingProviderCode(response.jsonPath().getString("data.shippingProviderCode"));
        orderContext.setAwdNumber(response.jsonPath().getString("data.awbNumber"));
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

}

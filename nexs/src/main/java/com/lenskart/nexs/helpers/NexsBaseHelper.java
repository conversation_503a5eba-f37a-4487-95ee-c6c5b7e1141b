package com.lenskart.nexs.helpers;

import com.lenskart.commons.base.BaseHelper;
import com.lenskart.commons.constants.HeaderMapper;
import com.lenskart.nexs.config.NexsConfigRegistry;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.exceptions.NexsExceptionStates;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@SuperBuilder
@Slf4j
public class NexsBaseHelper extends BaseHelper<NexsExceptionStates, Object> {

    public Map<String, String> getHeadersWithAuth(NexsOrderContext orderContext) {
        headers = getHeaders(orderContext);
        headers.put(HeaderMapper.AUTHORIZATION.getHeaderName(), orderContext.getHeaders().getFacilityCode());
        return headers;
    }

    public Map<String, String> getloginHeaders(NexsOrderContext nexsOrderContext) {
        headers = new HashMap<>();
        headers.put(HeaderMapper.FACILITY_CODE.getHeaderName(), nexsOrderContext.getHeaders().getFacilityCode());
        headers.put(HeaderMapper.X_LENSKART_APP_ID.getHeaderName(), Constants.X_LENSKART_APP_ID);
        headers.put(HeaderMapper.SOURCE_DOMAIN.getHeaderName(), NexsConfigRegistry.getInstance().getBaseUrl("nexsService"));
        return headers;
    }

    public Map<String, String> getHeadersFacilityCode(NexsOrderContext nexsOrderContext) {
        headers = new HashMap<>();
        headers.put(HeaderMapper.FACILITY_CODE.getHeaderName(), nexsOrderContext.getFacilityCode());
        return headers;
    }

    public Map<String, String> getLogoutHeaders(NexsOrderContext nexsOrderContext) {
        headers = new HashMap<>();
        headers.put(HeaderMapper.X_LENSKART_AUTH_TOKEN.getHeaderName(), nexsOrderContext.getHeaders().getJwtToken());
        headers.put(HeaderMapper.X_LENSKART_APP_ID.getHeaderName(), Constants.X_LENSKART_APP_ID);
        return headers;
    }

    public Map<String, String> getHeaders(NexsOrderContext orderContext) {
        headers = new HashMap<>();
        headers.put(HeaderMapper.JWT_TOKEN.getHeaderName(), orderContext.getHeaders().getJwtToken());
        headers.put(HeaderMapper.FACILITY_CODE.getHeaderName(), orderContext.getFacilityCode());
        headers.put(HeaderMapper.WORKSTATION_ID.getHeaderName(), orderContext.getHeaders().getWorkstationId());
        headers.put(HeaderMapper.DATE_TIME.getHeaderName(), orderContext.getHeaders().getDateTime());
        return headers;
    }

    public Map<String, Object> getQueryParams(String incrementId) {
        queryParams = new HashMap<>();
        queryParams.put("id", incrementId);
        return queryParams;
    }

    public Map<String, Object> getQueryParamsForQC(String scannedEntity) {
        queryParams = new HashMap<>();
        queryParams.put("scannedEntity", scannedEntity);
        return queryParams;
    }

    public Map<String, Object> getQueryParamsForCompleteQC() {
        queryParams = new HashMap<>();
        queryParams.put("wmsOrderCode", "undefined");
        queryParams.put("printAtPacking", "0");
        return queryParams;
    }

    public Map<String, Object> getQueryParamsGetComment(String orderItemHeaderId, String nexsOrderId) {
        queryParams = new HashMap<>();
        queryParams.put("orderItemHeaderId", orderItemHeaderId);
        queryParams.put("nexsOrderId", nexsOrderId);
        return queryParams;
    }

    public Map<String, Object> getQueryParamsForInvoice(String shippingPackageId) {
        queryParams = new HashMap<>();
        queryParams.put("shippingPackageId", shippingPackageId);
        return queryParams;
    }

    public Map<String, Object> getQueryParamsForDO(String shippingPackageId) {
        queryParams = new HashMap<>();
        queryParams.put("shippingPackageID", shippingPackageId);
        return queryParams;
    }

    public Map<String, Object> getQueryParamsForDOPicking(String barcode, String pickingSummaryId) {
        queryParams = new HashMap<>();
        queryParams.put("barcode", barcode);
        queryParams.put("pickingSummaryId", pickingSummaryId);
        return queryParams;
    }

    public Map<String, Object> getQueryParamsForGetProductDetails(String product_id) {
        queryParams = new HashMap<>();
        queryParams.put("pid", product_id);
        return queryParams;
    }


    public Map<String, Object> getQueryParamsTransferStatus(String transferCode, String status) {
        queryParams = new HashMap<>();
        queryParams.put("transferCode", transferCode);
        queryParams.put("status", status);
        return queryParams;
    }

    public Map<String, Object> getQueryParamsForScanBarcode(String barcode, String boxBarcode, String unicomTransferCode, boolean enableBoxBarcode) {
        queryParams = new HashMap<>();
        queryParams.put("barcode", barcode);
        queryParams.put("box_barcode", boxBarcode);
        queryParams.put("unicom_transfer_code", unicomTransferCode);
        queryParams.put("enableBoxBarcode", enableBoxBarcode);
        return queryParams;
    }

    public Map<String, Object> getQueryParamsForGetTransferDetails(String transferCode, boolean barcodeRequired, int page_number, int pageSize) {
        queryParams = new HashMap<>();
        queryParams.put("transferCode", transferCode);
        queryParams.put("barcode_required", barcodeRequired);
        queryParams.put("page_number", page_number);
        queryParams.put("page_size", pageSize);
        return queryParams;
    }
    public Map<String, Object> getAverageUnitPriceQueryParams() {
        queryParams = new HashMap<>();
        queryParams.put("useFallbackPrice", true);
        return queryParams;
    }
}

package com.lenskart.nexs.helpers;

import com.lenskart.commons.base.BaseHelper;
import com.lenskart.commons.constants.HeaderMapper;
import com.lenskart.nexs.exceptions.NexsExceptionStates;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

@SuperBuilder
@Slf4j
public class NexsBaseHelper extends BaseHelper<NexsExceptionStates, Object> {

    public Map<String, String> getHeaders(NexsOrderContext orderContext) {
        headers = new HashMap<>();
        return headers;
    }

    public Map<String, String> getHeadersWithAuth(NexsOrderContext orderContext){
        headers = getHeaders(orderContext);
        headers.put(HeaderMapper.AUTHORIZATION.getHeaderName(), orderContext.getHeaders().getAuthToken());
        return headers;
    }
}

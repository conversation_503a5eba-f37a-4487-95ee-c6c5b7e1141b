package com.lenskart.nexs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.NexsRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.PACKING_COMPLETE_PACKING;

@SuperBuilder
@Slf4j
public class PackingCompletionHelper extends NexsBaseHelper implements ServiceHelper {
    NexsOrderContext nexsOrderContext;
    Response response;
    JSONObject payload;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        payload = NexsRequestBuilder.getCompletePackingPayload(nexsOrderContext,nexsOrderContext.getUnicomOrderCode(),nexsOrderContext.getShippingId(), Integer.parseInt(nexsOrderContext.getIncrementId()));
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(PACKING_COMPLETE_PACKING.getUrl(), headers, payload.toString(), 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

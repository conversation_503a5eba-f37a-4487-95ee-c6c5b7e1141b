package com.lenskart.nexs.helpers;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.GenericUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.api.ImsService;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.pojo.picking.AddverbPickItemRequest;
import com.lenskart.nexs.pojo.picking.MetaData;
import com.lenskart.nexs.pojo.picking.OrderConfirm;
import io.restassured.response.Response;
import lombok.SneakyThrows;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.PICKING_ADDVERB_PICK_ITEM;

@SuperBuilder
@Slf4j
public class AddverbPickingHelper extends NexsBaseHelper implements ServiceHelper {

    NexsOrderContext nexsOrderContext;
    Response response;
    JSONObject payload;
    String waveId;
    String shippingPackageId;
    String orderItemId;
    String facility;
    List<OrderConfirm> list;
    AddverbPickItemRequest addverbPickItemRequest;


    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        list = new ArrayList<>();
        addverbPickItemRequest = new AddverbPickItemRequest();
        MetaData metaData = new MetaData("gtp");
        String trayBarcode = "T" + GenericUtils.genrateRandomNumericString(6);
        AtomicInteger index = new AtomicInteger(0);

        nexsOrderContext.getOrderItemResponses().forEach(orderItemResponse -> {
                    nexsOrderContext.setProductId(String.valueOf(orderItemResponse.getProduct_id()));
                    log.info("productID {}", nexsOrderContext.getProductId());
                    OrderConfirm orderConfirm = new OrderConfirm();
                    orderConfirm.setOrderItemId(String.valueOf(orderItemResponse.getId()));
                    orderConfirm.setSerialNumber(ImsService.fetchBarcodeForGivenPid(Constants.AVAILABLE, Constants.GOOD,
                            true, nexsOrderContext.getFacilityCode(), nexsOrderContext.getProductId(),
                            Constants.AVAILABLE, String.valueOf(index.getAndIncrement())));
                    orderConfirm.setWaveId(waveId);
                    orderConfirm.setTrayId(trayBarcode);
                    orderConfirm.setStatus("PICKED");
                    orderConfirm.setShipmemntId(nexsOrderContext.getShippingId());
                    orderConfirm.setMetaData(metaData);
                    list.add(orderConfirm);
                }
        );
        addverbPickItemRequest.setFacilityId(nexsOrderContext.getFacilityCode());
        addverbPickItemRequest.setOrderConfirm(list);
        return this;
    }

    @SneakyThrows
    @Override
    public ServiceHelper process() {
        response = RestUtils.post(PICKING_ADDVERB_PICK_ITEM.getUrl(), headers,
                new ObjectMapper().writeValueAsString(addverbPickItemRequest), 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

package com.lenskart.nexs.helpers.state;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.NexsOrderState;
import com.lenskart.commons.utils.GenericUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.base.BaseStateTransitionHelper;
import com.lenskart.nexs.base.StateTransitionHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.NexsRequestBuilder;
import com.lenskart.nexs.database.WMSDbUtils;
import com.lenskart.nexs.validator.wmsvalidator.TrayMakingValidator;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import static com.lenskart.commons.model.NexsOrderState.PICKED;
import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.WMS_CREATE_TRAY;

/**
 * Helper class to transition order from PICKED to IN_TRAY Status.
 * Handles the business logic for completing the picking process.
 */

@SuperBuilder
@Slf4j
public class TrayMakingHelper extends BaseStateTransitionHelper implements ServiceHelper, StateTransitionHelper {

    Response response;
    JSONObject payload;
    String trayBarcode;
    String frameBarcode;

    @Override
    public ServiceHelper init() {
        logOperationStart("Tray Making Started");
        headers = getHeaders(nexsOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        String trayBarcode = "T" + GenericUtils.genrateRandomNumericString(5);
        if(WMSDbUtils.getStatusOfShipment(nexsOrderContext).equals(PICKED.name())){
                nexsOrderContext.setTrayBarcode(trayBarcode);
                for (String barcode : nexsOrderContext.getBarcodes()) {
                    payload = NexsRequestBuilder.getCreateTrayPayload(nexsOrderContext, barcode, trayBarcode);
                    response = RestUtils.post(WMS_CREATE_TRAY.getUrl(), headers, payload.toString(), 200);
                }
            }

        logOperationComplete("Tray Making Completed");
        return this;
    }

    @Override
    public ServiceHelper validate() {
        TrayMakingValidator validator = TrayMakingValidator.builder().nexsOrderContext(nexsOrderContext).build();
        validator.validateNode();
        validator.validateDBEntities();
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

    @Override
    public NexsOrderState getExpectedSourceState() {
        return PICKED;
    }

    @Override
    public NexsOrderState getTargetState() {
        return NexsOrderState.IN_TRAY;
    }

    @Override
    public void executeTransition() {
        validateTransition();
        test();
    }

    @Override
    public NexsOrderContext getOrderContext() {
        return nexsOrderContext;
    }

}

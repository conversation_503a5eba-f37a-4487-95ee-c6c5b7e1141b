package com.lenskart.nexs.helpers.state;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.base.BaseStateTransitionHelper;
import com.lenskart.nexs.base.StateTransitionHelper;
import com.lenskart.commons.model.NexsOrderState;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.util.Map;

import static com.lenskart.nexs.endpoints.NexsEndpoints.INITIATE_PICKING;

/**
 * Helper class to transition order from PROCESSING to IN_PICKING state.
 * Handles the business logic for initiating the picking process.
 */
@SuperBuilder
@Slf4j
public class PickingInitiationHelper extends BaseStateTransitionHelper implements ServiceHelper, StateTransitionHelper {

    private Response response;
    private JSONObject payload;

    @Override
    public ServiceHelper init() {
        logOperationStart("Picking initiation");

        // Initialize headers with authentication
        headers = getHeadersWithAuth(orderContext);

        // Prepare payload for picking initiation
//        payload = create payload

        logOperationComplete("Picking initiation");
        return this;
    }

    @Override
    public ServiceHelper process() {
        logOperationStart("Picking process initiation");

        try {
            // Call API to initiate picking
            response = RestUtils.post(
                    INITIATE_PICKING.getUrl(Map.of("orderId", orderContext.getOrderId())),
                    headers,
                    payload.toString(),
                    200
            );

            // Process the response


            logOperationComplete("Picking process initiation");

        } catch (Exception e) {
            logOperationError("Picking process initiation", e.getMessage());
            throw new RuntimeException("Failed to initiate picking process", e);
        }

        return this;
    }

    @Override
    public ServiceHelper validate() {
        logOperationStart("Picking initiation validation");

        try {
            // Validate response
            if (response == null) {
                throw new IllegalStateException("No response received from picking initiation API");
            }

            // Validate response status
            if (response.getStatusCode() != 200) {
                throw new IllegalStateException("Picking initiation API returned error: " + response.getStatusCode());
            }

            // Additional business validations

            logOperationComplete("Picking initiation validation");

        } catch (Exception e) {
            logOperationError("Picking initiation validation", e.getMessage());
            throw new RuntimeException("Picking initiation validation failed", e);
        }

        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

    @Override
    public NexsOrderState getExpectedSourceState() {
        return NexsOrderState.PROCESSING;
    }

    @Override
    public NexsOrderState getTargetState() {
        return NexsOrderState.IN_PICKING;
    }



    @Override
    public void executeTransition() {
        validateTransition();
        test();

    }

    @Override
    public NexsOrderContext getOrderContext() {
        return orderContext;
    }
}

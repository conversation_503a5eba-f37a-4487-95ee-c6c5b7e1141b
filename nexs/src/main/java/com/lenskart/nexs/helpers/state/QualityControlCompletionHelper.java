package com.lenskart.nexs.helpers.state;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.base.BaseStateTransitionHelper;
import com.lenskart.nexs.base.StateTransitionHelper;
import com.lenskart.commons.model.NexsOrderState;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.util.Map;

import static com.lenskart.nexs.endpoints.NexsEndpoints.COMPLETE_QC;

/**
 * Helper class to transition order from IN_QC to QC_DONE state.
 * Handles the business logic for completing quality control process.
 */
@SuperBuilder
@Slf4j
public class QualityControlCompletionHelper extends BaseStateTransitionHelper implements ServiceHelper, StateTransitionHelper {
    
    private Response response;
    private JSONObject payload;
    
    @Override
    public ServiceHelper init() {
        logOperationStart("QC completion");
        
        // Initialize headers with authentication
        headers = getHeadersWithAuth(orderContext);
        
        // Prepare payload for QC completion
//        payload = create payload
        
        logOperationComplete("QC completion");
        return this;
    }
    
    @Override
    public ServiceHelper process() {
        logOperationStart("Quality control completion");
        
        try {
            // Call API to complete QC
            response = RestUtils.put(
                COMPLETE_QC.getUrl(Map.of("orderId", orderContext.getOrderId())),
                headers,
                payload.toString(),
                200
            );
            
            // Process the response
            
            logOperationComplete("Quality control completion");
            
        } catch (Exception e) {
            logOperationError("Quality control completion", e.getMessage());
            throw new RuntimeException("Failed to complete quality control process", e);
        }
        
        return this;
    }
    
    @Override
    public ServiceHelper validate() {
        logOperationStart("QC completion validation");
        
        try {
            // Validate response
            if (response == null) {
                throw new IllegalStateException("No response received from QC completion API");
            }
            
            // Validate response status
            if (response.getStatusCode() != 200) {
                throw new IllegalStateException("QC completion API returned error: " + response.getStatusCode());
            }
            
            // Additional business validations
            
            logOperationComplete("QC completion validation");
            
        } catch (Exception e) {
            logOperationError("QC completion validation", e.getMessage());
            throw new RuntimeException("QC completion validation failed", e);
        }
        
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

    @Override
    public NexsOrderState getExpectedSourceState() {
        return NexsOrderState.IN_QC;
    }
    
    @Override
    public NexsOrderState getTargetState() {
        return NexsOrderState.QC_DONE;
    }


    @Override
    public void executeTransition() {
        validateTransition();
        test();

    }

    @Override
    public NexsOrderContext getOrderContext() {
        return orderContext;
    }
}

package com.lenskart.nexs.helpers.pickinghelpers;

import com.google.gson.JsonObject;
import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.database.ImsDbUtils;
import com.lenskart.nexs.database.WMSDbUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.PICKING_PICK_BARCODE_DO;

/**
 * Helper class for picking barcodes for Delivery Orders (DO)
 * Handles the process of selecting barcodes for products in a picking summary
 */
@SuperBuilder
@Slf4j
public class PickBarcodeForDOHelper extends NexsBaseHelper implements ServiceHelper {
    NexsOrderContext nexsOrderContext;
    GetPickingSummaryDOHelper getPickingSummaryDOHelper;
    Response response;
    private static final int MAX_RETRY_ATTEMPTS = 2;
    private static final int EXTRA_BARCODES_BUFFER = 30;

    @Override
    public ServiceHelper init() {
        log.info("Initializing PickBarcodeForDOHelper for shipping ID: {}", nexsOrderContext.getShippingId());
        headers = getHeaders(nexsOrderContext);
        nexsOrderContext.setLegalOwner("LKIN");
        return this;
    }

    @Override
    public ServiceHelper process() {
        log.info("Starting barcode picking process for DO");
        Response pickingSummaryResponse = getPickingSummaryDOHelper.getResponse();
        List<JsonObject> productDetailsList = pickingSummaryResponse.jsonPath().getList("data.productQuantityDetailsList");
        
        log.info("Found {} products in picking summary", productDetailsList.size());
        
        for (int productIndex = 0; productIndex < productDetailsList.size(); productIndex++) {
            processProductItem(pickingSummaryResponse, productIndex);
        }
        
        log.info("Completed barcode picking process for all products");
        return this;
    }

    private void processProductItem(Response pickingSummaryResponse, int productIndex) {
        // Extract product details
        String productId = pickingSummaryResponse.jsonPath()
                .getString("data.productQuantityDetailsList[" + productIndex + "].productId");
        int requiredQuantity = pickingSummaryResponse.jsonPath()
                .getInt("data.productQuantityDetailsList[" + productIndex + "].totalQuantity");
        
        log.info("Processing product ID: {} with quantity: {}", productId, requiredQuantity);
        nexsOrderContext.setProductId(productId);
        
        // Fetch barcodes for the product
        List<String> barcodesList = fetchBarcodesForProduct(productId, requiredQuantity);
        if (barcodesList.isEmpty()) {
            log.error("No barcodes available for product ID: {}", productId);
            return;
        }
        
        // Pick barcodes for the required quantity
        pickBarcodesForProduct(barcodesList, requiredQuantity);
    }

    private List<String> fetchBarcodesForProduct(String productId, int requiredQuantity) {
        List<String> barcodesList = new ArrayList<>();
        try {
            // Fetch more barcodes than needed to handle potential failures
            List<Map<String, Object>> barcodes = ImsDbUtils.getGAABarcodes(
                    nexsOrderContext, requiredQuantity + EXTRA_BARCODES_BUFFER);
            log.info("Fetched {} barcodes for product ID: {}", barcodes.size(), productId);
            // Extract only the barcode value from each map
            barcodes.forEach(barcodeMap -> {
                Object barcodeValue = barcodeMap.get("barcode");
                if (barcodeValue != null) {
                    barcodesList.add(barcodeValue.toString());
                }
            });
        } catch (Exception e) {
            log.error("Error fetching barcodes for product ID: {}", productId, e);
        }
        return barcodesList;
    }

    private void pickBarcodesForProduct(List<String> barcodesList, int requiredQuantity) {
        int barcodeIndex = 0;
        
        for (int i = 0; i < requiredQuantity; i++) {
            if (barcodeIndex >= barcodesList.size()) {
                log.warn("Ran out of barcodes after picking {} of {} required items", i, requiredQuantity);
                break;
            }
            
            boolean pickSuccessful = pickBarcodeWithRetry(barcodesList, barcodeIndex, MAX_RETRY_ATTEMPTS);
            
            if (pickSuccessful) {
                barcodeIndex++;
            } else {
                // If all retries failed, try with next barcode
                log.warn("Failed to pick barcode after {} attempts, trying next barcode", MAX_RETRY_ATTEMPTS);
                barcodeIndex += MAX_RETRY_ATTEMPTS + 1;
            }
        }
    }

    private boolean pickBarcodeWithRetry(List<String> barcodesList, int startIndex, int maxRetries) {
        int currentIndex = startIndex;
        
        for (int attempt = 0; attempt <= maxRetries; attempt++) {
            if (currentIndex >= barcodesList.size()) {
                log.warn("No more barcodes available for retry");
                return false;
            }
            
            String barcode = barcodesList.get(currentIndex);
            queryParams = getQueryParamsForDOPicking(barcode, nexsOrderContext.getPickingSummaryId());
            
            try {
                log.debug("Attempting to pick barcode: {} (attempt {})", barcode, attempt + 1);
                response = RestUtils.post(PICKING_PICK_BARCODE_DO.getUrl(), headers, queryParams, 200);
                log.info("Successfully picked barcode: {}", barcode);
                return true;
            } catch (Exception e) {
                log.warn("Failed to pick barcode: {} (attempt {}): {}", barcode, attempt + 1, e.getMessage());
                currentIndex++;
            }
        }
        
        return false;
    }

    @Override
    public ServiceHelper validate() {
        log.info("Validating picking status for shipping ID: {}", nexsOrderContext.getShippingId());
        List<Map<String, Object>> statusList = WMSDbUtils.getShipmentStatus(nexsOrderContext);
        
        statusList.forEach(item -> {
            String status = item.get("status").toString();
            log.info("Shipment status: {}", status);
            Assert.assertNotEquals(status, "IN_PICKING", 
                    "Shipment should not be in IN_PICKING state after barcode picking");
        });
        
        log.info("Validation successful - shipment is no longer in picking state");
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}


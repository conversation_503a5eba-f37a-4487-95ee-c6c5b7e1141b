package com.lenskart.nexs.helpers.grn;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

import static com.lenskart.nexs.endpoints.NexsEndpoints.CLOSE_GRN;

@SuperBuilder
public class CloseGrnHelper extends NexsBaseHelper implements ServiceHelper {
    Response response;
    NexsOrderContext nexsOrderContext;
    String grnCode;


    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
         response = RestUtils.post(CLOSE_GRN.getUrl(Map.of("grnCode", grnCode)), headers, null, 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;

    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }


}
package com.lenskart.nexs.helpers.transfer;

import com.lenskart.commons.utils.RestUtils;
import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.TransfersRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

import static com.lenskart.nexs.endpoints.NexsEndpoints.SCAN_BARCODE;

@SuperBuilder
public class ScanBarcodeHelper extends NexsBaseHelper implements ServiceHelper {

    Response response;
    NexsOrderContext nexsOrderContext;
    JSONObject payload;


    @Override
    public ServiceHelper init() {
        queryParams = getQueryParamsForScanBarcode(nexsOrderContext.getBarcode(), nexsOrderContext.getBoxBarcode(), nexsOrderContext.getUnicomTransferCode(), false);
        headers = getHeaders(nexsOrderContext);
        payload = TransfersRequestBuilder.ScanBarcodePayload(nexsOrderContext.getBarcode(), nexsOrderContext.getBoxBarcode(), nexsOrderContext.getUnicomTransferCode(), nexsOrderContext.getTransferCode());
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(SCAN_BARCODE.getUrl(), headers, queryParams, payload.toString(), 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

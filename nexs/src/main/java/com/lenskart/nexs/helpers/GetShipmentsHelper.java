package com.lenskart.nexs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Objects;

import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.WMS_ORDER_DETAILS_OVERVIEW;

@SuperBuilder
@Slf4j
public class GetShipmentsHelper extends NexsBaseHelper implements ServiceHelper {
    NexsOrderContext nexsOrderContext;
    Response response;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        if (nexsOrderContext.getIncrementId() != null) {
            queryParams = getQueryParams(nexsOrderContext.getIncrementId());
            response = RestUtils.get(WMS_ORDER_DETAILS_OVERVIEW.getUrl(), headers, queryParams, 200);
            List<String> shipmentIds = response.jsonPath().getList("data.shipmentLists.shipmentId");
            nexsOrderContext.setShipmentIds(shipmentIds);
            log.info("Shipment ids: {}", nexsOrderContext.getShipmentIds());
        } else if (nexsOrderContext.getShippingId() != null) {
            log.info("Shipment ID is given Continue");
        } else {
            throw new RuntimeException("No shipment id or increment id found in order context");
        }
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

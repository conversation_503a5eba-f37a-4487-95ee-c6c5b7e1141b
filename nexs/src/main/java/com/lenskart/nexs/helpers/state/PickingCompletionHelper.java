package com.lenskart.nexs.helpers.state;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.api.ImsService;
import com.lenskart.nexs.base.BaseStateTransitionHelper;
import com.lenskart.nexs.base.StateTransitionHelper;
import com.lenskart.commons.model.NexsOrderState;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.PICKING_DYNAMIC_PICKING;


/**
 * Helper class to transition order from IN_PICKING to PICKED state.
 * Handles the business logic for completing the picking process.
 */
@SuperBuilder
@Slf4j
public class PickingCompletionHelper extends BaseStateTransitionHelper implements ServiceHelper, StateTransitionHelper {

    JSONObject payload;
    Response response;
    List<String> barcodes;

    @Override
    public ServiceHelper init() {
        logOperationStart("Picking completion Started");
        headers = getHeaders(nexsOrderContext);
        barcodes = new ArrayList<>();
        return this;
    }

    @Override
    public ServiceHelper process() {
        nexsOrderContext.getOrderItemResponses().forEach(orderItemResponse -> {
            nexsOrderContext.setProductId(String.valueOf(orderItemResponse.getProduct_id()));
            log.info("productID {}", nexsOrderContext.getProductId());
            String barcode = ImsService.fetchBarcodeForGivenPid(Constants.AVAILABLE, Constants.GOOD, true, nexsOrderContext.getFacilityCode(), nexsOrderContext.getProductId(), Constants.AVAILABLE);
            nexsOrderContext.setFrameBarcode(barcode);
            nexsOrderContext.setBarcode(barcode);
            barcodes.add(barcode);
            response = RestUtils.post(PICKING_DYNAMIC_PICKING.getUrl(Map.of("orderItemId", String.valueOf(orderItemResponse.getId()), "barcode", nexsOrderContext.getFrameBarcode())), headers, null, 200);
        });
        nexsOrderContext.setBarcodes(barcodes);

        logOperationComplete("Picking completion");
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

    @Override
    public NexsOrderState getExpectedSourceState() {
        return NexsOrderState.IN_PICKING;
    }

    @Override
    public NexsOrderState getTargetState() {
        return NexsOrderState.PICKED;
    }


    @Override
    public void executeTransition() {
        validateTransition();
        test();

    }

    @Override
    public NexsOrderContext getOrderContext() {
        return nexsOrderContext;
    }
}

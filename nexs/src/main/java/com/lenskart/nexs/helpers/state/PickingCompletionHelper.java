package com.lenskart.nexs.helpers.state;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.base.BaseStateTransitionHelper;
import com.lenskart.nexs.base.StateTransitionHelper;
import com.lenskart.commons.model.NexsOrderState;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.util.Map;

/**
 * Helper class to transition order from IN_PICKING to PICKED state.
 * Handles the business logic for completing the picking process.
 */
@SuperBuilder
@Slf4j
public class PickingCompletionHelper extends BaseStateTransitionHelper implements ServiceHelper, StateTransitionHelper {

    private Response response;
    private JSONObject payload;

    @Override
    public ServiceHelper init() {
        logOperationStart("Picking completion");

        // Initialize headers with authentication
        headers = getHeadersWithAuth(orderContext);

        // Prepare payload for picking completion
//        payload = create payload

        logOperationComplete("Picking completion");
        return this;
    }

    @Override
    public ServiceHelper process() {
        logOperationStart("Picking process completion");
        return this;
    }

    @Override
    public ServiceHelper validate() {
        logOperationStart("Picking completion validation");

        try {
            // Validate response
            if (response == null) {
                throw new IllegalStateException("No response received from picking completion API");
            }

            // Validate response status
            if (response.getStatusCode() != 200) {
                throw new IllegalStateException("Picking completion API returned error: " + response.getStatusCode());
            }

            // Additional business validations

            logOperationComplete("Picking completion validation");

        } catch (Exception e) {
            logOperationError("Picking completion validation", e.getMessage());
            throw new RuntimeException("Picking completion validation failed", e);
        }

        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

    @Override
    public NexsOrderState getExpectedSourceState() {
        return NexsOrderState.IN_PICKING;
    }

    @Override
    public NexsOrderState getTargetState() {
        return NexsOrderState.PICKED;
    }


    @Override
    public void executeTransition() {
        validateTransition();
        test();

    }

    @Override
    public NexsOrderContext getOrderContext() {
        return orderContext;
    }
}

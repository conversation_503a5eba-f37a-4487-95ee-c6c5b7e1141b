package com.lenskart.nexs.helpers.wms;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.requestBuilder.WmsRequestBuilder;
import io.restassured.response.Response;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

import static com.lenskart.nexs.endpoints.NexsEndpoints.WMS_UPDATE_BOX_COUNT;

@SuperBuilder
public class UpdateBoxCountHelper extends NexsBaseHelper implements ServiceHelper {
    NexsOrderContext nexsOrderContext;
    JSONObject payload;
    private Response response;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        payload = WmsRequestBuilder.UpdateBoxCountPayload(nexsOrderContext.getShippingId(), nexsOrderContext.getGetBoxCount());
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.put(WMS_UPDATE_BOX_COUNT.getUrl(), headers, payload.toString());
        return this;
    }

    @Override
    public ServiceHelper validate() {
        assert response.getStatusCode() == 200;
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }


}

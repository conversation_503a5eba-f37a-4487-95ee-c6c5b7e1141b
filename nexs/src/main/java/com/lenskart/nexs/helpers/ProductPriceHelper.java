package com.lenskart.nexs.helpers;
import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.model.ProductPriceModel;
import com.lenskart.nexs.requestBuilder.ProductPriceBuilder;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import io.restassured.response.Response;
import static com.lenskart.nexs.endpoints.NexsEndpoints.CATALOG_PRODUCT_PRICE;

@SuperBuilder
@Slf4j
public class ProductPriceHelper extends NexsBaseHelper implements ServiceHelper {
    Response response;
    NexsOrderContext nexsOrderContext;
    JSONObject payload;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        ProductPriceModel.HsnTaxInfo taxInfo = ProductPriceModel.HsnTaxInfo.builder()
                .hsnCode(nexsOrderContext.getHsnCode())
                .stateCode(nexsOrderContext.getStateCode())
                .igstPer(nexsOrderContext.getIgstPer())
                .cgstPer(nexsOrderContext.getCgstPer())
                .sgstPer(nexsOrderContext.getSgstPer())
                .ugstPer(nexsOrderContext.getUgstPer())
                .flatPer(nexsOrderContext.getFlatPer())
                .igstRate(nexsOrderContext.getIgstRate())
                .cgstRate(nexsOrderContext.getCgstRate())
                .sgstRate(nexsOrderContext.getSgstRate())
                .ugstRate(nexsOrderContext.getUgstRate())
                .flatRate(nexsOrderContext.getFlatRate())
                .build();

        payload = new JSONObject(ProductPriceBuilder.ProductPriceModel(
                nexsOrderContext.getCurrency(),
                nexsOrderContext.getFacilityStateCode(),
                taxInfo,
                nexsOrderContext.getProductId(),
                nexsOrderContext.getQuantity(),
                nexsOrderContext.getVendorCode(),
                nexsOrderContext.getVendorUnitCostPrice()
        ));
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(CATALOG_PRODUCT_PRICE.getUrl(), headers, payload.toString(), 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        log.info("Response:{}", response);
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

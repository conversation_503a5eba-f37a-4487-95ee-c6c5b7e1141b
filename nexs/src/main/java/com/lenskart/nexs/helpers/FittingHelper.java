package com.lenskart.nexs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.pojo.fitting.GetFittingDetailResponse;
import com.lenskart.nexs.requestBuilder.NexsRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.util.Map;

import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.FITTING_GET_FITTING_DETAIL;
import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.FITTING_MARK_FITTING_COMPLETE;

@SuperBuilder
@Slf4j
public class FittingHelper extends NexsBaseHelper implements ServiceHelper {
    NexsOrderContext orderContext;
    Response response;
    JSONObject payload;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(orderContext);
        payload = NexsRequestBuilder.getCompleteFittingPayload(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(FITTING_GET_FITTING_DETAIL.getUrl(Map.of("fittingId", orderContext.getFittingId())),
                headers, null, 200);
        GetFittingDetailResponse getFittingDetialResponse = response.as(GetFittingDetailResponse.class);

        response = RestUtils.post(FITTING_MARK_FITTING_COMPLETE.getUrl(Map.of("fittingId", orderContext.getFittingId(),
                "shippingPackageId", orderContext.getShippingId())), headers, payload.toString(), 200);

        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

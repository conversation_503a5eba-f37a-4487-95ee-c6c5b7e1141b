package com.lenskart.nexs.helpers.po;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import java.util.Map;

import static com.lenskart.nexs.endpoints.NexsEndpoints.GET_AVERAGE_UNIT_PRICE;

@SuperBuilder
@Slf4j
public class GetAverageUnitPriceHelper extends NexsBaseHelper implements ServiceHelper {

    Response response;
    Map<String, Object> queryParam;
    NexsOrderContext nexsOrderContext;
    @Override
    public ServiceHelper init() {
        queryParam = getAverageUnitPriceQueryParams();
        return this;
    }
    
    @Override
    public ServiceHelper process() {
            response = RestUtils.get(GET_AVERAGE_UNIT_PRICE.getUrl(Map.of("productId", nexsOrderContext.getProductId(),
                    "countryCode", nexsOrderContext.getCountryCode())), null, queryParam);
        return this;
    }
    
    @Override
    public ServiceHelper validate() {
        // Add validation logic if needed
        return this;
    }
    
    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
    @Test
    public static void main(String[] args) {
        GetAverageUnitPriceHelper helper = GetAverageUnitPriceHelper.builder().nexsOrderContext(NexsOrderContext.builder().productId("148248").build()).build();
        helper.test();
    }

}
package com.lenskart.nexs.helpers.state;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.nexs.base.BaseStateTransitionHelper;
import com.lenskart.nexs.base.StateTransitionHelper;
import com.lenskart.commons.model.NexsOrderState;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

/**
 * Helper class to transition order from CREATED to PROCESSING state.
 * Handles the business logic for starting order processing.
 */
@SuperBuilder
@Slf4j
public class ProcessingHelper extends BaseStateTransitionHelper implements ServiceHelper, StateTransitionHelper {
    
    private Response response;
    private JSONObject payload;
    
    @Override
    public NexsOrderState getExpectedSourceState() {
        return NexsOrderState.CREATED;
    }
    
    @Override
    public NexsOrderState getTargetState() {
        return NexsOrderState.PROCESSING;
    }


    @Override
    public ServiceHelper init() {
        return this;
    }

    @Override
    public ServiceHelper process() {
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

    @Override
    public void executeTransition() {
        validateTransition();
        test();

    }

    @Override
    public NexsOrderContext getOrderContext() {
        return orderContext;
    }
}

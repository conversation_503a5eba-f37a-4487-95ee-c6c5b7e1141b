package com.lenskart.nexs.helpers.state;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.database.mysql.MySQLQueryExecutor;
import com.lenskart.commons.model.MySQLCluster;
import com.lenskart.commons.model.NexsOrderState;
import com.lenskart.commons.model.ProcessingType;
import com.lenskart.commons.utils.GenericUtils;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.api.ImsService;
import com.lenskart.nexs.base.BaseStateTransitionHelper;
import com.lenskart.nexs.base.StateTransitionHelper;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.database.PickingQueries;
import com.lenskart.nexs.database.WMSDbUtils;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.model.picking.AddverbPickItemRequest;
import com.lenskart.nexs.model.picking.MetaData;
import com.lenskart.nexs.model.picking.OrderConfirm;
import io.restassured.response.Response;
import lombok.SneakyThrows;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.testng.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

import static com.lenskart.commons.model.NexsOrderState.*;
import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.PICKING_ADDVERB_PICK_ITEM;

@SuperBuilder
@Slf4j
public class AddverbPickingHelper extends BaseStateTransitionHelper implements ServiceHelper, StateTransitionHelper {

    Response response;
    JSONObject payload;
    String waveId;
    String shippingPackageId;
    String orderItemId;
    String facility;
    List<OrderConfirm> list;
    AddverbPickItemRequest addverbPickItemRequest;
    boolean isPicked;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        list = new ArrayList<>();
        addverbPickItemRequest = new AddverbPickItemRequest();
        MetaData metaData = new MetaData("gtp");
        String trayBarcode = "T" + GenericUtils.genrateRandomNumericString(6);
        AtomicInteger index = new AtomicInteger(0);

        List<Map<String, Object>> getWaveId = MySQLQueryExecutor
                .executeQuery(MySQLCluster.NEXS_CLUSTER.getClusterName(),
                        Constants.PICKING_DB,
                        PickingQueries.WAVE_ID,
                        nexsOrderContext.getShippingId());
        waveId = getWaveId.getFirst().get("picking_summary_id").toString();

        List<Map<String, Object>> orderItemId = MySQLQueryExecutor
                .executeQuery(MySQLCluster.NEXS_CLUSTER.getClusterName(),
                        Constants.PICKING_DB,
                        PickingQueries.ORDER_ITEM_ID,
                        nexsOrderContext.getShippingId());


        orderItemId.forEach(item -> {
                    if (WMSDbUtils.getStatusOfShipment(nexsOrderContext).equals(IN_PICKING.name())) {
                        nexsOrderContext.setProductId(String.valueOf(item.get("product_id")));
                        log.info("productID {}", nexsOrderContext.getProductId());
                        OrderConfirm orderConfirm = new OrderConfirm();
                        orderConfirm.setOrderItemId(String.valueOf(item.get("id")));
                        orderConfirm.setSerialNumber(ImsService.fetchBarcodeForGivenPid(Constants.AVAILABLE, Constants.GOOD,
                                true, nexsOrderContext.getFacilityCode(), nexsOrderContext.getProductId(),
                                Constants.AVAILABLE, String.valueOf(index.getAndIncrement())));
                        orderConfirm.setWaveId(waveId);
                        orderConfirm.setTrayId(trayBarcode);
                        orderConfirm.setStatus("PICKED");
                        orderConfirm.setShipmemntId(nexsOrderContext.getShippingId());
                        orderConfirm.setMetaData(metaData);
                        list.add(orderConfirm);
                        nexsOrderContext.setTrayBarcode(trayBarcode);
                        if(index.get()==1){
                            nexsOrderContext.setLeftLensBarcode(orderConfirm.getSerialNumber());
                        }
                    } else {
                        isPicked = true;
                        nexsOrderContext.getOrderItemResponses().forEach(orderItemResponse -> {
                                    if (orderItemResponse.getItemType().toString().equals("LEFTLENS")) {
                                        nexsOrderContext.setLeftLensBarcode(orderItemResponse.getBarcode());
                                    }
                                }
                        );
                        //   nexsOrderContext.setBarcode(orderItemResponse.getBarcode());
                    }
                }
        );
        if (!isPicked) {
            addverbPickItemRequest.setFacilityId(nexsOrderContext.getFacilityCode());
            addverbPickItemRequest.setOrderConfirm(list);
        }
        return this;
    }

    @SneakyThrows
    @Override
    public ServiceHelper process() {
        if (!isPicked) {
            response = RestUtils.post(PICKING_ADDVERB_PICK_ITEM.getUrl(), headers,
                    JsonUtils.convertObjectToJsonString(addverbPickItemRequest), 200);
            Assert.assertEquals((String) RestUtils.getValueFromResponse(response, "meta.status"), "Success");
        }
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

    @Override
    public NexsOrderState getExpectedSourceState() {
        return IN_PICKING;
    }

    @Override
    public NexsOrderState getTargetState() {
        if (nexsOrderContext.getProcessingType().equals(ProcessingType.FR0.getCode())) {
            return PICKED;
        } else {
            return IN_TRAY;
        }
    }

    @Override
    public void executeTransition() {
        validateTransition();
        test();
    }

    @Override
    public NexsOrderContext getOrderContext() {
        return nexsOrderContext;
    }
}

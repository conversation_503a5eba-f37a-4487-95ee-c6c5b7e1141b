package com.lenskart.nexs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.GenricUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.NexsRequestBuilder;
import io.restassured.response.Response;
import lombok.SneakyThrows;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.*;

@SuperBuilder
@Slf4j
public class ConsolidationHelpers extends NexsBaseHelper implements ServiceHelper {
    NexsOrderContext nexsOrderContext;
    Response response;
    JSONObject payload;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        payload = NexsRequestBuilder.getScanItemPayloadForConsolidation(nexsOrderContext);
        return this;
    }

    @SneakyThrows
    @Override
    public ServiceHelper process() {
        try {
            response = RestUtils.post(CONSOLIDATION_SCAN_ITEM.getUrl(), headers, payload.toString(), 200);
        } catch (Exception e) {
            Thread.sleep(5000);
            response = RestUtils.post(CONSOLIDATION_SCAN_ITEM.getUrl(), headers, payload.toString(), 200);
        }
        nexsOrderContext.setBoxCode(GenricUtils.genrateRandomNumericString(5));
        log.info("Box code: {}", nexsOrderContext.getBoxCode());

        payload = NexsRequestBuilder.getAllocateItemPayloadForConsolidation(nexsOrderContext);
        response = RestUtils.put(CONSOLIDATION_ALLOCATE_ITEM.getUrl(), headers, payload.toString(), 200);

        payload = NexsRequestBuilder.getScanItemPayloadForConsolidation(nexsOrderContext);
        response = RestUtils.post(CONSOLIDATION_SCAN_ITEM_IN_PACKING.getUrl(), headers, payload.toString(), 200);
        String storeCode = RestUtils.getValueFromResponse(response, "data.packingHeaderItem.storeCode").toString();
        nexsOrderContext.setStoreCode(storeCode);
        log.info("Store code: {}", nexsOrderContext.getStoreCode());

        payload = NexsRequestBuilder.getOrderFlushPayloadForConsolidation(nexsOrderContext);
        try {
            response = RestUtils.post(CONSOLIDATION_ORDER_FLUSH.getUrl(), headers, payload.toString(), 200);
        } catch (Exception e) {
            Thread.sleep(5000);
            response = RestUtils.post(CONSOLIDATION_ORDER_FLUSH.getUrl(), headers, payload.toString(), 200);
        }
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

}

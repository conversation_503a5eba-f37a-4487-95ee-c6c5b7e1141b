package com.lenskart.nexs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.wms.response.order.OrderDetailsResponse;
import com.lenskart.nexs.wms.response.order.OrderItemResponse;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.util.List;
import java.util.Map;
import java.util.Objects;


import static com.lenskart.commons.constants.Constants.RESULT;
import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.WMS_ORDER_DETAILS_OVERVIEW;
import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.WMS_ORDER_DETAILS_WITH_ID;

@SuperBuilder
@Slf4j
public class NexsOrderDetailsHelper extends NexsBaseHelper implements ServiceHelper {

    NexsOrderContext nexsOrderContext;
    Response response;
    NexsOrderContext.Headers nexsOrderContextHeader;
    OrderDetailsResponse orderDetailsResponse;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        statusCode = nexsOrderContext.getStatusCode();
        return this;
    }

    @Override
    public ServiceHelper process() {
        if (nexsOrderContext.getShippingId().isEmpty()) {
            throw new RuntimeException("Shipping id cannot be null or empty");
        } else {
            queryParams = getQueryParams(nexsOrderContext.getShippingId());
            response = RestUtils.get(WMS_ORDER_DETAILS_OVERVIEW.getUrl(), headers, queryParams, 200);
            List<Object> shipmentLists = response.jsonPath().getList("data.shipmentLists");
            for (int i = 0; i < shipmentLists.size(); i++) {
                String shipmentId = response.jsonPath().getString("data.shipmentLists[" + i + "].shipmentId");
                if (shipmentId.equals(nexsOrderContext.getShippingId())) {
                    String awbNo = response.jsonPath().getString("data.shipmentLists[" + i + "].awbNo");
                    nexsOrderContext.setAwdNumber((awbNo == null || awbNo.trim().isEmpty()) ? "" : awbNo);
                    nexsOrderContext.setFacilityCode(response.jsonPath().getString("data.shipmentLists[" + i + "].facility"));
                    break;
                }
                nexsOrderContextHeader.setFacilityCode(nexsOrderContext.getFacilityCode());
            }

            log.info("AWB number: {}", nexsOrderContext.getAwdNumber());
            log.info("Facility code: {}", nexsOrderContext.getFacilityCode());

            response = RestUtils.get(WMS_ORDER_DETAILS_WITH_ID.getUrl(Map.of("shippingPackageId", nexsOrderContext.getShippingId())), headers, null, 200);

            orderDetailsResponse = parseResponse(RestUtils.getValueFromResponse(response, "data"), OrderDetailsResponse.class);
            nexsOrderContext.setIncrementId(String.valueOf(orderDetailsResponse.getIncrementId()));
            nexsOrderContext.setNexsOrderId(String.valueOf(orderDetailsResponse.getNexsOrderId()));
            nexsOrderContext.setIsShipToStore(orderDetailsResponse.getOrderItemHeaderResponse().getShipToStoreRequired());
            nexsOrderContext.setNavChannel(orderDetailsResponse.getNavChannel());
            nexsOrderContext.setProcessingType(String.valueOf(orderDetailsResponse.getOrderItemHeaderResponse().getOrderItemResponses().get(0).getProcessingType()));
            nexsOrderContext.setWmsOrderId(orderDetailsResponse.getOrderItemHeaderResponse().getWmsOrderCode());
            nexsOrderContext.setLegalOwner(orderDetailsResponse.getOrderItemHeaderResponse().getLegalOwner());
            List<String> itemTypes = response.jsonPath().getList("data.orderItemHeaderResponse.orderItemResponses.itemType", String.class);
            nexsOrderContext.setIsLoyaltyItemPresent(itemTypes.contains("LOYALTY"));

            nexsOrderContext.setOrderItemResponses(orderDetailsResponse.getOrderItemHeaderResponse().getOrderItemResponses());

            nexsOrderContext.setBarcode(orderDetailsResponse.getOrderItemHeaderResponse().getOrderItemResponses().get(0).getBarcode());

            log.info("Increment id: {}", nexsOrderContext.getIncrementId());
            log.info("Nexs order id: {}", nexsOrderContext.getNexsOrderId());
            log.info("Is ship to store: {}", nexsOrderContext.getIsShipToStore());
            log.info("Nav channel: {}", nexsOrderContext.getNavChannel());
            log.info("Processing type: {}", nexsOrderContext.getProcessingType());
            log.info("WMS order id: {}", nexsOrderContext.getWmsOrderId());
            log.info("Legal owner: {}", nexsOrderContext.getLegalOwner());
            log.info("Is loyalty item present: {}", nexsOrderContext.getIsLoyaltyItemPresent());
            log.info("Barcode: {}", nexsOrderContext.getBarcode());

        }

        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

}

package com.lenskart.nexs.helpers.state;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.base.BaseStateTransitionHelper;
import com.lenskart.nexs.base.StateTransitionHelper;
import com.lenskart.commons.model.NexsOrderState;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.pojo.orderqc.FetchOrderScannedEntityResponse;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;


import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.ORDERQC_FETCH_ORDER_ENTITY_TYPE;


/**
 * Helper class to transition order from PICKED to IN_QC state.
 * Handles the business logic for initiating quality control process.
 */
@SuperBuilder
@Slf4j
public class OrderQcInitiationHelper extends BaseStateTransitionHelper implements ServiceHelper, StateTransitionHelper {

    private Response response;

    @Override
    public ServiceHelper init() {
        logOperationStart("QC initiation Started");
        log.info("Barcode1: {}", nexsOrderContext.getBarcode());
        headers = getHeaders(nexsOrderContext);
        statusCode = nexsOrderContext.getStatusCode();
        return this;
    }

    @Override
    public ServiceHelper process() {
        queryParams = getQueryParamsForQC(nexsOrderContext.getBarcode());

        response = RestUtils.get(ORDERQC_FETCH_ORDER_ENTITY_TYPE.getUrl(), headers, queryParams, 200);
        statusCode = response.getStatusCode();
        nexsOrderContext.setFetchOrderScannedEntityResponse(parseResponse(RestUtils.getValueFromResponse(response, "data"), FetchOrderScannedEntityResponse.class));
        String unicomOrderCode = nexsOrderContext.getFetchOrderScannedEntityResponse().getInfoForQCPanel().getOrderResponse().getUnicomOrdercode();
        nexsOrderContext.setUnicomOrderCode(unicomOrderCode);
        logOperationComplete("QC initiation Completion");
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

    @Override
    public NexsOrderState getExpectedSourceState() {
        return NexsOrderState.CUSTOMIZATION_COMPLETE;
    }

    @Override
    public NexsOrderState getTargetState() {
        return NexsOrderState.IN_QC;
    }


    @Override
    public void executeTransition() {
        validateTransition();
        test();

    }

    @Override
    public NexsOrderContext getOrderContext() {
        return nexsOrderContext;
    }
}

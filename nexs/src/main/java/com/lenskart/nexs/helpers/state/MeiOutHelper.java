package com.lenskart.nexs.helpers.state;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.NexsOrderState;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.base.BaseStateTransitionHelper;
import com.lenskart.nexs.base.StateTransitionHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.NexsRequestBuilder;
import com.lenskart.nexs.database.WMSDbUtils;
import com.lenskart.nexs.validator.wms.MeiOutValidator;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import static com.lenskart.commons.model.NexsOrderState.EDGING;
import static com.lenskart.nexs.constants.Constants.UAE_LEGAL_OWNER;
import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.WMS_HAND_EDGING_SCAN;
import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.WMS_LOCATION_SCAN;

@SuperBuilder
@Slf4j
public class MeiOutHelper extends BaseStateTransitionHelper implements ServiceHelper, StateTransitionHelper {
    @Getter
    Response response;
    JSONObject payload;

    @Override
    public ServiceHelper init() {
        logOperationStart("Mein Out initiation");
        headers = getHeaders(nexsOrderContext);
        payload = NexsRequestBuilder.getMeiPayload(nexsOrderContext, "MEI_EXIT");
        return this;
    }

    @Override
    public ServiceHelper process() {
        if (WMSDbUtils.getStausBasedOnBarcode(nexsOrderContext, nexsOrderContext.getLeftLensBarcode()).equals(EDGING.name())) {
            if (nexsOrderContext.getLegalOwner().equals(UAE_LEGAL_OWNER))
                response = RestUtils.post(WMS_HAND_EDGING_SCAN.getUrl(), headers, payload.toString(), 200);
            else
                response = RestUtils.post(WMS_LOCATION_SCAN.getUrl(), headers, payload.toString(), 200);
        } else {
            log.info("Shipment is already moved further");
        }
        logOperationComplete("Mein Out initiation");
        return this;
    }

    @Override
    public ServiceHelper validate() {
        MeiOutValidator validator = MeiOutValidator.builder().nexsOrderContext(nexsOrderContext).meiOutHelper(this).build();
        validator.validateNode();
        validator.validateDBEntities();
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

    @Override
    public NexsOrderState getExpectedSourceState() {
        return EDGING;
    }

    @Override
    public NexsOrderState getTargetState() {
        return NexsOrderState.PENDING_CUSTOMIZATION;
    }

    @Override
    public void executeTransition() {
        validateTransition();
        test();
    }

    @Override
    public NexsOrderContext getOrderContext() {
        return nexsOrderContext;
    }
}
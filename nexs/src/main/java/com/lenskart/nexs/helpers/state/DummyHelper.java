package com.lenskart.nexs.helpers.state;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.NexsOrderState;
import com.lenskart.commons.utils.GenericUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.base.BaseStateTransitionHelper;
import com.lenskart.nexs.base.StateTransitionHelper;
import com.lenskart.nexs.database.WMSDbUtils;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.NexsRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import static com.lenskart.commons.model.NexsOrderState.PICKED;
import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.WMS_CREATE_TRAY;

/**
 * Helper class to transition order from PICKED to IN_TRAY Status.
 * Handles the business logic for completing the picking process.
 */

@SuperBuilder
@Slf4j
public class DummyHelper extends BaseStateTransitionHelper implements ServiceHelper, StateTransitionHelper {


    @Override
    public ServiceHelper init() {
        return this;
    }

    @Override
    public ServiceHelper process() {
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

    @Override
    public NexsOrderState getExpectedSourceState() {
        return PICKED;
    }

    @Override
    public NexsOrderState getTargetState() {
        return NexsOrderState.IN_TRAY;
    }

    @Override
    public void executeTransition() {
        validateTransition();
        test();
    }

    @Override
    public NexsOrderContext getOrderContext() {
        return nexsOrderContext;
    }

}

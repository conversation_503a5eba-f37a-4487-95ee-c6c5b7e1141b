package com.lenskart.nexs.helpers.state;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.nexs.base.BaseStateTransitionHelper;
import com.lenskart.nexs.base.StateTransitionHelper;
import com.lenskart.commons.model.NexsOrderState;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

/**
 * Helper class to transition order from INVOICED to DISPATCHED state.
 * Handles the business logic for dispatching the order.
 */
@SuperBuilder
@Slf4j
public class DispatchHelper extends BaseStateTransitionHelper implements ServiceHelper, StateTransitionHelper {

    private Response response;
    private JSONObject payload;

    @Override
    public ServiceHelper init() {
        logOperationStart("Dispatch initiation");

        // Initialize headers with authentication
        headers = getHeadersWithAuth(orderContext);

        // Prepare payload for dispatch
//        payload = add code to create payload

        logOperationComplete("Dispatch initiation");
        return this;
    }

    @Override
    public ServiceHelper process() {
        logOperationStart("Order dispatch process");

        try {
            // Call API to initiate dispatch

            logOperationComplete("Order dispatch process");

        } catch (Exception e) {
            logOperationError("Order dispatch process", e.getMessage());
            throw new RuntimeException("Failed to dispatch order", e);
        }

        return this;
    }

    @Override
    public ServiceHelper validate() {
        logOperationStart("Dispatch validation");

        try {
            // Validate response
            if (response == null) {
                throw new IllegalStateException("No response received from dispatch API");
            }

            // Validate response status
            if (response.getStatusCode() != 200) {
                throw new IllegalStateException("Dispatch API returned error: " + response.getStatusCode());
            }

            // Add business validation

            logOperationComplete("Dispatch validation");

        } catch (Exception e) {
            logOperationError("Dispatch validation", e.getMessage());
            throw new RuntimeException("Dispatch validation failed", e);
        }

        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

    @Override
    public NexsOrderState getExpectedSourceState() {
        return NexsOrderState.INVOICED;
    }

    @Override
    public NexsOrderState getTargetState() {
        return NexsOrderState.DISPATCHED;
    }


    @Override
    public void executeTransition() {
        validateTransition();
        test();

    }

    @Override
    public NexsOrderContext getOrderContext() {
        return orderContext;
    }

}

package com.lenskart.nexs.helpers.state;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.base.BaseStateTransitionHelper;
import com.lenskart.nexs.base.StateTransitionHelper;
import com.lenskart.commons.model.NexsOrderState;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.database.WMSDbUtils;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.util.Map;

import static com.lenskart.commons.model.NexsOrderState.DISPATCHED;
import static com.lenskart.commons.model.NexsOrderState.READY_TO_SHIP;
import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.MANIFEST_CLOSE;
import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.MANIFEST_FETCH_MANIFEST;

/**
 * Helper class to transition order from INVOICED to DISPATCHED state.
 * Handles the business logic for dispatching the order.
 */
@SuperBuilder
@Slf4j
public class DispatchHelper extends BaseStateTransitionHelper implements ServiceHelper, StateTransitionHelper {

    Response response;
    JSONObject payload;
    String shippingProvider;

    @Override
    public ServiceHelper init() {
        logOperationStart("Dispatch initiation");
        headers = getHeaders(nexsOrderContext);
        return this;
    }


    @Override
    public ServiceHelper process() {
        logOperationStart("Dispatch process");

        if (WMSDbUtils.getStatusOfShipment(nexsOrderContext).equals(READY_TO_SHIP.name())) {
            response = RestUtils.get(MANIFEST_FETCH_MANIFEST.getUrl(Map.of("manifestNumber", nexsOrderContext.getManifestId())),
                    headers, null, 200);
            AwaitUtils.sleepSeconds(3);
            response = RestUtils.post(MANIFEST_CLOSE.getUrl(Map.of("manifestNumber", nexsOrderContext.getManifestId())),
                    headers, null, 200);
        } else if (WMSDbUtils.getStatusOfShipment(nexsOrderContext).equals(DISPATCHED.name())) {
            log.info("Shipment is already dispatched");
        } else {
            log.info("Shipment status {}", WMSDbUtils.getStatusOfShipment(nexsOrderContext));
        }
        logOperationComplete("Dispatch Complete");
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

    @Override
    public NexsOrderState getExpectedSourceState() {
        return NexsOrderState.READY_TO_SHIP;
    }

    @Override
    public NexsOrderState getTargetState() {
        return DISPATCHED;
    }


    @Override
    public void executeTransition() {
        validateTransition();
        test();

    }

    @Override
    public NexsOrderContext getOrderContext() {
        return nexsOrderContext;
    }

}

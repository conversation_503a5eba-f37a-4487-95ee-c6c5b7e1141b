package com.lenskart.nexs.helpers;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.model.ProcessingType;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.nexs.helpers.state.*;

import com.lenskart.nexs.model.NexsOrderContext;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@SuperBuilder
@Slf4j
public class NexsOrderCompletionHelper extends NexsBaseHelper implements ExecutionHelper {
    NexsOrderContext nexsOrderContext;
    OrderContext orderContext;

    @Override
    public ExecutionHelper init() {
        return this;
    }

    @Override
    public ExecutionHelper orchestrateFlow() {
        /* Authenticate User */
        NexsAuthHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        /* Get Shipments */
        GetShipmentsHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        if (nexsOrderContext.getShipmentIds() != null && !nexsOrderContext.getShipmentIds().isEmpty()) {
            log.info("Processing {} shipments: {}", nexsOrderContext.getShipmentIds().size(), nexsOrderContext.getShipmentIds());

            // Process each shipment
            for (String shipmentId : nexsOrderContext.getShipmentIds()) {
                log.info("Processing shipment ID: {}", shipmentId);

                // Set the current shipment ID
                nexsOrderContext.setShippingId(shipmentId);

                // Process this shipment
                processShipment();
            }
        }  else {
            log.error("No shipment IDs found to process");
        }

        /* Logout User */
        NexsLogoutHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        return this;
    }

    /**
     * Process a single shipment with the current shipment ID in the context
     */
    private void processShipment() {
        /* Order Details */
        NexsOrderDetailsHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        /* Picking Completion */
        PickingCompletionHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        if (nexsOrderContext.getProcessingType().equals(ProcessingType.FR1.getCode()) ||
                nexsOrderContext.getProcessingType().equals(ProcessingType.FR2.getCode())) {
            /* Tray Making */
            TrayMakingHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
            /* MEI In */
            MeiInHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
            /* MEI Out */
            MeiOutHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
            /* Fitting */
            FittingHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
        }

        /* QC Initiation */
        OrderQcInitiationHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        /* QC Completion */
        OrderQcCompletionHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        /* Store Consolidation and Store Packing */
        if (nexsOrderContext.getIsShipToStore() && !nexsOrderContext.getNavChannel().contains("Bulk")) {
            ConsolidationHelpers.builder().nexsOrderContext(nexsOrderContext).build().test();
        }

        /* Packing Details */
        PackingDetailsHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        /* Packing Completion */
        PackingCompletionHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        /* Print Invoice */
        if (nexsOrderContext.getNavChannel().contains("DTC") || nexsOrderContext.getNavChannel().contains("Bulk"))
            PrintInvoiceHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        /* Print Shipment */
        PrintShipmentHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        /* Manifest */
        ManifestHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        List<OrderContext.ProductList> productLists = orderContext.getProductLists();
        OrderContext.ProductList product = productLists.get(0);
        product.setShippingPackageId(nexsOrderContext.getShippingId());
        product.setFacilityCode(nexsOrderContext.getFacilityCode());

        log.info("Product details are {}", JsonUtils.convertObjectToJsonString(orderContext.getProductLists()));

    }

    @Override
    public ExecutionHelper test() {
        init();
        orchestrateFlow();
        return this;
    }
}

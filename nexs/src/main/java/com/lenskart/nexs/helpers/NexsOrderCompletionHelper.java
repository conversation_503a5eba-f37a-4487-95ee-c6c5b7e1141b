package com.lenskart.nexs.helpers;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.commons.model.ProcessingType;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.helpers.consolidation.ConsolidationHelpers;
import com.lenskart.nexs.helpers.lkauth.NexsAuthHelper;
import com.lenskart.nexs.helpers.lkauth.NexsLogoutHelper;
import com.lenskart.nexs.helpers.packing.PackingCompletionHelper;
import com.lenskart.nexs.helpers.packing.PackingDetailsHelper;
import com.lenskart.nexs.helpers.state.*;

import com.lenskart.nexs.helpers.wms.GetShipmentsHelper;
import com.lenskart.nexs.helpers.wms.NexsOrderDetailsHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

@SuperBuilder
@Slf4j
public class NexsOrderCompletionHelper extends NexsBaseHelper implements ExecutionHelper {
    NexsOrderContext nexsOrderContext;

    @Override
    public ExecutionHelper init() {
        return this;
    }

    @Override
    public ExecutionHelper orchestrateFlow() {
        /* Authenticate User */
        NexsAuthHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        /* Get Shipments */
        GetShipmentsHelper.builder().nexsOrderContext(nexsOrderContext).build().test();


        if (nexsOrderContext.getShipmentIds() != null && !nexsOrderContext.getShipmentIds().isEmpty()) {
            log.info("Processing {} shipments: {}", nexsOrderContext.getShipmentIds().size(), nexsOrderContext.getShipmentIds());

            // Process each shipment
            for (String shipmentId : nexsOrderContext.getShipmentIds()) {
                log.info("Processing shipment ID: {}", shipmentId);

                // Set the current shipment ID
                nexsOrderContext.setShippingId(shipmentId);

                // Process this shipment
                processShipment();
            }
        } else if (nexsOrderContext.getShippingId() != null) {
            processShipment();
        } else {
            log.error("No shipment IDs found to process");
        }

        /* Logout User */
        NexsLogoutHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        return this;
    }

    /**
     * Process a single shipment with the current shipment ID in the context
     */
    private void processShipment() {
        /* Order Details */
        NexsOrderDetailsHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        if (nexsOrderContext.getIsAddverbPicking())
            AddverbPickingHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
        else
            PickingCompletionHelper.builder().nexsOrderContext(nexsOrderContext).build().test();


        if (nexsOrderContext.getProcessingType().equals(ProcessingType.FR1.getCode()) ||
                nexsOrderContext.getProcessingType().equals(ProcessingType.FR2.getCode())) {
            /* Tray Making */
            TrayMakingHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
            /* MEI In */
            MeiInHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
            /* MEI Out */
            MeiOutHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
            /* Fitting */
            FittingHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
        }

        /* QC Initiation */
        OrderQcInitiationHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        /* QC Completion */
        OrderQcCompletionHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        /* Store Consolidation and Store Packing */
        if (nexsOrderContext.getIsShipToStore() && !nexsOrderContext.getNavChannel().contains(Constants.BULK)) {
            ConsolidationHelpers.builder().nexsOrderContext(nexsOrderContext).build().test();
        }

        /* Packing Details */
        PackingDetailsHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        /* Packing Completion */
        PackingCompletionHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        /* Print Invoice */
        if (nexsOrderContext.getNavChannel().contains(Constants.DTC) || nexsOrderContext.getNavChannel().contains(Constants.BULK))
            PrintInvoiceHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        /* Print Shipment */
        PrintShipmentHelper.builder().nexsOrderContext(nexsOrderContext).build().test();

        /* Manifest */
        ManifestHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
    }

    @Override
    public ExecutionHelper test() {
        init();
        orchestrateFlow();
        return this;
    }
}

package com.lenskart.nexs.helpers;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.nexs.helpers.state.InvoiceGenerationHelper;
import com.lenskart.nexs.helpers.state.PickingCompletionHelper;
import com.lenskart.nexs.helpers.state.QualityControlCompletionHelper;
import com.lenskart.nexs.helpers.state.QualityControlInitiationHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

@SuperBuilder
@Slf4j
public class NexsOrderCompletionHelper extends NexsBaseHelper implements ExecutionHelper {
    NexsOrderContext orderContext;

    @Override
    public ExecutionHelper init() {
        return this;
    }

    @Override
    public ExecutionHelper orchestrateFlow() {
        /* Authenticate User */
        NexsAuthHelper.builder().nexsOrderContext(orderContext).build().test();

        /* Get Shipments */
        GetShipmentsHelper.builder().orderContext(orderContext).build().test();

        if (orderContext.getShipmentIds() != null && !orderContext.getShipmentIds().isEmpty()) {
            log.info("Processing {} shipments: {}", orderContext.getShipmentIds().size(), orderContext.getShipmentIds());

            // Process each shipment
            for (String shipmentId : orderContext.getShipmentIds()) {
                log.info("Processing shipment ID: {}", shipmentId);

                // Set the current shipment ID
                orderContext.setShippingId(shipmentId);

                // Process this shipment
                processShipment();
            }
        } else if (orderContext.getShippingId() != null && !orderContext.getShippingId().isEmpty()) {
            log.info("Processing single shipment ID: {}", orderContext.getShippingId());

            // Process the single shipment
            processShipment();
        } else {
            log.error("No shipment IDs found to process");
        }

        /* Logout User */
        NexsLogoutHelper.builder().nexsOrderContext(orderContext).build().test();

        return this;
    }

    /**
     * Process a single shipment with the current shipment ID in the context
     */
    private void processShipment() {
        /* Order Details */
        NexsOrderDetailsHelper.builder().nexsOrderContext(orderContext).build().test();

        /* Picking Completion */
        PickingCompletionHelper.builder().nexsOrderContext(orderContext).build().test();

        /* QC Initiation */
        QualityControlInitiationHelper.builder().nexsOrderContext(orderContext).build().test();

        /* QC Completion */
        QualityControlCompletionHelper.builder().nexsOrderContext(orderContext).build().test();

        /* Packing Details */
        PackingDetailsHelper.builder().nexsOrderContext(orderContext).build().test();

        /* Packing Completion */
        PackingCompletionHelper.builder().nexsOrderContext(orderContext).build().test();

        /* Print Invoice */
        PrintInvoiceHelper.builder().orderContext(orderContext).build().test();

        /* Print Shipment */
        PrintShipmentHelper.builder().orderContext(orderContext).build().test();

        /* Manifest */
        ManifestHelper.builder().orderContext(orderContext).build().test();
    }

    @Override
    public ExecutionHelper test() {
        init();
        orchestrateFlow();
        return this;
    }
}

package com.lenskart.nexs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.GenricUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.requestBuilder.NexsRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.WMS_CREATE_TRAY;

@SuperBuilder
@Slf4j
public class TrayMakingHelper extends NexsBaseHelper implements ServiceHelper {
    NexsOrderContext orderContext;
    Response response;
    JSONObject payload;
    String trayBarcode;
    String frameBarcode;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(orderContext);
        payload = NexsRequestBuilder.getCreateTrayPayload(orderContext,frameBarcode,trayBarcode);
        return this;
    }

    @Override
    public ServiceHelper process() {
        String trayBarcode = "T" + GenricUtils.genrateRandomNumericString(5);
        orderContext.setTrayBarcode(trayBarcode);
        response = RestUtils.post(WMS_CREATE_TRAY.getUrl(), headers, null, 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

}

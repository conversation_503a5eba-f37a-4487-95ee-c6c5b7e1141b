package com.lenskart.nexs.helpers.state;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.NexsOrderState;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.base.BaseStateTransitionHelper;
import com.lenskart.nexs.base.StateTransitionHelper;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.SneakyThrows;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.testng.Assert;

import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.WMS_EXPORT_PDF;

@SuperBuilder
@Slf4j
public class PrintInvoiceHelper extends BaseStateTransitionHelper implements ServiceHelper, StateTransitionHelper {
    NexsOrderContext nexsOrderContext;
    Response response;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        return this;
    }

    @SneakyThrows
    @Override
    public ServiceHelper process() {
        queryParams = getQueryParamsForInvoice(nexsOrderContext.getShippingId());
        response = RestUtils.get(WMS_EXPORT_PDF.getUrl(), headers, queryParams);
        try {
            Assert.assertEquals(response.statusCode(), HttpStatus.SC_OK);
            AwaitUtils.sleepSeconds(5);
        } catch (AssertionError e) {
            AwaitUtils.sleepSeconds(5);
            response = RestUtils.get(WMS_EXPORT_PDF.getUrl(), headers, queryParams);
            try {
                Assert.assertEquals(response.statusCode(), HttpStatus.SC_OK);
            } catch (AssertionError ee) {
                AwaitUtils.sleepSeconds(5);
                response = RestUtils.get(WMS_EXPORT_PDF.getUrl(), headers, queryParams);
                try {
                    Assert.assertEquals(response.statusCode(), HttpStatus.SC_OK);
                } catch (AssertionError eee) {
                    response = RestUtils.get(WMS_EXPORT_PDF.getUrl(), headers, queryParams);
                    Assert.assertEquals(response.statusCode(), HttpStatus.SC_OK);
                }
            }
        }
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

    @Override
    public NexsOrderState getExpectedSourceState() {
        return NexsOrderState.QC_DONE;
    }

    @Override
    public NexsOrderState getTargetState() {
        return NexsOrderState.INVOICED;
    }

    @Override
    public void executeTransition() {
        validateTransition();
        test();
    }

    @Override
    public NexsOrderContext getOrderContext() {
        return nexsOrderContext;
    }
}

package com.lenskart.nexs.helpers.state;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.NexsOrderState;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.base.BaseStateTransitionHelper;
import com.lenskart.nexs.base.StateTransitionHelper;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import io.restassured.response.Response;
import lombok.SneakyThrows;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpStatus;
import org.testng.Assert;

import java.time.Duration;

import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.WMS_EXPORT_PDF;

@SuperBuilder
@Slf4j
public class PrintInvoiceHelper extends BaseStateTransitionHelper implements ServiceHelper, StateTransitionHelper {

    Response response;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(nexsOrderContext);
        return this;
    }

    @SneakyThrows
    @Override
    public ServiceHelper process() {
        queryParams = getQueryParamsForInvoice(nexsOrderContext.getShippingId());

        // Use AwaitUtils polling approach to wait for successful invoice PDF export
        boolean success = AwaitUtils.waitUntil(
            () -> {
                try {
                    log.debug("Attempting to export invoice PDF for shipping ID: {}", nexsOrderContext.getShippingId());
                    response = RestUtils.get(WMS_EXPORT_PDF.getUrl(), headers, queryParams);

                    if (response.statusCode() == HttpStatus.SC_OK) {
                        log.info("✅ Invoice PDF export successful for shipping ID: {}", nexsOrderContext.getShippingId());
                        return true;
                    } else {
                        log.warn("Invoice PDF export failed with status: {} for shipping ID: {}",
                                response.statusCode(), nexsOrderContext.getShippingId());
                        return false;
                    }
                } catch (Exception e) {
                    log.warn("Exception during invoice PDF export attempt: {}", e.getMessage());
                    return false;
                }
            },
            "Invoice PDF export to be successful",
            Duration.ofMinutes(1),  // 2 minute timeout
            Duration.ofSeconds(5)   // 5 second poll interval
        );

        if (!success) {
            log.error("❌ Invoice PDF export failed after polling timeout for shipping ID: {}",
                    nexsOrderContext.getShippingId());
            Assert.fail("Invoice PDF export failed after multiple attempts for shipping ID: " +
                       nexsOrderContext.getShippingId());
        }
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

    @Override
    public NexsOrderState getExpectedSourceState() {
        return NexsOrderState.QC_DONE;
    }

    @Override
    public NexsOrderState getTargetState() {
        return NexsOrderState.INVOICED;
    }

    @Override
    public void executeTransition() {
        validateTransition();
        test();
    }

    @Override
    public NexsOrderContext getOrderContext() {
        return nexsOrderContext;
    }
}

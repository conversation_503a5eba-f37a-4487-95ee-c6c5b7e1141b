package com.lenskart.nexs.manager;

import com.lenskart.commons.model.ProcessingType;
import com.lenskart.nexs.base.StateTransitionHelper;
import com.lenskart.commons.model.NexsOrderState;
import com.lenskart.nexs.helpers.state.MeiInHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.helpers.state.*;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;

import static com.lenskart.commons.model.NexsOrderState.IN_QC;
import static com.lenskart.commons.model.NexsOrderState.PICKED;

/**
 * Manager class responsible for handling order state transitions.
 * Maintains mapping between states and their corresponding helper classes.
 */
@Slf4j
public class OrderStateTransitionManager {

    // Mapping of state transitions to their corresponding helper classes
    private static final Map<StateTransition, Function<NexsOrderContext, StateTransitionHelper>> FR1_TRANSITION_HELPERS = new HashMap<>();

    // Mapping of state transitions to their corresponding helper classes
    private static final Map<StateTransition, Function<NexsOrderContext, StateTransitionHelper>> FR0_TRANSITION_HELPERS = new HashMap<>();


    static {
        // Initialize state transition mappings
        initializeTransitionMappings();
        initializeFR0TransitionMappings();
    }

    /**
     * Initialize the mapping between state transitions and helper classes
     */
    private static void initializeTransitionMappings() {

        // IN_PICKING -> PICKED
        FR1_TRANSITION_HELPERS.put(
            new StateTransition(NexsOrderState.IN_PICKING, PICKED),
            orderContext -> PickingCompletionHelper.builder().nexsOrderContext(orderContext).build()
        );

        // PICKED -> IN_TRAY - FR1
        FR1_TRANSITION_HELPERS.put(
                new StateTransition(PICKED, NexsOrderState.IN_TRAY),
                orderContext -> TrayMakingHelper.builder().nexsOrderContext(orderContext).build()
        );

        // IN_TRAY -> EDGING - FR1
        FR1_TRANSITION_HELPERS.put(
            new StateTransition(NexsOrderState.IN_TRAY, NexsOrderState.EDGING),
            orderContext -> MeiInHelper.builder().nexsOrderContext(orderContext).build()
        );

        // EDGING -> PENDING_CUSTOMIZATION - FR1
        FR1_TRANSITION_HELPERS.put(
            new StateTransition(NexsOrderState.EDGING, NexsOrderState.PENDING_CUSTOMIZATION),
            orderContext -> MeiOutHelper.builder().nexsOrderContext(orderContext).build()
        );

        // PENDING_CUSTOMIZATION -> CUSTOMIZATION_COMPLETE - FR1
        FR1_TRANSITION_HELPERS.put(
            new StateTransition(NexsOrderState.PENDING_CUSTOMIZATION, NexsOrderState.CUSTOMIZATION_COMPLETE),
            orderContext -> FittingHelper.builder().nexsOrderContext(orderContext).build()
        );

        // CUSTOMIZATION_COMPLETE -> IN_QC
        FR1_TRANSITION_HELPERS.put(
            new StateTransition(NexsOrderState.CUSTOMIZATION_COMPLETE, IN_QC),
            orderContext -> OrderQcInitiationHelper.builder().nexsOrderContext(orderContext).build()
        );

        // IN_QC -> QC_DONE
        FR1_TRANSITION_HELPERS.put(
                new StateTransition(IN_QC, NexsOrderState.QC_DONE),
                orderContext -> OrderQcCompletionHelper.builder().nexsOrderContext(orderContext).build()
        );

        // QC_DONE -> INVOICED
        FR1_TRANSITION_HELPERS.put(
                new StateTransition(NexsOrderState.QC_DONE, NexsOrderState.INVOICED),
                orderContext -> PrintInvoiceHelper.builder().nexsOrderContext(orderContext).build()
        );

        // INVOICED -> AWB_CREATED
        FR1_TRANSITION_HELPERS.put(
                new StateTransition(NexsOrderState.INVOICED, NexsOrderState.AWB_CREATED),
                orderContext -> PrintShipmentHelper.builder().nexsOrderContext(orderContext).build()
        );

        // AWB_CREATED -> READY_TO_SHIP
        FR1_TRANSITION_HELPERS.put(
                new StateTransition(NexsOrderState.AWB_CREATED, NexsOrderState.READY_TO_SHIP),
                orderContext -> ManifestHelper.builder().nexsOrderContext(orderContext).build()
        );

        // READY_TO_SHIP -> DISPATCHED
        FR1_TRANSITION_HELPERS.put(
                new StateTransition(NexsOrderState.READY_TO_SHIP, NexsOrderState.DISPATCHED),
                orderContext -> DispatchHelper.builder().nexsOrderContext(orderContext).build()
        );
    }


    /**
     * Initialize the mapping between state transitions and helper classes
     */
    private static void initializeFR0TransitionMappings() {

        // IN_PICKING -> PICKED
        FR0_TRANSITION_HELPERS.put(
                new StateTransition(NexsOrderState.IN_PICKING, PICKED),
                orderContext -> PickingCompletionHelper.builder().nexsOrderContext(orderContext).build()
        );


        // PICKED -> IN_QC
        FR0_TRANSITION_HELPERS.put(
                new StateTransition(PICKED, IN_QC),
                orderContext -> OrderQcInitiationHelper.builder().nexsOrderContext(orderContext).build()
        );

        // IN_QC -> QC_DONE
        FR0_TRANSITION_HELPERS.put(
                new StateTransition(IN_QC, NexsOrderState.QC_DONE),
                orderContext -> OrderQcCompletionHelper.builder().nexsOrderContext(orderContext).build()
        );

        // QC_DONE -> INVOICED
        FR0_TRANSITION_HELPERS.put(
                new StateTransition(NexsOrderState.QC_DONE, NexsOrderState.INVOICED),
                orderContext -> PrintInvoiceHelper.builder().nexsOrderContext(orderContext).build()
        );

        // INVOICED -> AWB_CREATED
        FR0_TRANSITION_HELPERS.put(
                new StateTransition(NexsOrderState.INVOICED, NexsOrderState.AWB_CREATED),
                orderContext -> PrintShipmentHelper.builder().nexsOrderContext(orderContext).build()
        );

        // AWB_CREATED -> READY_TO_SHIP
        FR0_TRANSITION_HELPERS.put(
                new StateTransition(NexsOrderState.AWB_CREATED, NexsOrderState.READY_TO_SHIP),
                orderContext -> ManifestHelper.builder().nexsOrderContext(orderContext).build()
        );

        // READY_TO_SHIP -> DISPATCHED
        FR0_TRANSITION_HELPERS.put(
                new StateTransition(NexsOrderState.READY_TO_SHIP, NexsOrderState.DISPATCHED),
                orderContext -> DispatchHelper.builder().nexsOrderContext(orderContext).build()
        );
    }

    /**
     * Transition an order from current state to the next state
     *
     * @param orderContext The order context containing current state
     * @return Updated order context with new state
     * @throws IllegalStateException if transition is not allowed
     * @throws RuntimeException if transition fails
     */
    public static NexsOrderContext transitionToNextState(NexsOrderContext orderContext) {
        validateOrderContext(orderContext);
        Optional<NexsOrderState> nextState;

        NexsOrderState currentState = orderContext.getCurrentState();
        if (currentState == PICKED && orderContext.getProcessingType().equals(ProcessingType.FR0.getCode())) {
            nextState = Optional.of(IN_QC);
        } else {
            nextState = currentState.getNextState();
        }
        if (nextState.isEmpty()) {
            throw new IllegalStateException("Order is already in final state: " + currentState.getDisplayName());
        }

        return transitionToState(orderContext, nextState.get());
    }

    /**
     * Transition an order to a specific target state
     *
     * @param orderContext The order context
     * @param targetState The target state to transition to
     * @return Updated order context with new state
     * @throws IllegalStateException if transition is not allowed
     * @throws RuntimeException if transition fails
     */
    public static NexsOrderContext transitionToState(NexsOrderContext orderContext, NexsOrderState targetState) {

        validateTargetState(targetState);

        NexsOrderState currentState = orderContext.getCurrentState();

        if (currentState == targetState) {
            log.info("Order {} is already in state: {}", orderContext.getIncrementId(), targetState.getDisplayName());
            return orderContext;
        }

        // this has been commented out to handle FR0 Orders
//        if (!currentState.canTransitionTo(targetState)) {
//            throw new IllegalStateException(
//                String.format("Invalid state transition from %s to %s for order %s",
//                    currentState.getDisplayName(), targetState.getDisplayName(), orderContext.getIncrementId())
//            );
//        }

        log.info("Transitioning order {} and shipment id {}from {} to {}",
            orderContext.getIncrementId(), orderContext.getShippingId(), currentState.getDisplayName(), targetState.getDisplayName());

        try {
            StateTransition transition = new StateTransition(currentState, targetState);
            StateTransitionHelper helper = getTransitionHelper(transition, orderContext);

            // Execute the transition
            helper.executeTransition();

            // Update the order context with new state
            orderContext.setCurrentState(targetState);
            orderContext.addStateTransitionHistory(currentState, targetState);

            log.info("Successfully transitioned order {}  with shipment Id {} to state: {}",
                orderContext.getIncrementId(), orderContext.getShippingId(), targetState.getDisplayName());

            return orderContext;

        } catch (Exception e) {
            log.error("Failed to transition order {} with shipment Id {} from {} to {}: {}",
                orderContext.getIncrementId(), orderContext.getShippingId(), currentState.getDisplayName(), targetState.getDisplayName(), e.getMessage());
            throw new RuntimeException("State transition failed", e);
        }
    }

    /**
     * Transition an order through multiple states to reach the target state
     *
     * @param orderContext The order context
     * @param targetState The final target state
     * @return Updated order context with new state
     */
    public static NexsOrderContext transitionToStateSequentially(NexsOrderContext orderContext, NexsOrderState targetState) {

        validateTargetState(targetState);

        NexsOrderState currentState = orderContext.getCurrentState();

        if (currentState.getSequence() >= targetState.getSequence()) {
            throw new IllegalStateException(
                String.format("Cannot transition backwards or to same state. Current: %s, Target: %s",
                    currentState.getDisplayName(), targetState.getDisplayName())
            );
        }

        log.info("Starting sequential transition for order {} with shipment id {} from {} to {}",
            orderContext.getIncrementId(), orderContext.getShippingId(), currentState.getDisplayName(), targetState.getDisplayName());

        NexsOrderContext updatedContext = orderContext;

        while (updatedContext.getCurrentState() != targetState) {
            updatedContext = transitionToNextState(updatedContext);
        }

        log.info("Completed sequential transition for order {}  with shipment Id {} to state: {}",
            orderContext.getIncrementId(), orderContext.getShippingId(), targetState.getDisplayName());

        return updatedContext;
    }

    /**
     * Get the appropriate helper for a state transition
     *
     * @param transition The state transition
     * @param orderContext The order context
     * @return The helper instance for the transition
     */
    private static StateTransitionHelper getTransitionHelper(StateTransition transition, NexsOrderContext orderContext) {
        Function<NexsOrderContext, StateTransitionHelper> helperFactory = null;

        if (orderContext.getProcessingType().equals(ProcessingType.FR1.getCode()) ||
                orderContext.getProcessingType().equals(ProcessingType.FR2.getCode())) {
            helperFactory = FR1_TRANSITION_HELPERS.get(transition);
        }else {
            helperFactory = FR0_TRANSITION_HELPERS.get(transition);
        }

        if (helperFactory == null) {
            throw new IllegalStateException("No helper found for transition: " + transition);
        }

        return helperFactory.apply(orderContext);
    }

    /**
     * Validate order context
     *
     * @param orderContext The order context to validate
     */
    private static void validateOrderContext(NexsOrderContext orderContext) {
        if (orderContext == null) {
            throw new IllegalArgumentException("Order context cannot be null");
        }

        if (orderContext.getIncrementId() == null || orderContext.getIncrementId().trim().isEmpty()) {
            throw new IllegalArgumentException("Order ID cannot be null or empty");
        }

        if (orderContext.getCurrentState() == null) {
            throw new IllegalArgumentException("Current state cannot be null");
        }
    }

    /**
     * Validate target state
     *
     * @param targetState The target state to validate
     */
    private static void validateTargetState(NexsOrderState targetState) {
        if (targetState == null) {
            throw new IllegalArgumentException("Target state cannot be null");
        }
    }

    /**
     * Check if a transition is supported
     *
     * @param fromState The source state
     * @param toState The target state
     * @return true if transition is supported
     */
    public static boolean isTransitionSupported(NexsOrderState fromState, NexsOrderState toState) {
        if (fromState == null || toState == null) {
            return false;
        }

        StateTransition transition = new StateTransition(fromState, toState);
        return FR1_TRANSITION_HELPERS.containsKey(transition);
    }

    /**
     * Inner class representing a state transition
     */
    private static class StateTransition {
        private final NexsOrderState fromState;
        private final NexsOrderState toState;

        public StateTransition(NexsOrderState fromState, NexsOrderState toState) {
            this.fromState = fromState;
            this.toState = toState;
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            StateTransition that = (StateTransition) obj;
            return fromState == that.fromState && toState == that.toState;
        }

        @Override
        public int hashCode() {
            return java.util.Objects.hash(fromState, toState);
        }

        @Override
        public String toString() {
            return fromState.getDisplayName() + " -> " + toState.getDisplayName();
        }
    }
}

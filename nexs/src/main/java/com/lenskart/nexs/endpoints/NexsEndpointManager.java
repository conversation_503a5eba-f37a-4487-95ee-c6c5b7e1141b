package com.lenskart.nexs.endpoints;

import com.lenskart.commons.endpoints.EndpointManager;
import com.lenskart.nexs.config.NexsConfigProvider;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * Endpoint manager for NEXS module that provides URL generation
 * and management for all NEXS endpoints.
 */
@Slf4j
public class NexsEndpointManager extends EndpointManager<NexsOrderProcessingEndpoints> {
    
    // Singleton instance
    private static volatile NexsEndpointManager instance;
    
    /**
     * Private constructor for singleton pattern
     */
    private NexsEndpointManager() {
        super(NexsConfigProvider.getInstance(), NexsOrderProcessingEndpoints.class);
        log.info("NexsEndpointManager initialized");
    }
    
    /**
     * Gets the singleton instance of NexsEndpointManager
     *
     * @return The singleton instance
     */
    public static NexsEndpointManager getInstance() {
        if (instance == null) {
            synchronized (NexsEndpointManager.class) {
                if (instance == null) {
                    instance = new NexsEndpointManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * Convenience method to get URL for a NEXS endpoint
     *
     * @param endpoint The NEXS endpoint
     * @return Complete URL for the endpoint
     */
    public static String getUrl(NexsOrderProcessingEndpoints endpoint) {
        return getInstance().getUrl(endpoint);
    }
    
    /**
     * Convenience method to get URL for a NEXS endpoint with path parameters
     *
     * @param endpoint The NEXS endpoint
     * @param pathParams Map of path parameters to replace in the URL
     * @return Complete URL with path parameters replaced
     */
    public static String getUrl(NexsOrderProcessingEndpoints endpoint, Map<String, String> pathParams) {
        return getInstance().getUrl(endpoint, pathParams);
    }
    
    /**
     * Convenience method to refresh all NEXS endpoint URLs
     */
    public static void refresh() {
        getInstance().refresh();
    }
    
    /**
     * Convenience method to validate all NEXS endpoints
     *
     * @return true if all endpoints are valid, false otherwise
     */
    public static boolean validate() {
        return getInstance().validateEndpoints();
    }
    
    /**
     * Convenience method to get all NEXS endpoint URLs
     *
     * @return Map of endpoint names to their full URLs
     */
    public static Map<String, String> getAllUrls() {
        return getInstance().getAllUrls();
    }
}

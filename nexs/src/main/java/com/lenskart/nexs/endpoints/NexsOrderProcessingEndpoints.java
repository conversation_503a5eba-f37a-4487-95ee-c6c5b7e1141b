package com.lenskart.nexs.endpoints;

import com.lenskart.commons.endpoints.BaseEndpoint;
import lombok.Getter;

import java.util.Map;

@Getter
public enum NexsOrderProcessingEndpoints implements BaseEndpoint {

    //Picking Service Endpoints
    PICKING_GET_LOCATION_CATEGORIES("/nexs/order/picking/location/categories", "pickingService"),
    PICKING_SUMMARY("/nexs/order/picking/summary/{$location}/{$categories}/standard", "pickingService"),
    PICKING_CLOSE_PICKING_SUMMARY("/nexs/order/picking/complete/{$id}", "pickingService"),
    PICKING_DISPLAY_PICKLIST("/nexs/order/picking/{$pickingSummaryId}", "pickingService"),
    PICKING_SCAN_BARCODE("/nexs/order/picking/scan/{$picklistId}/{$barcode}", "pickingService"),
    PICKING_VIEW_ORDER("/nexs/order/picking/view/order", "pickingService"),
    PICKING_VALIDATE_FRAME("/nexs/order/picking/validate/frame", "pickingService"),
    PICKING_LENS_PICKING_DETAILS("/nexs/order/picking/details/{$fittingId}", "pickingService"),
    PICKING_SCAN_LENS("/nexs/order/picking/lens/scan", "pickingService"),
    PICKING_COMPLETE_LENS_PICKING("/nexs/order/picking/v2/lens/summary/{$pickingSummaryId}/complete", "pickingService"),
    PICKING_GET_WAVE_DETAILS("/nexs/order/picking/addverb/wave/{$waveId}", "pickingService"),
    PICKING_DISCARD_ASRS_ORDER("/nexs/order/picking/supervisor/discard/asrs/Order/{$shippingPackageId}", "pickingService"),
    PICKING_SYNC_ORDER_TO_ES("/nexs/order/picking/order/item/syncToES", "pickingService"),
    PICKING_DYNAMIC_PICKING("/nexs/order/picking/pick/orderItemId/{$orderItemId}/{$barcode}", "pickingService"),
    PICKING_ADDVERB_CREATE_WAVE("/nexs/order/picking/addverb/create/wave", "pickingService"),
    PICKING_ADDVERB_PICK_ITEM("/nexs/order/picking/addverb/pick/item", "pickingService"),
    PICKING_ADDVERB_COMPLETE("/nexs/order/picking/addverb/complete", "pickingService"),
    PICKING_CHANGE_ES_STATUS("/nexs/order/picking/supervisor/es/change/status", "pickingService"),
    PICKING_CREATE_PICKING_SUMMARY_DO("/nexs/order/picking/DO/createPickingSummary", "pickingService"),
    PICKING_PICK_BARCODE_DO("/nexs/order/picking/DO/pickBarcode", "pickingService"),
    PICKING_GET_PICKING_SUMMARY_DO("/nexs/order/picking/DO/getPickingSummary", "pickingService"),

    //Packing Service Endpoints
    PACKING_GET_PACKING_SUMMARY("/nexs/packing/getPackingSummary", "packingService"),
    PACKING_COMPLETE_PACKING("/nexs/packing/completePacking", "packingService"),
    PACKING_PACKING_SLIP("/nexs/packing/label/pdf", "packingService"),

    //OrderQC Service Endpoints
    ORDERQC_FETCH_ORDER_ENTITY_TYPE("/nexs/api/orderqc/fetch/order/entityType", "orderQcService"),
    ORDERQC_COMPLETE_QC("/nexs/api/orderqc/completeQc", "orderQcService"),

    //WMS Service Endpoints
    WMS_GET_COMMENT("/nexs/wms/api/v1/comment/get","wmsService"),
    WMS_PRINT_SHIPMENT("/nexs/wms/api/v1/shipment/print","wmsService"),
    WMS_PRINT_SHIPMENT_MP("/nexs/wms/api/v1/shipment/mporder/print","wmsService"),
    WMS_CREATE_TRAY("/nexs/wms/api/v1/tray/create","wmsService"),
    WMS_LOCATION_SCAN("/nexs/wms/api/v1/location/scan","wmsService"),
    WMS_EXPORT_PDF("/nexs/wms/api/v1/exportPdf","wmsService"),
    WMS_ADD_VSM_COMMENT("/nexs/wms/api/v1/vsmComment/add","wmsService"),
    WMS_REASSIGN_SHIPMENT("/nexs/wms/api/v1/shipment/reassign","wmsService"),
    WMS_ORDER_DETAILS("/nexs/wms/api/v1/order/details/header","wmsService"),
    WMS_ORDER_DETAILS_WITH_ID("/nexs/wms/api/v1/order/details/id/{$shippingPackageId}","wmsService"),
    WMS_HAND_EDGING_SCAN("/nexs/wms/api/v1/hand/edging/scan","wmsService"),
    WMS_FETCH_INVOICE("/nexs/wms/api/v1/invoice/fetch","wmsService"),
    WMS_ORDER_DETAILS_OVERVIEW("/nexs/wms/api/v1/order/details/overview","wmsService");





    private final String endpoint;
    private final String serviceName;

    NexsOrderProcessingEndpoints(String endpoint, String serviceName) {
        this.endpoint = endpoint;
        this.serviceName = serviceName;
    }

    @Override
    public String getUrl() {
        return NexsEndpointManager.getEndpointUrl(this);
    }

    @Override
    public String getUrl(Map<String, String> pathParams) {
        return NexsEndpointManager.getEndpointUrl(this, pathParams);
    }

    // Legacy methods for backward compatibility - marked as deprecated

    /**
     * @deprecated Use NexsEndpointManager.refreshEndpoints() instead
     */
    @Deprecated
    public static void refreshUrls() {
        NexsEndpointManager.refreshEndpoints();
    }
}

package com.lenskart.nexs.validator.wmsvalidator;

import com.lenskart.commons.base.IValidator;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.nexs.database.WMSDbUtils;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.scm.database.ScmDbUtils;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@SuperBuilder
@Slf4j
public class OrderCancellationValidator implements IValidator {
    OrderContext orderContext;
    NexsOrderContext nexsOrderContext;

    @Override
    public void validateNode() {
        validateDBEntities();
    }

    @Override
    public void validateDBEntities() {
        log.info("Validating order cancellation in NEXS");
        String shipmentState = ScmDbUtils.getShipmentState(String.valueOf(orderContext.getOrderId()));
        log.info("Shipment State - {}", shipmentState);
        String shippingPackaeID = null;
        if (!shipmentState.isEmpty()) {
            shippingPackaeID = ScmDbUtils.getShippingPackageId(String.valueOf(orderContext.getOrderId())).stream()
                    .map(map -> map.get("shipping_package_id").toString())
                    .collect(Collectors.joining(","));
            log.info(shippingPackaeID);
        }
        if (!shippingPackaeID.isEmpty()) {
            List<Map<String, Object>> status = WMSDbUtils.itemStatusByShippingId(shippingPackaeID);
            status.forEach(item -> {
                log.info("Item Status - {}", item.get("status"));
                Assert.assertEquals(status.toString(), "CANCELLED");
            });
        }
    }
}

package com.lenskart.nexs.validator.wmsvalidator;

import com.lenskart.commons.base.IValidator;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.database.WMSDbUtils;
import com.lenskart.nexs.helpers.state.MeiInHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;

@Builder
@Slf4j
public class MeiInValidator implements IValidator {
    NexsOrderContext nexsOrderContext;
    MeiInHelper meiInHelper;

    @Override
    public void validateNode() {
        if (nexsOrderContext.getIsValidationRequired()) {
            var response = meiInHelper.getResponse();
            Assert.assertNotNull(response, "MeiInHelper response should not be null");
            Assert.assertEquals(response.statusCode(), 200, "Expected status code 200");
            Assert.assertEquals((String) RestUtils.getValueFromResponse(response, "data.shipmentId"), nexsOrderContext.getShippingId(),
                    "Shipping ID in response should match the context shipping ID");
            log.info("MeiIn validation successful for shipping ID: {}", nexsOrderContext.getShippingId());
        }
    }

    @Override
    public void validateDBEntities() {
        if (nexsOrderContext.getIsValidationRequired()) {
            Assert.assertEquals(WMSDbUtils.getStausBasedOnBarcode(nexsOrderContext, nexsOrderContext.getLeftLensBarcode()),
                    Constants.EDGING);
            Assert.assertEquals(WMSDbUtils.getStausBasedOnBarcode(nexsOrderContext, nexsOrderContext.getRightLensBarcode()),
                    Constants.EDGING);
            Assert.assertEquals(WMSDbUtils.getStausBasedOnBarcode(nexsOrderContext, nexsOrderContext.getFrameBarcode()),
                    Constants.IN_TRAY);
        }
    }
}

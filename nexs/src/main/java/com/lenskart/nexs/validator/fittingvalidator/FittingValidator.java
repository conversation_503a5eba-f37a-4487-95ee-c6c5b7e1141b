package com.lenskart.nexs.validator.fittingvalidator;

import com.lenskart.commons.base.IValidator;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.database.ImsDbUtils;
import com.lenskart.nexs.database.WMSDbUtils;
import com.lenskart.nexs.helpers.state.FittingHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.model.fitting.GetFittingDetailResponse;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;

@Builder
@Slf4j
public class FittingValidator implements IValidator {
    NexsOrderContext nexsOrderContext;
    GetFittingDetailResponse getFittingDetailResponse;
    FittingHelper fittingHelper;

    @Override
    public void validateNode() {
        if (nexsOrderContext.getIsValidationRequired()) {
            Assert.assertEquals(getFittingDetailResponse.getData().getScannedOrderItem().getShippingPackageId(),
                    nexsOrderContext.getShippingId());
            if (nexsOrderContext.getIsShipToStore())
                Assert.assertFalse(getFittingDetailResponse.getData().getOrder().getShipToCustomer());
            else
                Assert.assertTrue(getFittingDetailResponse.getData().getOrder().getShipToCustomer());
            Assert.assertEquals(getFittingDetailResponse.getData().getScannedOrderItem().getFittingId(),
                    Integer.valueOf(nexsOrderContext.getFittingId()));
            Assert.assertEquals(getFittingDetailResponse.getData().getScannedOrderItem().getFrameTrayId(), nexsOrderContext.getTrayBarcode());
            Assert.assertEquals(getFittingDetailResponse.getData().getScannedOrderItem().getLeftLensTrayId(),
                    nexsOrderContext.getTrayBarcode());
            Assert.assertEquals(getFittingDetailResponse.getData().getScannedOrderItem().getRightLensTrayId(),
                    nexsOrderContext.getTrayBarcode());
            Assert.assertEquals(getFittingDetailResponse.getData().getScannedOrderItem().getFrameBarcode(),
                    nexsOrderContext.getFrameBarcode());
            Assert.assertEquals(getFittingDetailResponse.getData().getScannedOrderItem().getLeftLensBarcode(),
                    nexsOrderContext.getLeftLensBarcode());
            Assert.assertEquals(getFittingDetailResponse.getData().getScannedOrderItem().getRightLensBarcode(),
                    nexsOrderContext.getRightLensBarcode());

            Assert.assertTrue(fittingHelper.getResponse().jsonPath().getBoolean("data.fitting_completed"));

        }
    }

    @Override
    public void validateDBEntities() {
        if (nexsOrderContext.getIsValidationRequired()) {
            Assert.assertEquals(WMSDbUtils.getFittingStatus(nexsOrderContext).getFirst().get("fitting_status").toString(),
                    Constants.DONE);
            nexsOrderContext.getBarcodes().forEach(barcode -> {
                try {
                    ImsDbUtils.getBarcodeItemDetails(nexsOrderContext, barcode, Constants.GOOD, Constants.ALLOCATED, Constants.CUSTOMIZATION_COMPLETE);
                } catch (Exception e) {
                    log.error("Failed to get barcode item details for barcode: {}", barcode);
                }
            });
            Assert.assertEquals(WMSDbUtils.getStausBasedOnBarcode(nexsOrderContext, nexsOrderContext.getLeftLensBarcode()),
                    Constants.CUSTOMIZATION_COMPLETE);
            Assert.assertEquals(WMSDbUtils.getStausBasedOnBarcode(nexsOrderContext, nexsOrderContext.getRightLensBarcode()),
                    Constants.CUSTOMIZATION_COMPLETE);
            Assert.assertEquals(WMSDbUtils.getStausBasedOnBarcode(nexsOrderContext, nexsOrderContext.getFrameBarcode()),
                    Constants.CUSTOMIZATION_COMPLETE);
        }
    }
}

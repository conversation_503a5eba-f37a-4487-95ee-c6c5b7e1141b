package com.lenskart.nexs.validator.wmsvalidator;

import com.lenskart.commons.base.IValidator;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.database.ImsDbUtils;
import com.lenskart.nexs.database.WMSDbUtils;
import com.lenskart.nexs.helpers.state.MeiOutHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;

@Builder
@Slf4j
public class MeiOutValidator implements IValidator {
    NexsOrderContext nexsOrderContext;
    MeiOutHelper meiOutHelper;

    @Override
    public void validateNode() {
        if(nexsOrderContext.getIsValidationRequired()){
            assert meiOutHelper.getResponse().getStatusCode() == 200;
            Assert.assertEquals((String) RestUtils.getValueFromResponse(meiOutHelper.getResponse(), "data.shipmentId"), nexsOrderContext.getShippingId(),
                    "Shipping ID in response should match the context shipping ID");
        }
    }

    @Override
    public void validateDBEntities() {
        if(nexsOrderContext.getIsValidationRequired()){
            nexsOrderContext.getBarcodes().forEach(barcode -> {
                try {
                    ImsDbUtils.getBarcodeItemDetails(nexsOrderContext, barcode, Constants.GOOD, Constants.ALLOCATED, Constants.PENDING_CUSTOMIZATION);
                } catch (Exception e) {
                    log.error("Failed to get barcode item details for barcode: {}", barcode);
                }
            });
            Assert.assertEquals(WMSDbUtils.getStausBasedOnBarcode(nexsOrderContext, nexsOrderContext.getLeftLensBarcode()),
                    Constants.PENDING_CUSTOMIZATION);
            Assert.assertEquals(WMSDbUtils.getStausBasedOnBarcode(nexsOrderContext, nexsOrderContext.getRightLensBarcode()),
                    Constants.PENDING_CUSTOMIZATION);
            Assert.assertEquals(WMSDbUtils.getStausBasedOnBarcode(nexsOrderContext, nexsOrderContext.getFrameBarcode()),
                    Constants.PENDING_CUSTOMIZATION);
        }
    }
}

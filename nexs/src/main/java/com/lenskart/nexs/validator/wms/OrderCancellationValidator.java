package com.lenskart.nexs.validator.wms;

import com.lenskart.commons.base.IValidator;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.nexs.database.WMSDbUtils;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.scm.constants.ScmConstants;
import com.lenskart.scm.database.ScmDbUtils;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@SuperBuilder
@Slf4j
public class OrderCancellationValidator implements IValidator {
    OrderContext orderContext;
    NexsOrderContext nexsOrderContext;

    @Override
    public void validateNode() {
        validateDBEntities();
    }

    @Override
    public void validateDBEntities() {
        log.info("Validating order cancellation in NEXS");
        String shipmentState = ScmDbUtils.getShipmentState(String.valueOf(orderContext.getOrderId()));
        log.info("Shipment State - {}", shipmentState);
        String shippingPackageID = null;
        if (!shipmentState.isEmpty()) {
            shippingPackageID = ScmDbUtils.getShippingPackageId(String.valueOf(orderContext.getOrderId())).stream()
                    .map(map -> map.get("shipping_package_id").toString())
                    .collect(Collectors.joining(","));
            log.info(shippingPackageID);
        }
        if (!shippingPackageID.isEmpty()) {
            List<Map<String, Object>> status = WMSDbUtils.itemStatusByShippingId(shippingPackageID);
           boolean isCancelled = status.getFirst().get("status").toString().equals(ScmConstants.CANCELLED);
           try{
               Assert.assertTrue(isCancelled);
           } catch (AssertionError e) {
               Assert.assertFalse(ScmDbUtils.getVirtualShipmentCancelTasks(String.valueOf(orderContext.getOrderId())).isEmpty());
           }
        }
    }
}

package com.lenskart.nexs.api;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.nexs.model.ims.BarcodeItem;
import com.lenskart.nexs.model.ims.FetchBarcodeItemDetailsRequest;
import io.restassured.response.Response;
import lombok.SneakyThrows;

import java.util.ArrayList;
import java.util.List;

import static com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints.IMS_FETCH_BARCODE_ITEM_DETAILS;

public class ImsService {

    public static String fetchBarcodeForGivenPid(String availability, String condition, Boolean enabled, String facility,
                                                 String pid, String status) {
        return fetchBarcodeForGivenPid(availability, condition, enabled, facility, pid, status, "1");
    }

    @SneakyThrows
    public static String fetchBarcodeForGivenPid(String availability, String condition, Boolean enabled, String facility,
                                                 String pid, String status, String index) {
        BarcodeItem barcodeItem = new BarcodeItem();
        barcodeItem.setAvailability(availability);
        barcodeItem.setCondition(condition);
        barcodeItem.setEnabled(enabled);
        barcodeItem.setFacility(facility);
        barcodeItem.setPid(pid);
        barcodeItem.setStatus(status);

        FetchBarcodeItemDetailsRequest request = new FetchBarcodeItemDetailsRequest();
        List<BarcodeItem> list = new ArrayList<BarcodeItem>();
        list.add(barcodeItem);
        request.setBarcodeItems(list);
        Response response = RestUtils.post(IMS_FETCH_BARCODE_ITEM_DETAILS.getUrl(), null, new ObjectMapper().writeValueAsString(request));
        try {
            index = response.jsonPath().getList("data.barcodeItems").size() == 1 ? "0" : index;
            return response.jsonPath().getString("data.barcodeItems[" + index + "].barcode");
        } catch (Exception e) {
            throw new RuntimeException("No barcodes to return");
        }
    }
}

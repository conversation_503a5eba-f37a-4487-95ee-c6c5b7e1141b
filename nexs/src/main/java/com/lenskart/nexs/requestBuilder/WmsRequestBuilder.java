package com.lenskart.nexs.requestBuilder;

import com.lenskart.nexs.model.NexsOrderContext;
import org.json.JSONObject;

public class WmsRequestBuilder {
    public static JSONObject createReassignmentPayload(String shippingPackageID,String selectedfacility) {
        JSONObject payload = new JSONObject();
        payload.put("shippingPackageId", shippingPackageID);
        payload.put("selectedFacility", selectedfacility);
        return payload;
    }

    public static JSONObject getFullfillabilityPayload(NexsOrderContext nexsOrderContext) {
        JSONObject payload = new JSONObject();
        payload.put("nexsOrderId", nexsOrderContext.getNexsOrderId());
        payload.put("isFullfillable", 1);
        return payload;
    }


    public static JSONObject UpdateBoxCountPayload(String shippingPackageID, String boxCount) {
        JSONObject payload = new JSONObject();
        payload.put("shippingPackageID", shippingPackageID);
        payload.put("boxCount", boxCount);
        return payload;
    }
}


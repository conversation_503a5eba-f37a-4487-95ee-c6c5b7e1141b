package com.lenskart.nexs.requestBuilder.grn;

import org.json.JSONObject;
import java.util.Map;

public class BarcodePriceBuilder
{
    public static JSONObject buildBarcodePriceRequest(Map<String, Integer> barcodePidMap, String facilityCode) {
        return new JSONObject()
                .put("barcode_pid", new JSONObject(barcodePidMap))
                .put("facility_code", facilityCode);
    }
}

package com.lenskart.nexs.requestBuilder.catalog;

import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CatalogRequestBuilder {

    public static JSONObject getProductDetailsPayload(int productId, String stateCode, String supplierId) {
        JSONObject request = new JSONObject();
        List<Integer> productIds = new ArrayList<>();
        productIds.add(productId);
        request.put("product_ids", productIds);
        request.put("state_code", stateCode);
        request.put("supplier_id", supplierId);
        return request;
    }
}

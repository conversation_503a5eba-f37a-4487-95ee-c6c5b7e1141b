package com.lenskart.nexs.requestBuilder.jit;

import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.nexs.model.jit.JITStatusUpdateModel;
import org.json.JSONArray;
import org.json.JSONObject;
import java.text.SimpleDateFormat;
import java.util.Date;

public class JitRequestBuilder {

    public static JSONObject rxuStatusRequestBuilder(NexsOrderContext nexsOrderContext, String jobId, String stationId) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        String formattedDateTime = dateFormat.format(new Date());
        JSONObject rxuStatusItem = new JSONObject();
        rxuStatusItem.put("dateTime", formattedDateTime);
        rxuStatusItem.put("jobId", jobId);
        rxuStatusItem.put("stationDesc", "test");
        rxuStatusItem.put("stationId", stationId);

        JSONArray rxuStatusArray = new JSONArray();
        rxuStatusArray.put(rxuStatusItem);
        JSONObject request = new JSONObject();
        request.put("rxuStatus", rxuStatusArray);
        return request;
    }

    public static JSONObject jitStatusUpdateRequestBuilder(JITStatusUpdateModel jitStatusUpdateModel) {
        JSONObject request = new JSONObject();
        request.put("jobId", jitStatusUpdateModel.getJobId());
        request.put("status", jitStatusUpdateModel.getStatus());
        request.put("facility", jitStatusUpdateModel.getFacility());
        request.put("sourceRefId", jitStatusUpdateModel.getSourceRefId());
        request.put("reason", jitStatusUpdateModel.getReason());
        return request;
    }
}

package com.lenskart.nexs.requestBuilder;

import org.json.JSONObject;

public class TransfersRequestBuilder {
    public static JSONObject ScanBarcodePayload(String barcode, String boxBarcode, String unicomTransferCode, String transferCode) {
        JSONObject payload = new JSONObject();
        payload.put("barcode", barcode);
        payload.put("box_barcode", boxBarcode);
        payload.put("unicom_transfer_code", unicomTransferCode);
        payload.put("transfer_code", transferCode);
        return payload;
    }
}


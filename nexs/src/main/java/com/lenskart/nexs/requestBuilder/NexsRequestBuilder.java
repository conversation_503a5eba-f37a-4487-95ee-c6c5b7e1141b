package com.lenskart.nexs.requestBuilder;

import com.lenskart.nexs.model.NexsOrderContext;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import static com.lenskart.nexs.constants.Constants.UAE_LEGAL_OWNER;

@Slf4j
public class NexsRequestBuilder {

    public static JSONObject getLoginPayload(NexsOrderContext nexsOrderContext, String user, String password) {
        JSONObject obj = new JSONObject();
        obj.put("userName", user);
        obj.put("password", password);
        return obj;
    }

    public static JSONObject getCompletePackingPayload(NexsOrderContext nexsOrderContext, String unicomOrderCode, String shipmentId, int incrementId) {
        JSONObject request = new JSONObject();
        request.put("unicomOrderCode", Integer.parseInt(unicomOrderCode));
        request.put("magentoItemId", String.valueOf(nexsOrderContext.getMagentoItemId()));

        JSONObject packingDetails = new JSONObject();
        packingDetails.put("shipmentId", shipmentId);
        packingDetails.put("status", "DONE");
        packingDetails.put("packerName", "<EMAIL>");
        packingDetails.put("incrementId", incrementId);

        request.put("packingDetails", packingDetails);
        return request;
    }

    public static JSONObject getSaveManifestPayload(NexsOrderContext nexsOrderContext) {
        JSONObject request = new JSONObject();
        request.put("channel", nexsOrderContext.getChannelType());
        request.put("facilityCode", nexsOrderContext.getFacilityCode());
        request.put("shippingMethod", "Any");
        request.put("shippingProvider", nexsOrderContext.getShippingProvider());
        request.put("shippingProviderCode", nexsOrderContext.getShippingProviderCode());
        return request;
    }

    public static JSONObject getScanItemPayloadForConsolidation(NexsOrderContext nexsOrderContext) {
        JSONObject request = new JSONObject();
        request.put("barCode", nexsOrderContext.getShippingId());
        request.put("type", 5);
        return request;
    }

    public static JSONObject getAllocateItemPayloadForConsolidation(NexsOrderContext nexsOrderContext) {
        JSONObject request = new JSONObject();
        request.put("barCode", nexsOrderContext.getShippingId());
        request.put("type", 5);
        request.put("boxBarCode", nexsOrderContext.getBoxCode());
        return request;
    }

    public static JSONObject getOrderFlushPayloadForConsolidation(NexsOrderContext nexsOrderContext) {
        JSONObject request = new JSONObject();
        request.put("storeCode", nexsOrderContext.getStoreCode());
        request.put("type", 5);
        return request;
    }

    public static JSONObject getCreateTrayPayload(NexsOrderContext nexsOrderContext, String frameBarcode, String trayBarcode) {
        JSONObject request = new JSONObject();
        request.put("frame_barcode", frameBarcode);
        request.put("tray_barcode", trayBarcode);
        return request;
    }

    public static JSONObject getMeiPayload(NexsOrderContext nexsOrderContext, String event) {
        JSONObject obj = new JSONObject();
        obj.put("location", "001");
        obj.put("barcode", nexsOrderContext.getTrayBarcode());
        obj.put("event", event);
        if (nexsOrderContext.getLegalOwner().equals(UAE_LEGAL_OWNER)) {
            obj.put("isEspressoRequired", false);
        }
        return obj;
    }

    public static JSONObject getCompleteFittingPayload(NexsOrderContext nexsOrderContext) {
        JSONObject request = new JSONObject();
        request.put("leftLensHandEdgingRequired", false);
        request.put("rightLensHandEdgingRequired", false);
        return request;
    }


}

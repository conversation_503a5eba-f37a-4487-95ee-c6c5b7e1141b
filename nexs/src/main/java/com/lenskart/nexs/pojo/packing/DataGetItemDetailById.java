package com.lenskart.nexs.pojo.packing;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DataGetItemDetailById {
    private String country;
    private String extCustomerId;
    private Integer incrementId;
    private String shipmentId;
    private String packingDetailStatus;
    private String status;
    private String orderType;
    private String facilityCode;
    private Object awbNumber;
    private Boolean webB2BOrder;
    private Boolean jitOrder;
    private String awb;
    private Integer unicomOrderCode;
    private List<PackingOrderItem> packingOrderItem;
    private String role;
    private Boolean ownDaysCard;
    private Object additionalImageRequired;
    private String magentoItemId;
    private String expectedDeliveryDate;

}


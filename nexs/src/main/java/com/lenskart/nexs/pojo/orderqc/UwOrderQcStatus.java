package com.lenskart.nexs.pojo.orderqc;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class UwOrderQcStatus {
    private OrderQCDetailsResponse order;
    private QcStatus qcStatus;
    private boolean bluecutLensType;
    private String customText;
    private String lensNameColorFlag;
    private boolean isBulkEligibleForConsolidation;
    private boolean exchangeOrderHolded;
    private boolean delayOrderCard;
    private boolean tokaiCard;

}

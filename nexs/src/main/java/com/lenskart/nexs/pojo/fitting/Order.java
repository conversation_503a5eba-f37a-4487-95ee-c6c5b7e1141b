package com.lenskart.nexs.pojo.fitting;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class Order {
    @JsonProperty("order_type")
    private String orderType;
    @JsonProperty("country_code")
    private String countryCode;
    @JsonProperty("ext_customer_id")
    private String extCustomerId;
    @JsonProperty("ship_to_customer")
    private Boolean shipToCustomer;
    @JsonProperty("increment_id")
    private Boolean incrementId;
}

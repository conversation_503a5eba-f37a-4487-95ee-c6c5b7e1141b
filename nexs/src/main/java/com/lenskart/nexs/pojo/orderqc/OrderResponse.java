package com.lenskart.nexs.pojo.orderqc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class OrderResponse {
    private int itemId;
    private Long orderItemHeaderId;
    private long nexsOrderId;
    private Integer incrementId;
    private String productOptions;
    private short qtyOrdered;
    private BigDecimal grandTotal;
    private int discount;
    private short rewardPoints;
    private Date createdAt;
    private String state;
    private String status;
    private byte storeId;
    private byte stockOutStatus;
    private String saleSource;
    private byte comments;
    private String couponCode;
    private Long customerId;
    private BigDecimal emiCharge;
    private String couponRuleName;
    private BigDecimal orderDiscountAmount;
    private Double orderGrandTotal;
    private BigDecimal baseTotalInvoiced;
    private BigDecimal shippingAmount;
    private BigDecimal subtotal;
    private String customerEmail;
    private int magentoItemId;
    private String unicomSynStatus;
    private String unicomOrdercode;
    private String unicomOrderStatus;
    private BigDecimal taxCollected;
    private Date updatedAt;
    private String channel;
    private String extCustomerId;
    private Integer parentId;
    private String orderType;
    private Integer priority;
    private Integer payementCapture;
    private String productDeliveryType;
    private Boolean shipToStoreRequired = false;
    private Boolean isLocalFittingRequired = false;
    private String facilityCode;
    private String b2bRefrenceItemId;
    private Date dispatchDate;
    private Date deliveryDate;

}


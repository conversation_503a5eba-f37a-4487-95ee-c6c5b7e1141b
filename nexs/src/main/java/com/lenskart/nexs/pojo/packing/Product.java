package com.lenskart.nexs.pojo.packing;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data

public class Product {
    private Integer productId;
    private Integer classification;
    private String groupProductIds;
    private String value;
    private Integer qty;
    private Integer isInStock;
    private String sku;
    private String productImage;
    private Object productImageAlt;
    private Integer enable;
    private Integer sellingPrice;
    private Integer costPrice;
    private Integer costAverage;
    private Integer price;
    private String size;
    private Integer startingSoh;
    private Integer currentSoh;
    private Integer qsi;
    private Integer wfi;
    private Integer stockOut;
    private Integer stockMissingSupplier;
    private Integer stockOutSupplier;
    private Integer stockInSupplier;
    private Integer stockFoundSupplier;
    private Integer stockDefectiveSupplier;
    private Integer stockReturnSupplier;
    private Integer webStockOut;
    private Integer stockIn;
    private Integer stockDefective;
    private Integer stockMissing;
    private Integer stockFound;
    private Integer stockReturn;
    private Integer returnVendor;
    private String inventoryStartDate;
    private String createdAt;
    private Integer closedStockOut;
    private Integer processingNotStockout;
    private Integer nqsi;
    private Integer repeatRate;
    private Integer repeatMailerRate;
    private String catIds;
    private String brand;
    private Integer stockOffline;
    private String productUrl;
    private String frameType;
    private Integer msl;
    private Integer isPowerFollowup;
    private Object wmsSynStatus;
    private Integer wmsInventory;
    private Integer blockedWmsInventory;
    private Integer completeClosedCount;
    private List<String> productImages;
    private Object lensonlyImageUploadTime;
    private String hsnCode;
    private String hsnClassification;
    private String hsnCodeWithoutPower;
}

package com.lenskart.nexs.pojo.orderqc;


import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.lenskart.nexs.wms.response.OrderAndEntityTypeResponse;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class FetchOrderScannedEntityResponse {

    private OrderQcStatus orderQcStatus;
    private InfoForQCPanel infoForQCPanel;
    public OrderAndEntityTypeResponse orderAndEntityTypeResponse;

}

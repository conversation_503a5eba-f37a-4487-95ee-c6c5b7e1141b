package com.lenskart.nexs.pojo.fitting;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DataGetFittingDetail {
    private Order order;
    @JsonProperty("scanned_order_item")
    private ScannedOrderItem scannedOrderItem;
}

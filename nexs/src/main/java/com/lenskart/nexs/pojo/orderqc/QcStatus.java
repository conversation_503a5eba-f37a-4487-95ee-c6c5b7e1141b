package com.lenskart.nexs.pojo.orderqc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class QcStatus implements Serializable {
    private static final long serialVersionUID = 1L;
    private Integer id;
    private int incrementId;
    private String shippingPackageId;
    private int orderItemId;
    private String barcode;
    private String status;
    private String mediaFileName;
    private Integer reasonId;
    private String reasonName;
    private String shelfCode;
    private Date createdAt;
    private String createdBy;
    private Date updatedAt;
    private String updatedBy;
    private String qcStage;
    private Integer qcResponsibleDepartment;
    private Integer qcResponsibleName;
    private Boolean isInvoicePrinted = false;
    private Boolean isInvoiceCreated;
    private String invoiceCode;
    private String invoiceFailureReason;
    private Boolean isCourierAllocated;
    private String fittingId;
    private String trayId;

    private Integer qcFailedCount;

}

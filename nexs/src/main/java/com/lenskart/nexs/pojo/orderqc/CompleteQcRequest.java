package com.lenskart.nexs.pojo.orderqc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CompleteQcRequest {
    private String barcode;
    private Boolean damaged;
    private Integer incrementId;
    private String primaryReason;
    private String qcStage;
    private String shelfCode;
    private String shippingPackageId;
    private String status;
    private String tertiaryReason;
    private Boolean isScanningDone;
    private String fittingId;
    private String trayId;
    private String reason;
    private String reasonId;
    private String reasonName;
    private String secondaryReason;
    private Integer orderItemId;
}

package com.lenskart.nexs.pojo.orderqc;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;


@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class OrderQcStatus {
    private List<UwOrderQcStatus> orderQcStatusList;
    private boolean duplicateBarcode;
    private boolean allQCPassOrAnyItemQCFail;
    private List<Map<String,String>> additionalImageRequired;

}

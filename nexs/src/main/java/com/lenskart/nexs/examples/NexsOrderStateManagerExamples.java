package com.lenskart.nexs.examples;

import com.lenskart.commons.model.NexsOrderState;
import com.lenskart.nexs.helpers.NexsOrderStateHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import java.time.LocalDateTime;

/**
 * Comprehensive examples demonstrating how to use the NexsOrderStateManager.
 * These examples show different patterns and use cases for order state management.
 */
@Slf4j
public class NexsOrderStateManagerExamples {
    
    /**
     * Example 1: Basic Single State Transition
     * Shows how to move an order from one state to the next immediate state.
     */
    @Test
    public void example1_SingleStateTransition() {
        log.info("=== Example 1: Single State Transition ===");
        
        // Create order context with initial state
        NexsOrderContext orderContext = createSampleOrderContext("ORD-001", NexsOrderState.CREATED);
        
        // Create state manager and transition to next state
        NexsOrderStateHelper stateManager = NexsOrderStateHelper.builder()
                .orderContext(orderContext)
                .build()
                .transitionToNextState();
        
        // Verify the transition
        NexsOrderContext updatedContext = stateManager.getOrderContext();
        log.info("Order {} transitioned from CREATED to {}", 
                updatedContext.getOrderId(), updatedContext.getCurrentState().getDisplayName());
        
        // Print session summary
        log.info("Session Summary: {}", stateManager.getSessionSummary());
        
        assert updatedContext.getCurrentState() == NexsOrderState.PROCESSING;
        log.info("✅ Single state transition completed successfully\n");
    }
    
    /**
     * Example 2: Direct State Transition
     * Shows how to transition to a specific state (must be next in sequence).
     */
    @Test
    public void example2_DirectStateTransition() {
        log.info("=== Example 2: Direct State Transition ===");
        
        // Create order context
        NexsOrderContext orderContext = createSampleOrderContext("ORD-002", NexsOrderState.CREATED);
        
        // Transition directly to IN_PICKING state
        NexsOrderStateHelper stateManager = NexsOrderStateHelper.builder()
                .orderContext(orderContext)
                .build()
                .transitionToState(NexsOrderState.IN_PICKING);
        
        // Verify the transition
        log.info("Order {} transitioned to: {}", 
                orderContext.getOrderId(), orderContext.getCurrentState().getDisplayName());
        
        assert stateManager.isInState(NexsOrderState.IN_PICKING);
        log.info("✅ Direct state transition completed successfully\n");
    }
    
    /**
     * Example 3: Sequential Transitions to Final State
     * Shows how to move an order through multiple states to reach the final state.
     */
    @Test
    public void example3_SequentialTransitionsToFinalState() {
        log.info("=== Example 3: Sequential Transitions to Final State ===");
        
        // Create order context
        NexsOrderContext orderContext = createSampleOrderContext("ORD-003", NexsOrderState.CREATED);
        
        // Transition through all states to reach DISPATCHED
        NexsOrderStateHelper stateManager = NexsOrderStateHelper.builder()
                .orderContext(orderContext)
                .sequentialTransition(true)
                .build()
                .transitionToFinalState(NexsOrderState.DISPATCHED);
        
        // Verify final state
        NexsOrderContext updatedContext = stateManager.getOrderContext();
        log.info("Order {} reached final state: {}", 
                updatedContext.getOrderId(), updatedContext.getCurrentState().getDisplayName());
        
        // Print complete transition history
        log.info("Complete Transition History:");
        updatedContext.getStateHistory().forEach(record -> 
                log.info("  {}", record.toString()));
        
        assert updatedContext.getCurrentState() == NexsOrderState.DISPATCHED;
        assert stateManager.isCompleted();
        assert updatedContext.getStateHistory().size() == 7; // 7 transitions from CREATED to DISPATCHED
        log.info("✅ Sequential transitions completed successfully\n");
    }
    
    /**
     * Example 4: Orchestrated Flow with ServiceHelper Pattern
     * Shows how to use the init() → process() → validate() pattern.
     */
    @Test
    public void example4_OrchestratedFlow() {
        log.info("=== Example 4: Orchestrated Flow ===");
        
        // Create order context
        NexsOrderContext orderContext = createSampleOrderContext("ORD-004", NexsOrderState.CREATED);
        
        // Use orchestration flow to transition to INVOICED
        NexsOrderStateHelper stateManager = NexsOrderStateHelper.builder()
                .orderContext(orderContext)
                .targetState(NexsOrderState.INVOICED)
                .sequentialTransition(true)
                .build();
        
        // Execute the complete flow: init() → process() → validate()
        stateManager.test();
        
        log.info("Orchestration completed. Final state: {}", 
                orderContext.getCurrentState().getDisplayName());
        
        log.info("Session Summary: {}", stateManager.getSessionSummary());
        
        assert orderContext.getCurrentState() == NexsOrderState.INVOICED;
        log.info("✅ Orchestrated flow completed successfully\n");
    }
    
    /**
     * Example 5: Step-by-Step Transitions with Validation
     * Shows how to perform controlled step-by-step transitions with validation at each step.
     */
    @Test
    public void example5_StepByStepTransitions() {
        log.info("=== Example 5: Step-by-Step Transitions ===");
        
        // Create order context
        NexsOrderContext orderContext = createSampleOrderContext("ORD-005", NexsOrderState.CREATED);
        
        NexsOrderStateHelper stateManager = NexsOrderStateHelper.builder()
                .orderContext(orderContext)
                .build();
        
        // Step 1: CREATED → PROCESSING
        stateManager.transitionToState(NexsOrderState.PROCESSING);
        log.info("Step 1 completed: {}", stateManager.getOrderContext().getCurrentState().getDisplayName());
        assert stateManager.isInState(NexsOrderState.PROCESSING);
        
        // Step 2: PROCESSING → IN_PICKING
        stateManager.transitionToState(NexsOrderState.IN_PICKING);
        log.info("Step 2 completed: {}", stateManager.getOrderContext().getCurrentState().getDisplayName());
        assert stateManager.isInState(NexsOrderState.IN_PICKING);
        
        // Step 3: IN_PICKING → PICKED
        stateManager.transitionToState(NexsOrderState.PICKED);
        log.info("Step 3 completed: {}", stateManager.getOrderContext().getCurrentState().getDisplayName());
        assert stateManager.isInState(NexsOrderState.PICKED);
        
        // Continue until QC_DONE
        stateManager.transitionToState(NexsOrderState.IN_QC);
        stateManager.transitionToState(NexsOrderState.QC_DONE);
        
        log.info("Order reached QC_DONE state");
        log.info("Time in current state: {} ms", stateManager.getTimeInCurrentState());
        
        assert stateManager.isInState(NexsOrderState.QC_DONE);
        log.info("✅ Step-by-step transitions completed successfully\n");
    }
    
    /**
     * Example 6: State History and Monitoring
     * Shows how to track state history and monitor transition timing.
     */
    @Test
    public void example6_StateHistoryAndMonitoring() throws InterruptedException {
        log.info("=== Example 6: State History and Monitoring ===");
        
        // Create order context
        NexsOrderContext orderContext = createSampleOrderContext("ORD-006", NexsOrderState.CREATED);
        
        NexsOrderStateHelper stateManager = NexsOrderStateHelper.builder()
                .orderContext(orderContext)
                .build();
        
        // Perform transitions with delays to show timing
        long startTime = System.currentTimeMillis();
        
        stateManager.transitionToNextState();
        Thread.sleep(100); // Simulate processing time
        
        stateManager.transitionToNextState();
        Thread.sleep(200); // Simulate processing time
        
        stateManager.transitionToNextState();
        
        long totalTime = System.currentTimeMillis() - startTime;
        
        // Show state history and timing
        NexsOrderContext context = stateManager.getOrderContext();
        log.info("Current state: {}", context.getCurrentState().getDisplayName());
        log.info("Total transition time: {} ms", totalTime);
        log.info("Time in current state: {} ms", stateManager.getTimeInCurrentState());
        
        log.info("Complete state history:");
        context.getStateHistory().forEach(record -> 
                log.info("  {}", record.toString()));
        
        // Check if order has been in specific states
        log.info("Has been in PROCESSING: {}", context.hasBeenInState(NexsOrderState.PROCESSING));
        log.info("Has been in DISPATCHED: {}", context.hasBeenInState(NexsOrderState.DISPATCHED));
        
        log.info("✅ State history and monitoring example completed\n");
    }
    
    /**
     * Example 7: Error Handling - Invalid Transition
     * Shows how the system handles invalid state transitions.
     */
    @Test(expectedExceptions = IllegalStateException.class)
    public void example7_InvalidTransitionHandling() {
        log.info("=== Example 7: Invalid Transition Handling ===");
        
        // Create order context in CREATED state
        NexsOrderContext orderContext = createSampleOrderContext("ORD-007", NexsOrderState.CREATED);
        
        try {
            // Try to transition directly to DISPATCHED (should fail)
            NexsOrderStateHelper.builder()
                    .orderContext(orderContext)
                    .build()
                    .transitionToState(NexsOrderState.DISPATCHED);
        } catch (IllegalStateException e) {
            log.info("Expected error caught: {}", e.getMessage());
            log.info("✅ Invalid transition properly handled");
            throw e; // Re-throw for test framework
        }
    }
    
    /**
     * Example 8: Batch Processing Multiple Orders
     * Shows how to process multiple orders efficiently.
     */
    @Test
    public void example8_BatchProcessingMultipleOrders() {
        log.info("=== Example 8: Batch Processing Multiple Orders ===");
        
        String[] orderIds = {"ORD-BATCH-001", "ORD-BATCH-002", "ORD-BATCH-003"};
        
        for (String orderId : orderIds) {
            log.info("Processing order: {}", orderId);
            
            // Create order context
            NexsOrderContext orderContext = createSampleOrderContext(orderId, NexsOrderState.CREATED);
            
            // Process order to PICKED state
            NexsOrderStateHelper.builder()
                    .orderContext(orderContext)
                    .sequentialTransition(true)
                    .build()
                    .transitionToFinalState(NexsOrderState.PICKED);
            
            log.info("Order {} processed to: {}", orderId, orderContext.getCurrentState().getDisplayName());
        }
        
        log.info("✅ Batch processing completed successfully\n");
    }
    
    /**
     * Helper method to create sample order context for examples
     */
    private NexsOrderContext createSampleOrderContext(String orderId, NexsOrderState initialState) {
        return NexsOrderContext.builder()
                .orderId(orderId)
                .customerId("CUST-" + orderId.substring(orderId.length() - 3))
                .storeId("STORE-001")
                .warehouseId("WH-001")
                .currentState(initialState)
                .lastStateChange(LocalDateTime.now())
                .headers(NexsOrderContext.Headers.builder()
                        .authToken("sample-auth-token-" + orderId)
                        .sessionToken("session-token")
                        .userId("user-automation")
                        .storeId("STORE-001")
                        .warehouseId("WH-001")
                        .build())
                .build();
    }
}

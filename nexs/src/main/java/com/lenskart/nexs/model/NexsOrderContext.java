package com.lenskart.nexs.model;

import com.lenskart.commons.model.NexsOrderState;
import com.lenskart.nexs.model.orderqc.FetchOrderScannedEntityResponse;
import com.lenskart.nexs.wms.response.order.OrderItemResponse;
import lombok.Builder;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

import static com.lenskart.commons.model.NexsOrderState.IN_PICKING;

/**
 * Context object for NEXS order operations.
 * Contains all necessary information for order state transitions and processing.
 */
@Data
@Builder
public class NexsOrderContext {
    
    // Basic order information
    private String customerId;
    private String storeId;
    private String warehouseId;
    @Builder.Default
    String facilityCode="NXS2";
    @Builder.Default
    String countryCode="IN";
    List<String> shipmentIds;
    String shippingId;
    String incrementId;
    String nexsOrderId;
    String awdNumber;
    String shipToStoreRequired;
    Boolean isBulkOrder;
    Boolean isShipToStore;
    String orderType;
    String navChannel;
    String processingType;
    String wmsOrderId;
    String legalOwner;
    Boolean isAddverbPicking;
    String picklistId;
    String pickingSummaryId;
    String frameBarcode;
    String trayBarcode;
    String rightLensBarcode;
    String leftLensBarcode;
    String barcode;
    String fittingId;
    String shippingProviderCode;
    String shippingProvider;
    String manifestId;
    String channelType;
    String productId;
    String rightPid;
    String leftPid;
    String frameProductId;
    Boolean isLoyaltyItemPresent;
    String uwItemId;
    String trayId;
    String orderItemHeaderId;
    String UnicomOrderCode;
    String boxCode;
    String storeCode;
    int magentoItemId;
    String poNumber;
    String customerCode;
    String doType;
    String userName;
    String filePath;
    String eventType;
    String boxBarcode;
    String unicomTransferCode;
    String transferCode;
    List<String> barcodes;
    String stationDesc;
    String stationId;
    String shipmentStatus;
    BigDecimal collectibleAmount;
    String hsnCode;
    @Builder.Default
    String stateCode = "08";
    String hsnTaxInfo;
    String vendorUnitCostPrice;
    String igstPer;
    String cgstPer;
    String sgstPer;
    String ugstPer;
    String flatPer;
    String igstRate;
    String cgst;
    String quantity;
    String vendorCode;
    String cgstRate;
    String sgstRate;
    String ugstRate;
    String flatRate;
    String currency;
    String facilityStateCode;
    String getBoxCount;

    @Builder.Default
    Boolean isValidationRequired=false;
    @Builder.Default
    int statusCode = 200;
    List<OrderItemResponse> orderItemResponses;
    @Builder.Default
    String supplierId = "WHOLESALE01";
 //   JSONArray orderQcStatusList;
   FetchOrderScannedEntityResponse fetchOrderScannedEntityResponse;

    @Builder.Default
    private NexsOrderState currentState = IN_PICKING;
    private LocalDateTime lastStateChange;
    
    // State transition history
    @Builder.Default
    private List<StateTransitionRecord> stateHistory = new ArrayList<>();


    // Authentication and headers
   // private Headers headers;
    
    /**
     * Add a state transition record to the history
     * 
     * @param fromState The source state
     * @param toState The target state
     */
    public void addStateTransitionHistory(NexsOrderState fromState, NexsOrderState toState) {
        StateTransitionRecord record = StateTransitionRecord.builder()
                .fromState(fromState)
                .toState(toState)
                .timestamp(LocalDateTime.now())
                .build();
        
        this.stateHistory.add(record);
        this.lastStateChange = LocalDateTime.now();
    }
    
    /**
     * Get the last state transition record
     * 
     * @return The most recent state transition record, or null if no transitions
     */
    public StateTransitionRecord getLastTransition() {
        if (stateHistory.isEmpty()) {
            return null;
        }
        return stateHistory.get(stateHistory.size() - 1);
    }
    
    /**
     * Check if the order has been in a specific state
     * 
     * @param state The state to check
     * @return true if the order has been in the specified state
     */
    public boolean hasBeenInState(NexsOrderState state) {
        return stateHistory.stream()
                .anyMatch(record -> record.getToState() == state || record.getFromState() == state)
                || currentState == state;
    }
    
    /**
     * Get the time spent in current state
     * 
     * @return Duration in milliseconds, or 0 if no state change recorded
     */
    public long getTimeInCurrentState() {
        if (lastStateChange == null) {
            return 0;
        }
        return java.time.Duration.between(lastStateChange, LocalDateTime.now()).toMillis();
    }

    Headers headers;

    /**
     * Headers for API calls
     */
    @Data
    @Builder
    public static class Headers {
         @Builder.Default
        private String facilityCode="NXS2";
        private String jwtToken;
        private String lenskartAppId;
        private String sourceDomain;
        private String workstationId;
        private String dateTime;
    }
    
    /**
     * Record of a state transition
     */
    @Data
    @Builder
    public static class StateTransitionRecord {
        private NexsOrderState fromState;
        private NexsOrderState toState;
        private LocalDateTime timestamp;
        private String notes;
        private String performedBy;
        
        @Override
        public String toString() {
            return String.format("%s -> %s at %s", 
                fromState.getDisplayName(), 
                toState.getDisplayName(), 
                timestamp);
        }
    }
}

package com.lenskart.nexs.model;

import com.lenskart.commons.model.NexsOrderState;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * Context object for NEXS order operations.
 * Contains all necessary information for order state transitions and processing.
 */
@Data
@Builder
public class NexsOrderContext {
    
    // Basic order information
    private String orderId;
    private String customerId;
    private String storeId;
    private String warehouseId;
    
    // Current state information
    private NexsOrderState currentState;
    private LocalDateTime lastStateChange;
    
    // State transition history
    @Builder.Default
    private List<StateTransitionRecord> stateHistory = new ArrayList<>();
    
    // Processing information
    private String processingId;
    private String estimatedProcessingTime;
    private String processingNotes;
    
    // Picking information
    private String pickingAssignedTo;
    private String pickingLocation;
    private LocalDateTime pickingStartTime;
    private LocalDateTime pickingEndTime;
    private List<String> pickedItems;
    
    // Quality Control information
    private String qcAssignedTo;
    private String qcNotes;
    private LocalDateTime qcStartTime;
    private LocalDateTime qcEndTime;
    private boolean qcPassed;
    
    // Invoice information
    private String invoiceId;
    private String invoiceNumber;
    private LocalDateTime invoiceGeneratedTime;
    
    // Dispatch information
    private String dispatchId;
    private String trackingNumber;
    private String courierPartner;
    private LocalDateTime dispatchTime;
    private String deliveryAddress;
    
    // Authentication and headers
    private Headers headers;
    
    /**
     * Add a state transition record to the history
     * 
     * @param fromState The source state
     * @param toState The target state
     */
    public void addStateTransitionHistory(NexsOrderState fromState, NexsOrderState toState) {
        StateTransitionRecord record = StateTransitionRecord.builder()
                .fromState(fromState)
                .toState(toState)
                .timestamp(LocalDateTime.now())
                .build();
        
        this.stateHistory.add(record);
        this.lastStateChange = LocalDateTime.now();
    }
    
    /**
     * Get the last state transition record
     * 
     * @return The most recent state transition record, or null if no transitions
     */
    public StateTransitionRecord getLastTransition() {
        if (stateHistory.isEmpty()) {
            return null;
        }
        return stateHistory.get(stateHistory.size() - 1);
    }
    
    /**
     * Check if the order has been in a specific state
     * 
     * @param state The state to check
     * @return true if the order has been in the specified state
     */
    public boolean hasBeenInState(NexsOrderState state) {
        return stateHistory.stream()
                .anyMatch(record -> record.getToState() == state || record.getFromState() == state)
                || currentState == state;
    }
    
    /**
     * Get the time spent in current state
     * 
     * @return Duration in milliseconds, or 0 if no state change recorded
     */
    public long getTimeInCurrentState() {
        if (lastStateChange == null) {
            return 0;
        }
        return java.time.Duration.between(lastStateChange, LocalDateTime.now()).toMillis();
    }
    
    /**
     * Headers for API calls
     */
    @Data
    @Builder
    public static class Headers {
        private String authToken;
        private String sessionToken;
        private String userId;
        private String storeId;
        private String warehouseId;
    }
    
    /**
     * Record of a state transition
     */
    @Data
    @Builder
    public static class StateTransitionRecord {
        private NexsOrderState fromState;
        private NexsOrderState toState;
        private LocalDateTime timestamp;
        private String notes;
        private String performedBy;
        
        @Override
        public String toString() {
            return String.format("%s -> %s at %s", 
                fromState.getDisplayName(), 
                toState.getDisplayName(), 
                timestamp);
        }
    }
}

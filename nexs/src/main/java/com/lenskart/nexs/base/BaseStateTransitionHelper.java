package com.lenskart.nexs.base;

import com.lenskart.commons.model.NexsOrderState;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.nexs.helpers.NexsBaseHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

/**
 * Base implementation for state transition helpers.
 * Provides common functionality and structure for all state transition operations.
 */
@SuperBuilder
@Slf4j
public abstract class BaseStateTransitionHelper extends NexsBaseHelper{
    
    protected NexsOrderContext nexsOrderContext;
    /**
     * Get the expected source state for this transition
     * 
     * @return The source state
     */
    public abstract NexsOrderState getExpectedSourceState();
    
    /**
     * Get the target state for this transition
     * 
     * @return The target state
     */
    public abstract NexsOrderState getTargetState();
    
    /**
     * Validate that the current state matches the expected source state
     */

    public void validateTransition() {

        if (nexsOrderContext == null) {
            throw new IllegalStateException("Order context cannot be null");
        }

        if (nexsOrderContext.getIncrementId() == null || nexsOrderContext.getIncrementId().trim().isEmpty()) {
            throw new IllegalStateException("Order ID cannot be null or empty");
        }

        if (nexsOrderContext.getCurrentState() == null) {
            throw new IllegalStateException("Current state cannot be null");
        }

        // Validate specific state requirements
        NexsOrderState currentState = getExpectedSourceState();
        NexsOrderState expectedState = getTargetState();

        log.info("Current state: {}, Expected state: {}", currentState, expectedState);


        // Validate that transition is allowed
        // this has been commented out to handle FR0 Orders
//        if (!currentState.canTransitionTo(getTargetState())) {
//            throw new IllegalStateException(
//                String.format("Transition from %s to %s is not allowed",
//                    currentState.getDisplayName(), getTargetState().getDisplayName())
//            );
//        }
    }
    
    /**
     * Log the start of a specific operation
     * 
     * @param operation The operation being performed
     */
    protected void logOperationStart(String operation) {
      //  log.info("Starting {} for order: {}", operation, orderContext.getIncrementId());
    }
    
    /**
     * Log the completion of a specific operation
     * 
     * @param operation The operation that was completed
     */
    protected void logOperationComplete(String operation) {
      //  log.info("Completed {} for order: {}", operation, orderContext.getIncrementId());
    }
    
    /**
     * Log an error during operation
     * 
     * @param operation The operation that failed
     * @param error The error that occurred
     */
    protected void logOperationError(String operation, String error) {
       // log.error("Failed {} for order {}: {}", operation, orderContext.getIncrementId(), error);
    }
}

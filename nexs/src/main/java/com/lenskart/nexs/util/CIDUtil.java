package com.lenskart.nexs.util;

import com.lenskart.commons.model.ProductId;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.helpers.cid.GetConsolidatedInvInfo;
import com.lenskart.nexs.helpers.ims.AddGAAInventoryHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.IntStream;

import static com.lenskart.nexs.util.CommonNexsUtil.legalOwnerMapping;

@Slf4j
public class CIDUtil {

    public static void ensureMinimumInventory(ProductId product, int requiredInventory) {
        String country = product.name().split("_")[0];

        List<String> facilities = switch (country) {
            case "IN" -> List.of("NXS2", "QNXS2");
            case "SG", "ID", "TH" -> List.of("SGNXS1");
            case "AE", "SA" -> List.of("UAE1");
            default -> List.of();
        };

        long combinedInventory = 0;

        for (String facility : facilities) {
            NexsOrderContext nexsOrderContext = NexsOrderContext.builder()
                    .productId(product.getProductId())
                    .facilityCode(facility)
                    .build();

            GetConsolidatedInvInfo getConsolidatedInvInfo = GetConsolidatedInvInfo.builder()
                    .nexsOrderContext(nexsOrderContext)
                    .build();

            getConsolidatedInvInfo.test();

            long availableInventory = getConsolidatedInvInfo.getConsolidatedInvInfo().getAvailableInventory();
            log.info("Available inventory in facility {}: {}", facility, availableInventory);

            combinedInventory += availableInventory;
        }

        log.info("Total combined inventory for {}: {}", country, combinedInventory);

        if (combinedInventory < requiredInventory && !facilities.isEmpty()) {
            int inventoryToAdd = (int) (requiredInventory - combinedInventory);
            String facility = facilities.getFirst();

            log.info("Adding {} units to facility {} to reach combined target of {}",
                    inventoryToAdd, facility, requiredInventory);

            IntStream.range(0, inventoryToAdd)
                    .parallel()
                    .forEach(i -> AddGAAInventoryHelper.builder()
                            .location(Constants.TEST_LOCATION)
                            .facility(facility)
                            .pid(Integer.parseInt(product.getProductId()))
                            .legalOwner(legalOwnerMapping(facility))
                            .build()
                            .test());
        }
    }
}
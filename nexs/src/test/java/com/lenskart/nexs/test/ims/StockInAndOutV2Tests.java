package com.lenskart.nexs.test.ims;

import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.GenericUtils;
import com.lenskart.nexs.constants.Constants;
import com.lenskart.nexs.helpers.imshelpers.StockInAndOutV2Helper;
import org.testng.annotations.Optional;
import org.testng.annotations.Parameters;
import org.testng.annotations.Test;

import java.util.List;

public class StockInAndOutV2Tests {

    @Parameters({ "pid", "location", "facility", "legalOwner" })
    @Test(description = "Perform stock-in and stock-out transitions using different IMS operations")
    public void stockInAndOutV2(
            @Optional("131932") int pid,
            @Optional("test-1-1") String location,
            @Optional("QNXS2") String facility,
            @Optional("LKIN") String legalOwner
    ) {
        List<String> operations = List.of(Constants.GRN_QC_PASS, Constants.PUTAWAY_PENDING, Constants.PUTAWAY_COMPLETE);
        String barcode = GenericUtils.genrateRandomNumericString(4) +
                GenericUtils.genrateRandomAlphabeticString(4).toUpperCase();

        for (String operation : operations) {
            String resolvedLocation = switch (operation) {
                case Constants.GRN_QC_PASS -> "GRN";
                case Constants.PUTAWAY_PENDING -> "";
                case Constants.PUTAWAY_COMPLETE -> location;
                default -> throw new IllegalArgumentException("Unsupported IMS operation: " + operation);
            };

            StockInAndOutV2Helper.builder()
                    .barcode(barcode)
                    .facility(facility)
                    .location(resolvedLocation)
                    .operation(operation)
                    .pid(pid)
                    .legalOwner(legalOwner)
                    .updatedBy("Automation")
                    .build()
                    .test();

            AwaitUtils.sleepMillis(500); // prevent flooding the API
        }
    }
}

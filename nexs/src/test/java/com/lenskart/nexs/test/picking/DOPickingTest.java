package com.lenskart.nexs.test.picking;

import com.lenskart.nexs.helpers.pickinghelpers.DOPickingHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import org.testng.annotations.Test;

public class DOPickingTest {

    @Test
    public void doPicking() {


        NexsOrderContext  nexsOrderContext = NexsOrderContext
                .builder()
                .shippingId("SNXS2260000004446057")
                .headers(NexsOrderContext.Headers.builder().build())
                .build();

        DOPickingHelper.builder()
                .nexsOrderContext(nexsOrderContext)
                .build()
                .test();
    }
}

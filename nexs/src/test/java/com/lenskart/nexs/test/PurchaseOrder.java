package com.lenskart.nexs.test;

import com.lenskart.nexs.helpers.lkauth.NexsAuthHelper;
import com.lenskart.nexs.helpers.po.GeneratePoNumHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import org.testng.annotations.Test;

public class PurchaseOrder {
    @Test
    public void createPO() {

        NexsOrderContext  nexsOrderContext = NexsOrderContext
                .builder()
                .headers(NexsOrderContext.Headers.builder().build())
                .build();
        NexsAuthHelper.builder().nexsOrderContext(nexsOrderContext).build().test();
        GeneratePoNumHelper.builder()
                .nexsOrderContext(nexsOrderContext)
                .build()
                .test();


    }
}

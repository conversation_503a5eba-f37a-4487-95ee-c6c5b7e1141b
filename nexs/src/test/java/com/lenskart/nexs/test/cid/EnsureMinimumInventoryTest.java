package com.lenskart.nexs.test.cid;

import com.lenskart.commons.model.ProductId;
import com.lenskart.nexs.util.CIDUtil;
import org.testng.annotations.DataProvider;
import org.testng.annotations.Test;

public class EnsureMinimumInventoryTest {

    @DataProvider(name = "countryProducts")
    public Object[][] countryProducts() {
        return new Object[][]{
                {ProductId.IN_EYEGLASSES},
                {ProductId.IN_SUNGLASSES},
                {ProductId.SG_EYEGLASSES},
                {ProductId.AE_EYEGLASSES}
        };
    }

    @Test(dataProvider = "countryProducts")
    public void addInventory(ProductId product) {
        CIDUtil.ensureMinimumInventory(product, 50);
    }
}

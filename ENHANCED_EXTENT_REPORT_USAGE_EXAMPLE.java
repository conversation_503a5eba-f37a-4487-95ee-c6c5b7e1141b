package com.lenskart.example.tests;

import com.lenskart.commons.utils.ExtentReportUtils;
import org.testng.Assert;
import org.testng.annotations.Test;

/**
 * Example test class demonstrating enhanced ExtentReport logging
 * This shows how to use ExtentReportUtils for detailed step-by-step logging
 */
public class EnhancedExtentReportUsageExample {

    @Test(description = "Example test demonstrating enhanced extent report logging")
    public void testWithEnhancedLogging() {
        
        // Create sections for better organization
        ExtentReportUtils.createSection("Test Setup");
        
        // Log test data
        ExtentReportUtils.logTestData("Username", "<EMAIL>");
        ExtentReportUtils.logTestData("Environment", "preprod");
        ExtentReportUtils.logTestData("Test Category", "REGRESSION");
        
        // Log test steps
        ExtentReportUtils.logStep("🔧", "Initialize Test Data", "Setting up test data for user registration");
        ExtentReportUtils.logInfo("Test data initialized successfully");
        
        ExtentReportUtils.createSection("API Testing");
        
        // Log API request
        String requestPayload = "{\n  \"username\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}";
        ExtentReportUtils.logApiRequest("POST", "https://api.example.com/login", requestPayload);
        
        // Simulate API call and log response
        ExtentReportUtils.logApiResponse(200, "{\"status\": \"success\", \"token\": \"abc123\"}", 250);
        
        ExtentReportUtils.createSection("Database Validation");
        
        // Log database operations
        ExtentReportUtils.logDatabaseOperation("SELECT", "users", "WHERE username = '<EMAIL>'");
        ExtentReportUtils.logInfo("User found in database");
        
        ExtentReportUtils.createSection("Assertions");
        
        // Log assertions
        String expectedStatus = "success";
        String actualStatus = "success";
        ExtentReportUtils.logAssertion(expectedStatus, actualStatus, true);
        
        // Actual assertion
        Assert.assertEquals(actualStatus, expectedStatus, "Status should match");
        ExtentReportUtils.logPass("Status assertion passed successfully");
        
        ExtentReportUtils.createSection("Test Cleanup");
        ExtentReportUtils.logInfo("Cleaning up test data");
        ExtentReportUtils.logPass("Test completed successfully");
    }
    
    @Test(description = "Example test demonstrating failure logging")
    public void testWithFailureLogging() {
        
        ExtentReportUtils.createSection("Test Setup");
        ExtentReportUtils.logInfo("Starting test that will demonstrate failure logging");
        
        ExtentReportUtils.createSection("API Testing");
        
        // Log API request that will fail
        ExtentReportUtils.logApiRequest("GET", "https://api.example.com/invalid-endpoint", null);
        
        // Log failed API response
        ExtentReportUtils.logApiResponse(404, "{\"error\": \"Endpoint not found\"}", 150);
        ExtentReportUtils.logFail("API endpoint returned 404 error");
        
        ExtentReportUtils.createSection("Assertions");
        
        // Log failed assertion
        String expected = "200";
        String actual = "404";
        ExtentReportUtils.logAssertion(expected, actual, false);
        
        // This will fail and demonstrate failure logging
        Assert.assertEquals(actual, expected, "Status code should be 200");
    }
    
    @Test(description = "Example test demonstrating warning and skip logging")
    public void testWithWarningAndSkipLogging() {
        
        ExtentReportUtils.createSection("Test Setup");
        ExtentReportUtils.logInfo("Starting test with warnings and skips");
        
        ExtentReportUtils.createSection("Conditional Logic");
        
        // Log warning
        ExtentReportUtils.logWarning("Test environment is not fully configured");
        ExtentReportUtils.logWarning("Some features may not work as expected");
        
        // Log skip
        ExtentReportUtils.logSkip("Skipping payment validation due to test environment limitations");
        
        ExtentReportUtils.createSection("Basic Validation");
        
        // Log some basic steps
        ExtentReportUtils.logStep("🔍", "Validate Basic Functionality", "Checking if basic features work");
        ExtentReportUtils.logInfo("Basic validation completed");
        ExtentReportUtils.logPass("Test completed with warnings");
    }
    
    @Test(description = "Example test demonstrating database operations logging")
    public void testWithDatabaseLogging() {
        
        ExtentReportUtils.createSection("Database Setup");
        ExtentReportUtils.logInfo("Connecting to test database");
        
        // Log various database operations
        ExtentReportUtils.logDatabaseOperation("INSERT", "test_users", "Creating test user record");
        ExtentReportUtils.logDatabaseOperation("SELECT", "test_users", "Verifying user was created");
        ExtentReportUtils.logDatabaseOperation("UPDATE", "test_users", "Updating user status to active");
        ExtentReportUtils.logDatabaseOperation("DELETE", "test_users", "Cleaning up test data");
        
        ExtentReportUtils.createSection("Validation");
        ExtentReportUtils.logInfo("Database operations completed successfully");
        ExtentReportUtils.logPass("All database operations executed without errors");
    }
    
    @Test(description = "Example test demonstrating comprehensive logging")
    public void testComprehensiveLogging() {
        
        // Test setup
        ExtentReportUtils.createSection("🚀 Test Initialization");
        ExtentReportUtils.logTestData("Test ID", "TC_001");
        ExtentReportUtils.logTestData("Priority", "High");
        ExtentReportUtils.logTestData("Module", "User Management");
        
        // Step 1: User Registration
        ExtentReportUtils.createSection("👤 User Registration");
        ExtentReportUtils.logStep("📝", "Fill Registration Form", "Entering user details in registration form");
        ExtentReportUtils.logTestData("Email", "<EMAIL>");
        ExtentReportUtils.logTestData("Phone", "+1234567890");
        
        String registrationPayload = "{\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"+1234567890\",\n  \"name\": \"Test User\"\n}";
        ExtentReportUtils.logApiRequest("POST", "/api/users/register", registrationPayload);
        ExtentReportUtils.logApiResponse(201, "{\"id\": 12345, \"status\": \"created\"}", 300);
        
        // Step 2: Email Verification
        ExtentReportUtils.createSection("📧 Email Verification");
        ExtentReportUtils.logStep("📬", "Check Email", "Verifying email was sent");
        ExtentReportUtils.logDatabaseOperation("SELECT", "email_queue", "WHERE recipient = '<EMAIL>'");
        ExtentReportUtils.logInfo("Verification email found in queue");
        
        // Step 3: Account Activation
        ExtentReportUtils.createSection("✅ Account Activation");
        ExtentReportUtils.logStep("🔗", "Click Verification Link", "Activating user account");
        ExtentReportUtils.logApiRequest("GET", "/api/users/verify?token=abc123", null);
        ExtentReportUtils.logApiResponse(200, "{\"status\": \"verified\"}", 150);
        
        // Step 4: Login Test
        ExtentReportUtils.createSection("🔐 Login Validation");
        String loginPayload = "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"password123\"\n}";
        ExtentReportUtils.logApiRequest("POST", "/api/auth/login", loginPayload);
        ExtentReportUtils.logApiResponse(200, "{\"token\": \"jwt_token_here\", \"expires\": \"2024-01-01\"}", 200);
        
        // Assertions
        ExtentReportUtils.createSection("🎯 Final Validations");
        ExtentReportUtils.logAssertion("verified", "verified", true);
        ExtentReportUtils.logAssertion("active", "active", true);
        
        // Cleanup
        ExtentReportUtils.createSection("🧹 Test Cleanup");
        ExtentReportUtils.logDatabaseOperation("DELETE", "users", "WHERE email = '<EMAIL>'");
        ExtentReportUtils.logInfo("Test user removed from database");
        ExtentReportUtils.logPass("Test completed successfully with full user lifecycle validation");
    }
}

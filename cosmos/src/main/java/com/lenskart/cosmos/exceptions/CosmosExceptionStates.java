package com.lenskart.cosmos.exceptions;

import lombok.Getter;

/**
 * Enum containing exception states and error codes for the Cosmos module
 */
@Getter
public enum CosmosExceptionStates {
    
    // Configuration related exceptions
    CONFIG_NOT_FOUND("COSMOS_CONFIG_001", "Configuration not found", "Cosmos configuration file or environment config not found"),
    CONFIG_LOAD_FAILED("COSMOS_CONFIG_002", "Configuration load failed", "Failed to load cosmos configuration from file"),
    INVALID_ENVIRONMENT("COSMOS_CONFIG_003", "Invalid environment", "Specified environment configuration not found"),
    
    // Service related exceptions
    SERVICE_NOT_FOUND("COSMOS_SERVICE_001", "Service not found", "Specified service configuration not found"),
    SERVICE_UNAVAILABLE("COSMOS_SERVICE_002", "Service unavailable", "Cosmos service is currently unavailable"),
    SERVICE_TIMEOUT("COSMOS_SERVICE_003", "Service timeout", "Request to cosmos service timed out"),
    
    // Authentication related exceptions
    AUTH_TOKEN_MISSING("COSMOS_AUTH_001", "Authentication token missing", "Authentication token is required but not provided"),
    AUTH_TOKEN_INVALID("COSMOS_AUTH_002", "Authentication token invalid", "Provided authentication token is invalid or expired"),
    AUTH_FAILED("COSMOS_AUTH_003", "Authentication failed", "Authentication failed for cosmos service"),
    
    // API related exceptions
    API_CALL_FAILED("COSMOS_API_001", "API call failed", "API call to cosmos service failed"),
    INVALID_RESPONSE("COSMOS_API_002", "Invalid response", "Received invalid response from cosmos service"),
    ENDPOINT_NOT_FOUND("COSMOS_API_003", "Endpoint not found", "Specified endpoint not found"),
    
    // Order related exceptions
    ORDER_NOT_FOUND("COSMOS_ORDER_001", "Order not found", "Order with specified ID not found"),
    ORDER_INVALID_STATUS("COSMOS_ORDER_002", "Invalid order status", "Order has invalid or unexpected status"),
    ORDER_ACCESS_DENIED("COSMOS_ORDER_003", "Order access denied", "Access denied for the specified order"),
    
    // Data validation exceptions
    INVALID_ORDER_ID("COSMOS_DATA_001", "Invalid order ID", "Provided order ID is invalid or malformed"),
    MISSING_REQUIRED_FIELD("COSMOS_DATA_002", "Missing required field", "Required field is missing in request"),
    INVALID_DATA_FORMAT("COSMOS_DATA_003", "Invalid data format", "Data format is invalid or not supported"),
    
    // Network related exceptions
    NETWORK_ERROR("COSMOS_NETWORK_001", "Network error", "Network error occurred while communicating with cosmos service"),
    CONNECTION_FAILED("COSMOS_NETWORK_002", "Connection failed", "Failed to establish connection to cosmos service"),
    REQUEST_TIMEOUT("COSMOS_NETWORK_003", "Request timeout", "Request to cosmos service timed out"),
    
    // General exceptions
    UNKNOWN_ERROR("COSMOS_GENERAL_001", "Unknown error", "An unknown error occurred in cosmos module"),
    OPERATION_FAILED("COSMOS_GENERAL_002", "Operation failed", "The requested operation failed"),
    INTERNAL_ERROR("COSMOS_GENERAL_003", "Internal error", "Internal error occurred in cosmos module");
    
    private final String errorCode;
    private final String errorMessage;
    private final String description;
    
    /**
     * Constructor for CosmosExceptionStates
     * 
     * @param errorCode Unique error code
     * @param errorMessage Short error message
     * @param description Detailed description of the error
     */
    CosmosExceptionStates(String errorCode, String errorMessage, String description) {
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
        this.description = description;
    }
    
    /**
     * Creates a formatted error message with additional context
     * 
     * @param additionalInfo Additional information to include in the error message
     * @return Formatted error message
     */
    public String getFormattedMessage(String additionalInfo) {
        if (additionalInfo != null && !additionalInfo.trim().isEmpty()) {
            return String.format("[%s] %s: %s", errorCode, errorMessage, additionalInfo);
        }
        return String.format("[%s] %s", errorCode, errorMessage);
    }
    
    /**
     * Creates a detailed error message with description and additional context
     * 
     * @param additionalInfo Additional information to include in the error message
     * @return Detailed error message
     */
    public String getDetailedMessage(String additionalInfo) {
        StringBuilder message = new StringBuilder();
        message.append(String.format("[%s] %s", errorCode, errorMessage));
        message.append(String.format("\nDescription: %s", description));
        
        if (additionalInfo != null && !additionalInfo.trim().isEmpty()) {
            message.append(String.format("\nAdditional Info: %s", additionalInfo));
        }
        
        return message.toString();
    }
    
    /**
     * Checks if this exception state is related to configuration
     * 
     * @return true if this is a configuration related exception
     */
    public boolean isConfigurationError() {
        return errorCode.startsWith("COSMOS_CONFIG_");
    }
    
    /**
     * Checks if this exception state is related to authentication
     * 
     * @return true if this is an authentication related exception
     */
    public boolean isAuthenticationError() {
        return errorCode.startsWith("COSMOS_AUTH_");
    }
    
    /**
     * Checks if this exception state is related to API calls
     * 
     * @return true if this is an API related exception
     */
    public boolean isApiError() {
        return errorCode.startsWith("COSMOS_API_");
    }
    
    /**
     * Checks if this exception state is related to orders
     * 
     * @return true if this is an order related exception
     */
    public boolean isOrderError() {
        return errorCode.startsWith("COSMOS_ORDER_");
    }
    
    /**
     * Checks if this exception state is related to data validation
     * 
     * @return true if this is a data validation exception
     */
    public boolean isDataValidationError() {
        return errorCode.startsWith("COSMOS_DATA_");
    }
    
    /**
     * Checks if this exception state is related to network issues
     * 
     * @return true if this is a network related exception
     */
    public boolean isNetworkError() {
        return errorCode.startsWith("COSMOS_NETWORK_");
    }
    
    /**
     * Gets the category of this exception based on the error code prefix
     * 
     * @return Exception category
     */
    public String getCategory() {
        String[] parts = errorCode.split("_");
        if (parts.length >= 2) {
            return parts[1]; // Returns CONFIG, SERVICE, AUTH, API, ORDER, DATA, NETWORK, or GENERAL
        }
        return "UNKNOWN";
    }
    
    @Override
    public String toString() {
        return String.format("%s: %s - %s", errorCode, errorMessage, description);
    }
}

package com.lenskart.cosmos.endpoints;

import com.lenskart.cosmos.config.CosmosConfig;
import com.lenskart.cosmos.config.CosmosConfigLoader;
import lombok.extern.slf4j.Slf4j;

/**
 * Enum containing all Cosmos service endpoints
 */
@Slf4j
public enum CosmosEndpoints {
    
    // Stateview Service Endpoints
    GET_ORDER_BY_ID("stateviewService", "/cosmos-stateview/v1/order/{orderId}", "GET", "Get order details by order ID"),
    GET_ORDER_STATUS("stateviewService", "/cosmos-stateview/v1/order/{orderId}/status", "GET", "Get order status by order ID"),
    GET_ORDER_HISTORY("stateviewService", "/cosmos-stateview/v1/order/{orderId}/history", "GET", "Get order history by order ID"),
    
    // Additional endpoints can be added here as needed
    HEALTH_CHECK("stateviewService", "/cosmos-stateview/health", "GET", "Health check endpoint");
    
    private final String serviceName;
    private final String path;
    private final String method;
    private final String description;
    
    /**
     * Constructor for CosmosEndpoints
     * 
     * @param serviceName Name of the service (should match baseUrls in config)
     * @param path Endpoint path
     * @param method HTTP method
     * @param description Description of the endpoint
     */
    CosmosEndpoints(String serviceName, String path, String method, String description) {
        this.serviceName = serviceName;
        this.path = path;
        this.method = method;
        this.description = description;
    }
    
    /**
     * Gets the complete URL for this endpoint
     * 
     * @return Complete URL including base URL and path
     */
    public String getUrl() {
        try {
            CosmosConfig.EnvironmentConfig envConfig = CosmosConfigLoader.getCurrentEnvironmentConfig();
            String baseUrl = envConfig.getBaseUrl(serviceName);
            
            if (baseUrl == null) {
                throw new RuntimeException("Base URL not found for service: " + serviceName);
            }
            
            // Remove trailing slash from base URL if present
            if (baseUrl.endsWith("/")) {
                baseUrl = baseUrl.substring(0, baseUrl.length() - 1);
            }
            
            // Ensure path starts with /
            String fullPath = path.startsWith("/") ? path : "/" + path;
            
            String fullUrl = baseUrl + fullPath;
            log.debug("Generated URL for {}: {}", this.name(), fullUrl);
            
            return fullUrl;
            
        } catch (Exception e) {
            log.error("Failed to generate URL for endpoint {}: {}", this.name(), e.getMessage());
            throw new RuntimeException("Failed to generate URL for endpoint: " + this.name(), e);
        }
    }
    
    /**
     * Gets the URL with path parameters replaced
     * 
     * @param pathParams Path parameters to replace in the URL
     * @return URL with path parameters replaced
     */
    public String getUrl(Object... pathParams) {
        String url = getUrl();
        
        // Replace path parameters
        for (Object param : pathParams) {
            if (param != null) {
                // Replace the first occurrence of {paramName} or similar pattern
                url = url.replaceFirst("\\{[^}]+\\}", String.valueOf(param));
            }
        }
        
        log.debug("Generated URL with parameters for {}: {}", this.name(), url);
        return url;
    }
    
    /**
     * Gets the service name for this endpoint
     * 
     * @return Service name
     */
    public String getServiceName() {
        return serviceName;
    }
    
    /**
     * Gets the path for this endpoint
     * 
     * @return Endpoint path
     */
    public String getPath() {
        return path;
    }
    
    /**
     * Gets the HTTP method for this endpoint
     * 
     * @return HTTP method
     */
    public String getMethod() {
        return method;
    }
    
    /**
     * Gets the description for this endpoint
     * 
     * @return Endpoint description
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * Checks if this endpoint requires authentication
     * 
     * @return true if authentication is required, false otherwise
     */
    public boolean requiresAuth() {
        // Most endpoints require authentication, health check typically doesn't
        return this != HEALTH_CHECK;
    }
    
    /**
     * Gets the timeout for this endpoint from service configuration
     * 
     * @return Timeout in milliseconds
     */
    public int getTimeout() {
        try {
            CosmosConfig.EnvironmentConfig envConfig = CosmosConfigLoader.getCurrentEnvironmentConfig();
            
            // Try to get service-specific timeout
            if (serviceName.equals("stateviewService")) {
                CosmosConfig.ServiceConfig serviceConfig = envConfig.getService("stateview");
                if (serviceConfig != null) {
                    return serviceConfig.getTimeout();
                }
            }
            
            // Default timeout
            return 30000;
            
        } catch (Exception e) {
            log.warn("Failed to get timeout for endpoint {}, using default: {}", this.name(), e.getMessage());
            return 30000;
        }
    }
    
    /**
     * Gets the retry count for this endpoint from service configuration
     * 
     * @return Retry count
     */
    public int getRetryCount() {
        try {
            CosmosConfig.EnvironmentConfig envConfig = CosmosConfigLoader.getCurrentEnvironmentConfig();
            
            // Try to get service-specific retry count
            if (serviceName.equals("stateviewService")) {
                CosmosConfig.ServiceConfig serviceConfig = envConfig.getService("stateview");
                if (serviceConfig != null) {
                    return serviceConfig.getRetryCount();
                }
            }
            
            // Default retry count
            return 3;
            
        } catch (Exception e) {
            log.warn("Failed to get retry count for endpoint {}, using default: {}", this.name(), e.getMessage());
            return 3;
        }
    }
    
    @Override
    public String toString() {
        return String.format("%s [%s] %s - %s", name(), method, path, description);
    }
}

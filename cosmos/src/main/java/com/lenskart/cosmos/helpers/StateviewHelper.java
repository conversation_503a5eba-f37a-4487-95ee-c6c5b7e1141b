package com.lenskart.cosmos.helpers;

import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cosmos.config.CosmosConfig;
import com.lenskart.cosmos.config.CosmosConfigLoader;
import com.lenskart.cosmos.endpoints.CosmosEndpoints;
import com.lenskart.cosmos.exceptions.CosmosExceptionStates;
import io.restassured.response.Response;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;

/**
 * Helper class for Cosmos Stateview service operations
 * Based on the provided curl: 
 * curl --location 'https://stateview.cosmos.preprod.lenskart.com/cosmos-stateview/v1/order/1930753219' \
 * --header 'X-Auth-Token: Bearer 123'
 */
@Slf4j
public class StateviewHelper {
    
    private final Map<String, String> headers;
    private final CosmosConfig.EnvironmentConfig envConfig;
    
    /**
     * Constructor for StateviewHelper
     */
    public StateviewHelper() {
        this.envConfig = CosmosConfigLoader.getCurrentEnvironmentConfig();
        this.headers = createDefaultHeaders();
    }
    
    /**
     * Constructor with custom authentication token
     * 
     * @param authToken Custom authentication token
     */
    public StateviewHelper(String authToken) {
        this.envConfig = CosmosConfigLoader.getCurrentEnvironmentConfig();
        this.headers = createHeaders(authToken);
    }
    
    /**
     * Gets order details by order ID
     * This method implements the exact functionality from the provided curl command
     * 
     * @param orderId Order ID to retrieve
     * @return Response containing order details
     */
    public Response getOrderById(String orderId) {
        log.info("Getting order details for order ID: {}", orderId);
        
        if (orderId == null || orderId.trim().isEmpty()) {
            throw new RuntimeException(CosmosExceptionStates.INVALID_ORDER_ID.getFormattedMessage("Order ID cannot be null or empty"));
        }
        
        try {
            String url = CosmosEndpoints.GET_ORDER_BY_ID.getUrl(orderId);
            log.info("Making GET request to: {}", url);
            log.debug("Request headers: {}", headers);
            
            Response response = RestUtils.get(url, headers, null);
            
            log.info("Received response with status code: {}", response.getStatusCode());
            log.debug("Response body: {}", response.getBody().asString());
            
            return response;
            
        } catch (Exception e) {
            log.error("Failed to get order details for order ID {}: {}", orderId, e.getMessage());
            throw new RuntimeException(CosmosExceptionStates.API_CALL_FAILED.getFormattedMessage("Failed to get order: " + e.getMessage()), e);
        }
    }
    
    /**
     * Gets order details by order ID with expected status code validation
     * 
     * @param orderId Order ID to retrieve
     * @param expectedStatusCode Expected HTTP status code
     * @return Response containing order details
     */
    public Response getOrderById(String orderId, int expectedStatusCode) {
        log.info("Getting order details for order ID: {} with expected status: {}", orderId, expectedStatusCode);
        
        Response response = getOrderById(orderId);
        
        if (response.getStatusCode() != expectedStatusCode) {
            String errorMsg = String.format("Expected status %d but received %d for order ID %s", 
                                          expectedStatusCode, response.getStatusCode(), orderId);
            log.error(errorMsg);
            throw new RuntimeException(CosmosExceptionStates.INVALID_RESPONSE.getFormattedMessage(errorMsg));
        }
        
        return response;
    }
    
    /**
     * Gets order status by order ID
     * 
     * @param orderId Order ID to retrieve status for
     * @return Response containing order status
     */
    public Response getOrderStatus(String orderId) {
        log.info("Getting order status for order ID: {}", orderId);
        
        if (orderId == null || orderId.trim().isEmpty()) {
            throw new RuntimeException(CosmosExceptionStates.INVALID_ORDER_ID.getFormattedMessage("Order ID cannot be null or empty"));
        }
        
        try {
            String url = CosmosEndpoints.GET_ORDER_STATUS.getUrl(orderId);
            log.info("Making GET request to: {}", url);
            
            Response response = RestUtils.get(url, headers, null);
            
            log.info("Received order status response with status code: {}", response.getStatusCode());
            return response;
            
        } catch (Exception e) {
            log.error("Failed to get order status for order ID {}: {}", orderId, e.getMessage());
            throw new RuntimeException(CosmosExceptionStates.API_CALL_FAILED.getFormattedMessage("Failed to get order status: " + e.getMessage()), e);
        }
    }
    
    /**
     * Gets order history by order ID
     * 
     * @param orderId Order ID to retrieve history for
     * @return Response containing order history
     */
    public Response getOrderHistory(String orderId) {
        log.info("Getting order history for order ID: {}", orderId);
        
        if (orderId == null || orderId.trim().isEmpty()) {
            throw new RuntimeException(CosmosExceptionStates.INVALID_ORDER_ID.getFormattedMessage("Order ID cannot be null or empty"));
        }
        
        try {
            String url = CosmosEndpoints.GET_ORDER_HISTORY.getUrl(orderId);
            log.info("Making GET request to: {}", url);
            
            Response response = RestUtils.get(url, headers, null);
            
            log.info("Received order history response with status code: {}", response.getStatusCode());
            return response;
            
        } catch (Exception e) {
            log.error("Failed to get order history for order ID {}: {}", orderId, e.getMessage());
            throw new RuntimeException(CosmosExceptionStates.API_CALL_FAILED.getFormattedMessage("Failed to get order history: " + e.getMessage()), e);
        }
    }
    
    /**
     * Performs health check on the stateview service
     * 
     * @return Response containing health check result
     */
    public Response healthCheck() {
        log.info("Performing health check on stateview service");
        
        try {
            String url = CosmosEndpoints.HEALTH_CHECK.getUrl();
            log.info("Making GET request to: {}", url);
            
            // Health check typically doesn't require authentication
            Response response = RestUtils.get(url, null, null);
            
            log.info("Health check response status code: {}", response.getStatusCode());
            return response;
            
        } catch (Exception e) {
            log.error("Health check failed: {}", e.getMessage());
            throw new RuntimeException(CosmosExceptionStates.SERVICE_UNAVAILABLE.getFormattedMessage("Health check failed: " + e.getMessage()), e);
        }
    }
    
    /**
     * Validates if an order exists and is accessible
     * 
     * @param orderId Order ID to validate
     * @return true if order exists and is accessible, false otherwise
     */
    public boolean validateOrderExists(String orderId) {
        log.info("Validating if order exists: {}", orderId);
        
        try {
            Response response = getOrderById(orderId);
            boolean exists = response.getStatusCode() == 200;
            
            log.info("Order {} exists: {}", orderId, exists);
            return exists;
            
        } catch (Exception e) {
            log.warn("Order validation failed for {}: {}", orderId, e.getMessage());
            return false;
        }
    }
    
    /**
     * Extracts order status from the order details response
     * 
     * @param orderId Order ID
     * @return Order status as string
     */
    public String extractOrderStatus(String orderId) {
        log.info("Extracting order status for order ID: {}", orderId);
        
        try {
            Response response = getOrderById(orderId, 200);
            
            // Try to extract status from JSON response
            String status = response.jsonPath().getString("status");
            if (status == null) {
                status = response.jsonPath().getString("orderStatus");
            }
            if (status == null) {
                status = response.jsonPath().getString("state");
            }
            
            log.info("Extracted order status for {}: {}", orderId, status);
            return status;
            
        } catch (Exception e) {
            log.error("Failed to extract order status for {}: {}", orderId, e.getMessage());
            throw new RuntimeException(CosmosExceptionStates.INVALID_RESPONSE.getFormattedMessage("Failed to extract order status: " + e.getMessage()), e);
        }
    }
    
    /**
     * Creates default headers with authentication token from configuration
     * 
     * @return Map of default headers
     */
    private Map<String, String> createDefaultHeaders() {
        String defaultToken = envConfig.getAuth().getDefaultToken();
        if (defaultToken == null) {
            throw new RuntimeException(CosmosExceptionStates.AUTH_TOKEN_MISSING.getFormattedMessage("Default token not configured"));
        }
        return createHeaders(defaultToken);
    }
    
    /**
     * Creates headers with the specified authentication token
     * 
     * @param authToken Authentication token
     * @return Map of headers
     */
    private Map<String, String> createHeaders(String authToken) {
        Map<String, String> headers = new HashMap<>();
        headers.put("X-Auth-Token", authToken);
        headers.put("Content-Type", "application/json");
        headers.put("Accept", "application/json");
        
        log.debug("Created headers with auth token: {}", authToken.substring(0, Math.min(authToken.length(), 10)) + "...");
        return headers;
    }
    
    /**
     * Gets the current headers being used
     * 
     * @return Current headers map
     */
    public Map<String, String> getHeaders() {
        return new HashMap<>(headers);
    }
    
    /**
     * Updates the authentication token
     * 
     * @param newAuthToken New authentication token
     */
    public void updateAuthToken(String newAuthToken) {
        if (newAuthToken == null || newAuthToken.trim().isEmpty()) {
            throw new RuntimeException(CosmosExceptionStates.AUTH_TOKEN_INVALID.getFormattedMessage("Auth token cannot be null or empty"));
        }
        
        headers.put("X-Auth-Token", newAuthToken);
        log.info("Updated authentication token");
    }
}

package com.lenskart.cosmos.config;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

/**
 * Configuration class for the Cosmos module
 */
@Data
@NoArgsConstructor
public class CosmosConfig {
    
    // Environment configurations
    private Map<String, EnvironmentConfig> environments = new HashMap<>();
    
    /**
     * Gets the configuration for a specific environment
     * 
     * @param environment Environment name (e.g., "preprod", "prod")
     * @return Environment configuration
     */
    public EnvironmentConfig getEnvironment(String environment) {
        return environments.get(environment);
    }
    
    /**
     * Configuration for a specific environment
     */
    @Data
    @NoArgsConstructor
    public static class EnvironmentConfig {
        // Base URLs for different services
        private Map<String, String> baseUrls = new HashMap<>();
        
        // Authentication configuration
        private AuthConfig auth = new AuthConfig();
        
        // Service specific configurations
        private Map<String, ServiceConfig> services = new HashMap<>();
        
        /**
         * Gets the base URL for a specific service
         * 
         * @param serviceName Service name
         * @return Base URL for the service
         */
        public String getBaseUrl(String serviceName) {
            return baseUrls.get(serviceName);
        }
        
        /**
         * Gets the service configuration for a specific service
         * 
         * @param serviceName Service name
         * @return Service configuration
         */
        public ServiceConfig getService(String serviceName) {
            return services.get(serviceName);
        }
    }
    
    /**
     * Authentication configuration
     */
    @Data
    @NoArgsConstructor
    public static class AuthConfig {
        private String defaultToken;
        
        /**
         * Gets the default authentication token
         * 
         * @return Default token
         */
        public String getDefaultToken() {
            return defaultToken;
        }
    }
    
    /**
     * Service specific configuration
     */
    @Data
    @NoArgsConstructor
    public static class ServiceConfig {
        private String basePath;
        private String version;
        private int timeout = 30000;
        private int retryCount = 3;
        
        /**
         * Gets the full service path
         * 
         * @return Full service path including base path and version
         */
        public String getFullPath() {
            if (basePath != null && version != null) {
                return basePath + "/" + version;
            } else if (basePath != null) {
                return basePath;
            } else if (version != null) {
                return "/" + version;
            }
            return "";
        }
    }
}

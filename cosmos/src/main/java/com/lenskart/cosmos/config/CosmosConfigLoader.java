package com.lenskart.cosmos.config;

import com.lenskart.commons.loader.ConfigLoader;
import lombok.extern.slf4j.Slf4j;
import org.yaml.snakeyaml.Yaml;

import java.io.InputStream;
import java.util.Map;

/**
 * Configuration loader for the Cosmos module
 */
@Slf4j
public class CosmosConfigLoader {
    
    private static final String CONFIG_FILE = "cosmos.yml";
    private static CosmosConfig cosmosConfig;
    private static final Object lock = new Object();
    
    /**
     * Loads the cosmos configuration from cosmos.yml
     * 
     * @return CosmosConfig instance
     */
    public static CosmosConfig loadConfig() {
        if (cosmosConfig == null) {
            synchronized (lock) {
                if (cosmosConfig == null) {
                    cosmosConfig = loadConfigFromFile();
                }
            }
        }
        return cosmosConfig;
    }
    
    /**
     * Loads configuration from the cosmos.yml file
     * 
     * @return CosmosConfig instance
     */
    @SuppressWarnings("unchecked")
    private static CosmosConfig loadConfigFromFile() {
        log.info("Loading cosmos configuration from {}", CONFIG_FILE);
        
        try (InputStream inputStream = CosmosConfigLoader.class.getClassLoader().getResourceAsStream(CONFIG_FILE)) {
            if (inputStream == null) {
                throw new RuntimeException("Configuration file not found: " + CONFIG_FILE);
            }
            
            Yaml yaml = new Yaml();
            Map<String, Object> configData = yaml.load(inputStream);
            
            CosmosConfig config = new CosmosConfig();
            
            // Parse each environment configuration
            for (Map.Entry<String, Object> envEntry : configData.entrySet()) {
                String envName = envEntry.getKey();
                Map<String, Object> envData = (Map<String, Object>) envEntry.getValue();
                
                CosmosConfig.EnvironmentConfig envConfig = parseEnvironmentConfig(envData);
                config.getEnvironments().put(envName, envConfig);
                
                log.debug("Loaded configuration for environment: {}", envName);
            }
            
            log.info("Successfully loaded cosmos configuration with {} environments", config.getEnvironments().size());
            return config;
            
        } catch (Exception e) {
            log.error("Failed to load cosmos configuration: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to load cosmos configuration", e);
        }
    }
    
    /**
     * Parses environment configuration from YAML data
     * 
     * @param envData Environment data from YAML
     * @return EnvironmentConfig instance
     */
    @SuppressWarnings("unchecked")
    private static CosmosConfig.EnvironmentConfig parseEnvironmentConfig(Map<String, Object> envData) {
        CosmosConfig.EnvironmentConfig envConfig = new CosmosConfig.EnvironmentConfig();
        
        // Parse base URLs
        if (envData.containsKey("baseUrls")) {
            Map<String, String> baseUrls = (Map<String, String>) envData.get("baseUrls");
            envConfig.getBaseUrls().putAll(baseUrls);
        }
        
        // Parse authentication configuration
        if (envData.containsKey("auth")) {
            Map<String, Object> authData = (Map<String, Object>) envData.get("auth");
            CosmosConfig.AuthConfig authConfig = new CosmosConfig.AuthConfig();
            
            if (authData.containsKey("defaultToken")) {
                authConfig.setDefaultToken((String) authData.get("defaultToken"));
            }
            
            envConfig.setAuth(authConfig);
        }
        
        // Parse services configuration
        if (envData.containsKey("services")) {
            Map<String, Object> servicesData = (Map<String, Object>) envData.get("services");
            
            for (Map.Entry<String, Object> serviceEntry : servicesData.entrySet()) {
                String serviceName = serviceEntry.getKey();
                Map<String, Object> serviceData = (Map<String, Object>) serviceEntry.getValue();
                
                CosmosConfig.ServiceConfig serviceConfig = parseServiceConfig(serviceData);
                envConfig.getServices().put(serviceName, serviceConfig);
            }
        }
        
        return envConfig;
    }
    
    /**
     * Parses service configuration from YAML data
     * 
     * @param serviceData Service data from YAML
     * @return ServiceConfig instance
     */
    private static CosmosConfig.ServiceConfig parseServiceConfig(Map<String, Object> serviceData) {
        CosmosConfig.ServiceConfig serviceConfig = new CosmosConfig.ServiceConfig();
        
        if (serviceData.containsKey("basePath")) {
            serviceConfig.setBasePath((String) serviceData.get("basePath"));
        }
        
        if (serviceData.containsKey("version")) {
            serviceConfig.setVersion((String) serviceData.get("version"));
        }
        
        if (serviceData.containsKey("timeout")) {
            serviceConfig.setTimeout((Integer) serviceData.get("timeout"));
        }
        
        if (serviceData.containsKey("retryCount")) {
            serviceConfig.setRetryCount((Integer) serviceData.get("retryCount"));
        }
        
        return serviceConfig;
    }
    
    /**
     * Gets the current environment name from system property or defaults to "preprod"
     *
     * @return Current environment name
     */
    public static String getCurrentEnvironment() {
        return ConfigLoader.getInstance().getDefaultEnvironment();
    }
    
    /**
     * Gets the configuration for the current environment
     * 
     * @return EnvironmentConfig for current environment
     */
    public static CosmosConfig.EnvironmentConfig getCurrentEnvironmentConfig() {
        CosmosConfig config = loadConfig();
        String currentEnv = getCurrentEnvironment();
        
        CosmosConfig.EnvironmentConfig envConfig = config.getEnvironment(currentEnv);
        if (envConfig == null) {
            throw new RuntimeException("Configuration not found for environment: " + currentEnv);
        }
        
        return envConfig;
    }
    
    /**
     * Reloads the configuration (useful for testing)
     */
    public static void reloadConfig() {
        synchronized (lock) {
            cosmosConfig = null;
        }
        log.info("Cosmos configuration reloaded");
    }
}

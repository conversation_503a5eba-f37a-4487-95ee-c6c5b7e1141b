package com.lenskart.cosmos.util;

import com.lenskart.cosmos.config.CosmosConfig;
import com.lenskart.cosmos.config.CosmosConfigLoader;
import com.lenskart.cosmos.exceptions.CosmosExceptionStates;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * Utility class for common Cosmos module operations
 */
@Slf4j
public class CosmosUtils {
    
    // Order ID validation pattern (assuming numeric order IDs)
    private static final Pattern ORDER_ID_PATTERN = Pattern.compile("^\\d{1,15}$");
    
    // Common HTTP status codes
    public static final int HTTP_OK = 200;
    public static final int HTTP_CREATED = 201;
    public static final int HTTP_BAD_REQUEST = 400;
    public static final int HTTP_UNAUTHORIZED = 401;
    public static final int HTTP_FORBIDDEN = 403;
    public static final int HTTP_NOT_FOUND = 404;
    public static final int HTTP_INTERNAL_SERVER_ERROR = 500;
    
    /**
     * Private constructor to prevent instantiation
     */
    private CosmosUtils() {
        throw new UnsupportedOperationException("Utility class cannot be instantiated");
    }
    
    /**
     * Validates if an order ID is in the correct format
     * 
     * @param orderId Order ID to validate
     * @return true if valid, false otherwise
     */
    public static boolean isValidOrderId(String orderId) {
        if (orderId == null || orderId.trim().isEmpty()) {
            return false;
        }
        
        return ORDER_ID_PATTERN.matcher(orderId.trim()).matches();
    }
    
    /**
     * Validates and sanitizes an order ID
     * 
     * @param orderId Order ID to validate
     * @return Sanitized order ID
     * @throws RuntimeException if order ID is invalid
     */
    public static String validateAndSanitizeOrderId(String orderId) {
        if (orderId == null || orderId.trim().isEmpty()) {
            throw new RuntimeException(CosmosExceptionStates.INVALID_ORDER_ID.getFormattedMessage("Order ID cannot be null or empty"));
        }
        
        String sanitized = orderId.trim();
        
        if (!isValidOrderId(sanitized)) {
            throw new RuntimeException(CosmosExceptionStates.INVALID_ORDER_ID.getFormattedMessage("Order ID format is invalid: " + sanitized));
        }
        
        return sanitized;
    }
    
    /**
     * Creates standard authentication headers for Cosmos services
     * 
     * @param authToken Authentication token
     * @return Map of authentication headers
     */
    public static Map<String, String> createAuthHeaders(String authToken) {
        if (authToken == null || authToken.trim().isEmpty()) {
            throw new RuntimeException(CosmosExceptionStates.AUTH_TOKEN_MISSING.getFormattedMessage("Authentication token is required"));
        }
        
        Map<String, String> headers = new HashMap<>();
        headers.put("X-Auth-Token", authToken);
        headers.put("Content-Type", "application/json");
        headers.put("Accept", "application/json");
        
        return headers;
    }
    
    /**
     * Creates standard authentication headers using default token from configuration
     * 
     * @return Map of authentication headers
     */
    public static Map<String, String> createDefaultAuthHeaders() {
        CosmosConfig.EnvironmentConfig envConfig = CosmosConfigLoader.getCurrentEnvironmentConfig();
        String defaultToken = envConfig.getAuth().getDefaultToken();
        
        if (defaultToken == null) {
            throw new RuntimeException(CosmosExceptionStates.AUTH_TOKEN_MISSING.getFormattedMessage("Default authentication token not configured"));
        }
        
        return createAuthHeaders(defaultToken);
    }
    
    /**
     * Checks if an HTTP status code indicates success
     * 
     * @param statusCode HTTP status code
     * @return true if status indicates success (2xx), false otherwise
     */
    public static boolean isSuccessStatus(int statusCode) {
        return statusCode >= 200 && statusCode < 300;
    }
    
    /**
     * Checks if an HTTP status code indicates client error
     * 
     * @param statusCode HTTP status code
     * @return true if status indicates client error (4xx), false otherwise
     */
    public static boolean isClientError(int statusCode) {
        return statusCode >= 400 && statusCode < 500;
    }
    
    /**
     * Checks if an HTTP status code indicates server error
     * 
     * @param statusCode HTTP status code
     * @return true if status indicates server error (5xx), false otherwise
     */
    public static boolean isServerError(int statusCode) {
        return statusCode >= 500 && statusCode < 600;
    }
    
    /**
     * Gets a human-readable description for common HTTP status codes
     * 
     * @param statusCode HTTP status code
     * @return Description of the status code
     */
    public static String getStatusDescription(int statusCode) {
        switch (statusCode) {
            case HTTP_OK:
                return "OK - Request successful";
            case HTTP_CREATED:
                return "Created - Resource created successfully";
            case HTTP_BAD_REQUEST:
                return "Bad Request - Invalid request parameters";
            case HTTP_UNAUTHORIZED:
                return "Unauthorized - Authentication required";
            case HTTP_FORBIDDEN:
                return "Forbidden - Access denied";
            case HTTP_NOT_FOUND:
                return "Not Found - Resource not found";
            case HTTP_INTERNAL_SERVER_ERROR:
                return "Internal Server Error - Server error occurred";
            default:
                if (isSuccessStatus(statusCode)) {
                    return "Success - Request completed successfully";
                } else if (isClientError(statusCode)) {
                    return "Client Error - Request error";
                } else if (isServerError(statusCode)) {
                    return "Server Error - Server error occurred";
                } else {
                    return "Unknown Status - Unexpected status code";
                }
        }
    }
    
    /**
     * Formats an error message with context information
     * 
     * @param operation Operation being performed
     * @param orderId Order ID (if applicable)
     * @param statusCode HTTP status code
     * @param errorMessage Error message
     * @return Formatted error message
     */
    public static String formatErrorMessage(String operation, String orderId, int statusCode, String errorMessage) {
        StringBuilder message = new StringBuilder();
        message.append(String.format("Operation '%s' failed", operation));
        
        if (orderId != null && !orderId.trim().isEmpty()) {
            message.append(String.format(" for order ID '%s'", orderId));
        }
        
        message.append(String.format(" with status %d (%s)", statusCode, getStatusDescription(statusCode)));
        
        if (errorMessage != null && !errorMessage.trim().isEmpty()) {
            message.append(String.format(": %s", errorMessage));
        }
        
        return message.toString();
    }
    
    /**
     * Extracts error message from response body
     * 
     * @param responseBody Response body as string
     * @return Extracted error message or default message
     */
    public static String extractErrorMessage(String responseBody) {
        if (responseBody == null || responseBody.trim().isEmpty()) {
            return "No error details available";
        }
        
        try {
            // Try to extract common error message fields from JSON
            if (responseBody.contains("\"message\"")) {
                // Simple extraction for message field
                int start = responseBody.indexOf("\"message\"") + 10;
                int end = responseBody.indexOf("\"", start + 1);
                if (end > start) {
                    return responseBody.substring(start + 1, end);
                }
            }
            
            if (responseBody.contains("\"error\"")) {
                // Simple extraction for error field
                int start = responseBody.indexOf("\"error\"") + 8;
                int end = responseBody.indexOf("\"", start + 1);
                if (end > start) {
                    return responseBody.substring(start + 1, end);
                }
            }
            
            // If no specific error field found, return truncated response
            if (responseBody.length() > 200) {
                return responseBody.substring(0, 200) + "...";
            }
            
            return responseBody;
            
        } catch (Exception e) {
            log.warn("Failed to extract error message from response: {}", e.getMessage());
            return "Error parsing response";
        }
    }
    
    /**
     * Generates a unique request ID for tracking
     * 
     * @return Unique request ID
     */
    public static String generateRequestId() {
        return "COSMOS_" + System.currentTimeMillis() + "_" + Thread.currentThread().getId();
    }
    
    /**
     * Logs request details for debugging
     * 
     * @param operation Operation being performed
     * @param url Request URL
     * @param headers Request headers (sensitive data will be masked)
     */
    public static void logRequestDetails(String operation, String url, Map<String, String> headers) {
        log.info("=== Cosmos Request Details ===");
        log.info("Operation: {}", operation);
        log.info("URL: {}", url);
        
        if (headers != null && !headers.isEmpty()) {
            Map<String, String> maskedHeaders = new HashMap<>(headers);
            // Mask sensitive headers
            if (maskedHeaders.containsKey("X-Auth-Token")) {
                String token = maskedHeaders.get("X-Auth-Token");
                maskedHeaders.put("X-Auth-Token", maskSensitiveData(token));
            }
            log.info("Headers: {}", maskedHeaders);
        }
        
        log.info("Request ID: {}", generateRequestId());
    }
    
    /**
     * Masks sensitive data for logging
     * 
     * @param data Sensitive data to mask
     * @return Masked data
     */
    public static String maskSensitiveData(String data) {
        if (data == null || data.length() <= 4) {
            return "****";
        }
        
        int visibleChars = Math.min(4, data.length() / 4);
        String visible = data.substring(0, visibleChars);
        String masked = "*".repeat(data.length() - visibleChars);
        
        return visible + masked;
    }
    
    /**
     * Validates that required configuration is present
     * 
     * @throws RuntimeException if required configuration is missing
     */
    public static void validateConfiguration() {
        try {
            CosmosConfig.EnvironmentConfig envConfig = CosmosConfigLoader.getCurrentEnvironmentConfig();
            
            // Check if stateview service base URL is configured
            String stateviewUrl = envConfig.getBaseUrl("stateviewService");
            if (stateviewUrl == null || stateviewUrl.trim().isEmpty()) {
                throw new RuntimeException(CosmosExceptionStates.CONFIG_NOT_FOUND.getFormattedMessage("Stateview service base URL not configured"));
            }
            
            // Check if default auth token is configured
            String defaultToken = envConfig.getAuth().getDefaultToken();
            if (defaultToken == null || defaultToken.trim().isEmpty()) {
                throw new RuntimeException(CosmosExceptionStates.AUTH_TOKEN_MISSING.getFormattedMessage("Default authentication token not configured"));
            }
            
            log.info("Cosmos configuration validation passed");
            
        } catch (Exception e) {
            log.error("Cosmos configuration validation failed: {}", e.getMessage());
            throw new RuntimeException(CosmosExceptionStates.CONFIG_LOAD_FAILED.getFormattedMessage("Configuration validation failed: " + e.getMessage()), e);
        }
    }
}

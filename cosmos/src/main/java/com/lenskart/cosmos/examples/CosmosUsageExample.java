package com.lenskart.cosmos.examples;

import com.lenskart.cosmos.config.CosmosConfig;
import com.lenskart.cosmos.config.CosmosConfigLoader;
import com.lenskart.cosmos.endpoints.CosmosEndpoints;
import com.lenskart.cosmos.helpers.StateviewHelper;
import com.lenskart.cosmos.util.CosmosUtils;
import io.restassured.response.Response;
import lombok.extern.slf4j.Slf4j;

/**
 * Example demonstrating how to use the Cosmos module
 * Based on the provided curl command:
 * curl --location 'https://stateview.cosmos.preprod.lenskart.com/cosmos-stateview/v1/order/**********' \
 * --header 'X-Auth-Token: Bearer 123'
 */
@Slf4j
public class CosmosUsageExample {

    /**
     * Example 1: Basic order retrieval using StateviewHelper
     */
    public static void basicOrderRetrievalExample() {
        log.info("=== Example 1: Basic Order Retrieval ===");
        
        try {
            // Initialize the helper (uses default auth token from config)
            StateviewHelper stateviewHelper = new StateviewHelper();
            
            // Order ID from the provided curl command
            String orderId = "**********";
            
            // Get order details - this replicates the exact curl command functionality
            Response response = stateviewHelper.getOrderById(orderId);
            
            log.info("Order retrieval successful!");
            log.info("Status Code: {}", response.getStatusCode());
            log.info("Response Body: {}", response.getBody().asString());
            
            // Extract specific information from the response
            if (response.getStatusCode() == 200) {
                // Try to extract order status
                try {
                    String orderStatus = response.jsonPath().getString("status");
                    log.info("Order Status: {}", orderStatus);
                } catch (Exception e) {
                    log.info("Could not extract order status from response");
                }
            }
            
        } catch (Exception e) {
            log.error("Basic order retrieval failed: {}", e.getMessage());
        }
        
        log.info("✅ Basic order retrieval example completed");
    }

    /**
     * Example 2: Order retrieval with custom authentication token
     */
    public static void customAuthTokenExample() {
        log.info("=== Example 2: Custom Auth Token ===");
        
        try {
            // Use custom authentication token
            String customToken = "Bearer your_custom_token_here";
            StateviewHelper stateviewHelper = new StateviewHelper(customToken);
            
            String orderId = "**********";
            
            // Get order with custom auth
            Response response = stateviewHelper.getOrderById(orderId);
            
            log.info("Custom auth order retrieval completed");
            log.info("Status Code: {}", response.getStatusCode());
            
        } catch (Exception e) {
            log.error("Custom auth example failed: {}", e.getMessage());
        }
        
        log.info("✅ Custom auth token example completed");
    }

    /**
     * Example 3: Order retrieval with status validation
     */
    public static void orderRetrievalWithValidationExample() {
        log.info("=== Example 3: Order Retrieval with Validation ===");
        
        try {
            StateviewHelper stateviewHelper = new StateviewHelper();
            String orderId = "**********";
            
            // Get order with expected status code validation
            Response response = stateviewHelper.getOrderById(orderId, 200);
            
            log.info("Order retrieval with validation successful!");
            log.info("Response: {}", response.getBody().asString());
            
        } catch (Exception e) {
            log.error("Order retrieval with validation failed: {}", e.getMessage());
            // This will throw an exception if status code is not 200
        }
        
        log.info("✅ Order retrieval with validation example completed");
    }

    /**
     * Example 4: Multiple order operations
     */
    public static void multipleOrderOperationsExample() {
        log.info("=== Example 4: Multiple Order Operations ===");
        
        try {
            StateviewHelper stateviewHelper = new StateviewHelper();
            String orderId = "**********";
            
            // 1. Get order details
            log.info("Getting order details...");
            Response orderResponse = stateviewHelper.getOrderById(orderId);
            log.info("Order details status: {}", orderResponse.getStatusCode());
            
            // 2. Get order status
            log.info("Getting order status...");
            Response statusResponse = stateviewHelper.getOrderStatus(orderId);
            log.info("Order status response: {}", statusResponse.getStatusCode());
            
            // 3. Get order history
            log.info("Getting order history...");
            Response historyResponse = stateviewHelper.getOrderHistory(orderId);
            log.info("Order history status: {}", historyResponse.getStatusCode());
            
            // 4. Validate order exists
            log.info("Validating order exists...");
            boolean orderExists = stateviewHelper.validateOrderExists(orderId);
            log.info("Order exists: {}", orderExists);
            
            // 5. Extract order status
            if (orderExists) {
                try {
                    String status = stateviewHelper.extractOrderStatus(orderId);
                    log.info("Extracted order status: {}", status);
                } catch (Exception e) {
                    log.warn("Could not extract order status: {}", e.getMessage());
                }
            }
            
        } catch (Exception e) {
            log.error("Multiple operations example failed: {}", e.getMessage());
        }
        
        log.info("✅ Multiple order operations example completed");
    }

    /**
     * Example 5: Configuration and endpoint usage
     */
    public static void configurationAndEndpointExample() {
        log.info("=== Example 5: Configuration and Endpoint Usage ===");
        
        try {
            // Load and display configuration
            CosmosConfig.EnvironmentConfig envConfig = CosmosConfigLoader.getCurrentEnvironmentConfig();
            
            log.info("Current environment configuration:");
            log.info("Stateview service URL: {}", envConfig.getBaseUrl("stateviewService"));
            log.info("Default auth token: {}", CosmosUtils.maskSensitiveData(envConfig.getAuth().getDefaultToken()));
            
            // Display endpoint information
            log.info("Available endpoints:");
            for (CosmosEndpoints endpoint : CosmosEndpoints.values()) {
                log.info("  {} [{}] - {}", endpoint.name(), endpoint.getMethod(), endpoint.getDescription());
                log.info("    URL: {}", endpoint.getUrl("SAMPLE_ORDER_ID"));
                log.info("    Requires Auth: {}", endpoint.requiresAuth());
                log.info("    Timeout: {}ms", endpoint.getTimeout());
                log.info("    Retry Count: {}", endpoint.getRetryCount());
                log.info("");
            }
            
        } catch (Exception e) {
            log.error("Configuration example failed: {}", e.getMessage());
        }
        
        log.info("✅ Configuration and endpoint example completed");
    }

    /**
     * Example 6: Error handling and validation
     */
    public static void errorHandlingExample() {
        log.info("=== Example 6: Error Handling and Validation ===");
        
        StateviewHelper stateviewHelper = new StateviewHelper();
        
        // Test invalid order ID handling
        try {
            stateviewHelper.getOrderById(null);
        } catch (Exception e) {
            log.info("✅ Correctly handled null order ID: {}", e.getMessage());
        }
        
        try {
            stateviewHelper.getOrderById("");
        } catch (Exception e) {
            log.info("✅ Correctly handled empty order ID: {}", e.getMessage());
        }
        
        try {
            stateviewHelper.getOrderById("invalid_order_id");
        } catch (Exception e) {
            log.info("✅ Correctly handled invalid order ID format: {}", e.getMessage());
        }
        
        // Test order ID validation utilities
        log.info("Order ID validation tests:");
        log.info("  '**********' is valid: {}", CosmosUtils.isValidOrderId("**********"));
        log.info("  'abc123' is valid: {}", CosmosUtils.isValidOrderId("abc123"));
        log.info("  '' is valid: {}", CosmosUtils.isValidOrderId(""));
        log.info("  null is valid: {}", CosmosUtils.isValidOrderId(null));
        
        // Test HTTP status utilities
        log.info("HTTP status utilities:");
        log.info("  200 is success: {}", CosmosUtils.isSuccessStatus(200));
        log.info("  404 is client error: {}", CosmosUtils.isClientError(404));
        log.info("  500 is server error: {}", CosmosUtils.isServerError(500));
        log.info("  404 description: {}", CosmosUtils.getStatusDescription(404));
        
        log.info("✅ Error handling example completed");
    }

    /**
     * Example 7: Health check and service validation
     */
    public static void healthCheckExample() {
        log.info("=== Example 7: Health Check ===");
        
        try {
            StateviewHelper stateviewHelper = new StateviewHelper();
            
            // Perform health check
            Response healthResponse = stateviewHelper.healthCheck();
            
            log.info("Health check completed");
            log.info("Status Code: {}", healthResponse.getStatusCode());
            log.info("Response: {}", healthResponse.getBody().asString());
            
            if (CosmosUtils.isSuccessStatus(healthResponse.getStatusCode())) {
                log.info("✅ Service is healthy");
            } else {
                log.warn("⚠️ Service health check failed");
            }
            
        } catch (Exception e) {
            log.error("Health check failed: {}", e.getMessage());
        }
        
        log.info("✅ Health check example completed");
    }

    /**
     * Example 8: Dynamic token management
     */
    public static void dynamicTokenManagementExample() {
        log.info("=== Example 8: Dynamic Token Management ===");
        
        try {
            // Start with default token
            StateviewHelper stateviewHelper = new StateviewHelper();
            log.info("Initial headers: {}", stateviewHelper.getHeaders());
            
            // Update to a new token
            String newToken = "Bearer updated_token_123";
            stateviewHelper.updateAuthToken(newToken);
            log.info("Updated headers: {}", stateviewHelper.getHeaders());
            
            // Try to use the service with updated token
            String orderId = "**********";
            Response response = stateviewHelper.getOrderById(orderId);
            log.info("Request with updated token - Status: {}", response.getStatusCode());
            
        } catch (Exception e) {
            log.error("Dynamic token management failed: {}", e.getMessage());
        }
        
        log.info("✅ Dynamic token management example completed");
    }

    /**
     * Example 9: Comprehensive order processing workflow
     */
    public static void comprehensiveWorkflowExample() {
        log.info("=== Example 9: Comprehensive Order Processing Workflow ===");
        
        try {
            StateviewHelper stateviewHelper = new StateviewHelper();
            String orderId = "**********";
            
            log.info("Starting comprehensive workflow for order: {}", orderId);
            
            // Step 1: Validate configuration
            CosmosUtils.validateConfiguration();
            log.info("✅ Configuration validated");
            
            // Step 2: Validate order ID format
            String sanitizedOrderId = CosmosUtils.validateAndSanitizeOrderId(orderId);
            log.info("✅ Order ID validated and sanitized: {}", sanitizedOrderId);
            
            // Step 3: Check if order exists
            boolean orderExists = stateviewHelper.validateOrderExists(sanitizedOrderId);
            log.info("✅ Order existence check: {}", orderExists);
            
            if (orderExists) {
                // Step 4: Get full order details
                Response orderDetails = stateviewHelper.getOrderById(sanitizedOrderId, 200);
                log.info("✅ Order details retrieved successfully");
                
                // Step 5: Extract and log order status
                String orderStatus = stateviewHelper.extractOrderStatus(sanitizedOrderId);
                log.info("✅ Order status extracted: {}", orderStatus);
                
                // Step 6: Get order history
                Response orderHistory = stateviewHelper.getOrderHistory(sanitizedOrderId);
                log.info("✅ Order history retrieved - Status: {}", orderHistory.getStatusCode());
                
                log.info("🎉 Comprehensive workflow completed successfully!");
                
            } else {
                log.warn("⚠️ Order does not exist or is not accessible");
            }
            
        } catch (Exception e) {
            log.error("Comprehensive workflow failed: {}", e.getMessage());
        }
        
        log.info("✅ Comprehensive workflow example completed");
    }

    /**
     * Main method to run all examples
     */
    public static void main(String[] args) {
        log.info("🚀 Running Cosmos Module Usage Examples");
        log.info("Based on curl: curl --location 'https://stateview.cosmos.preprod.lenskart.com/cosmos-stateview/v1/order/**********' --header 'X-Auth-Token: Bearer 123'");
        log.info("");
        
        basicOrderRetrievalExample();
        customAuthTokenExample();
        orderRetrievalWithValidationExample();
        multipleOrderOperationsExample();
        configurationAndEndpointExample();
        errorHandlingExample();
        healthCheckExample();
        dynamicTokenManagementExample();
        comprehensiveWorkflowExample();
        
        log.info("✅ All Cosmos Module Usage Examples completed!");
    }
}

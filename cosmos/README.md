# Cosmos Module

## Overview

The **Cosmos Module** provides automation testing capabilities for Lenskart's Cosmos service, which handles order state management, order tracking, and provides comprehensive order visibility across the entire order lifecycle.

## 🏗️ **Architecture**

### **Core Components**

- **Order State Management**: Track and manage order states across the lifecycle
- **Order Details Retrieval**: Get comprehensive order information
- **State Transition Tracking**: Monitor order state changes and transitions
- **Order Visibility**: Provide real-time order status and tracking information

## 📁 **Package Structure**

```
com.lenskart.cosmos/
├── config/              # Cosmos-specific configuration management
├── endpoints/           # Cosmos API endpoints and endpoint manager
├── exceptions/          # Cosmos-specific exception states
└── helpers/             # Service helper classes for Cosmos operations
```

## 🔧 **Key Features**

### **1. Order State Management**

#### **Get Order Details by ID**
```java
StateviewHelper stateviewHelper = new StateviewHelper();

// Get order details by order ID
Response response = stateviewHelper.getOrderById("1930753219");

// Parse order details
OrderDetails orderDetails = stateviewHelper.parseOrderDetails(response);

// Get current order state
String currentState = orderDetails.getCurrentState();
String mappedState = orderDetails.getMappedState();
```

#### **Order State Information**
```java
// Get comprehensive order state information
OrderStateInfo stateInfo = stateviewHelper.getOrderStateInfo("1930753219");

System.out.println("Order ID: " + stateInfo.getOrderId());
System.out.println("Current State: " + stateInfo.getCurrentState());
System.out.println("Mapped State: " + stateInfo.getMappedState());
System.out.println("Mapped Status: " + stateInfo.getMappedStatus());
System.out.println("Tracking Status: " + stateInfo.getMappedTrackingStatus());
System.out.println("Country: " + stateInfo.getOrderCountry());
```

### **2. Order Validation and Verification**

#### **Order Existence Validation**
```java
StateviewHelper helper = new StateviewHelper();

// Validate if order exists
boolean orderExists = helper.validateOrderExists("1930753219");

// Validate order ID format
boolean isValidFormat = helper.isValidOrderId("1930753219");

// Validate order state
boolean isValidState = helper.validateOrderState("1930753219", "PROCESSING");
```

### **3. Authentication and Security**

#### **Authentication Management**
```java
AuthenticationHelper authHelper = new AuthenticationHelper();

// Create authentication header
String authHeader = authHelper.createAuthHeader();

// Validate authentication
boolean isAuthenticated = authHelper.validateAuthentication();

// Get authentication token
String token = authHelper.getAuthToken();
```

## 🌐 **API Endpoints**

### **Cosmos Endpoints**
```java
public enum CosmosEndpoints implements BaseEndpoint {
    // Order State Management
    GET_ORDER_BY_ID("/cosmos-stateview/v1/order/{orderId}", "stateviewService", "GET", 
                   "Get order details by order ID"),
    
    GET_ORDER_STATE("/cosmos-stateview/v1/order/{orderId}/state", "stateviewService", "GET", 
                   "Get order state information"),
    
    GET_ORDER_TIMELINE("/cosmos-stateview/v1/order/{orderId}/timeline", "stateviewService", "GET", 
                      "Get order state transition timeline"),
    
    VALIDATE_ORDER("/cosmos-stateview/v1/order/{orderId}/validate", "stateviewService", "GET", 
                  "Validate order existence and state");
}
```

### **URL Generation**
```java
// Simple URL generation
String url = CosmosEndpoints.GET_ORDER_BY_ID.getUrl();

// URL with path parameters
String url = CosmosEndpoints.GET_ORDER_BY_ID.getUrl(
    Map.of("orderId", "1930753219")
);

// Using endpoint manager
String url = CosmosEndpointManager.getEndpointUrl(
    CosmosEndpoints.GET_ORDER_BY_ID,
    Map.of("orderId", "1930753219")
);
```

## 🔧 **Configuration**

### **cosmos.yml Configuration**
```yaml
cosmos:
  baseUrls:
    stateviewService: https://stateview.cosmos.preprod.lenskart.com
    
  authentication:
    authToken: "Bearer 123"
    tokenType: "Bearer"
    
  defaults:
    timeout: 30000
    retryAttempts: 3
    
  validation:
    validateOrderFormat: true
    validateStateTransitions: true
    
  stateview:
    supportedStates:
      - CREATED
      - PROCESSING
      - IN_PICKING
      - PICKED
      - IN_QC
      - QC_DONE
      - INVOICED
      - DISPATCHED
      - DELIVERED
      - CANCELLED
      - RETURNED
```

### **Configuration Usage**
```java
// Get Cosmos configuration
CosmosConfig config = CosmosConfigLoader.loadConfig();

// Get base URL
String baseUrl = config.getBaseUrl("stateviewService");

// Get authentication token
String authToken = config.getAuthToken();

// Get supported states
List<String> supportedStates = config.getSupportedStates();
```

## 🧪 **Testing**

### **Test Categories**

#### **Sanity Tests**
```java
@Test
@TestCategory(TestCategory.Category.SANITY)
public void testGetOrderById() {
    StateviewHelper helper = new StateviewHelper();
    Response response = helper.getOrderById("1930753219");
    
    assert response.getStatusCode() == 200;
    assert response.getBody().asString().contains("orderId");
}
```

#### **Regression Tests**
```java
@Test
@TestCategory(TestCategory.Category.REGRESSION)
public void testOrderStateValidation() {
    StateviewHelper helper = new StateviewHelper();
    
    // Test order existence
    boolean exists = helper.validateOrderExists("1930753219");
    assert exists : "Order should exist";
    
    // Test order state
    OrderStateInfo stateInfo = helper.getOrderStateInfo("1930753219");
    assert stateInfo.getCurrentState() != null : "Current state should not be null";
    assert stateInfo.getMappedState() != null : "Mapped state should not be null";
}
```

#### **E2E Tests**
```java
@Test
@TestCategory(TestCategory.Category.E2E)
public void testOrderLifecycleTracking() {
    StateviewHelper helper = new StateviewHelper();
    
    // Track order through multiple states
    String orderId = "1930753219";
    
    // Validate initial state
    OrderStateInfo initialState = helper.getOrderStateInfo(orderId);
    
    // Monitor state transitions
    List<String> expectedStates = Arrays.asList(
        "CREATED", "PROCESSING", "IN_PICKING", "PICKED"
    );
    
    for (String expectedState : expectedStates) {
        // Wait for state transition
        AwaitUtils.waitUntil(() -> {
            OrderStateInfo currentState = helper.getOrderStateInfo(orderId);
            return expectedState.equals(currentState.getCurrentState());
        }, Duration.ofMinutes(5));
    }
}
```

### **Running Cosmos Tests**
```bash
# Run all Cosmos tests
mvn test -pl cosmos

# Run specific test categories
mvn test -pl cosmos -DtestCategory=SANITY

# Run with specific environment
mvn test -pl cosmos -Denvironment=preprod

# Run specific test class
mvn test -pl cosmos -Dtest=StateviewHelperTest
```

## 📊 **Helper Classes**

### **StateviewHelper**
```java
StateviewHelper helper = new StateviewHelper();

// Get order by ID
Response response = helper.getOrderById("1930753219");

// Parse order details
OrderDetails order = helper.parseOrderDetails(response);

// Get order state information
OrderStateInfo stateInfo = helper.getOrderStateInfo("1930753219");

// Validate order
boolean isValid = helper.validateOrderExists("1930753219");
```

### **CosmosOrderDetailsHelper**
```java
CosmosOrderDetailsHelper detailsHelper = new CosmosOrderDetailsHelper();

// Get comprehensive order details
OrderDetails details = detailsHelper.getOrderDetails("1930753219");

// Get order items
List<OrderItem> items = detailsHelper.getOrderItems("1930753219");

// Get order timeline
List<OrderEvent> timeline = detailsHelper.getOrderTimeline("1930753219");
```

### **GetOrderDetailsHelper**
```java
GetOrderDetailsHelper getHelper = new GetOrderDetailsHelper();

// Get order with specific fields
OrderDetails order = getHelper.getOrderWithFields("1930753219", 
    Arrays.asList("orderId", "currentState", "items"));

// Get order summary
OrderSummary summary = getHelper.getOrderSummary("1930753219");
```

## 🔍 **Exception Handling**

### **Cosmos Exception States**
```java
public enum CosmosExceptionStates {
    ORDER_NOT_FOUND("COSMOS_001", "Order not found"),
    INVALID_ORDER_ID("COSMOS_002", "Invalid order ID format"),
    AUTHENTICATION_FAILED("COSMOS_003", "Authentication failed"),
    SERVICE_UNAVAILABLE("COSMOS_004", "Cosmos service unavailable"),
    INVALID_STATE("COSMOS_005", "Invalid order state");
}
```

### **Exception Usage**
```java
try {
    OrderStateInfo stateInfo = helper.getOrderStateInfo("INVALID_ORDER");
} catch (CosmosException e) {
    if (e.getState() == CosmosExceptionStates.ORDER_NOT_FOUND) {
        log.error("Order not found: {}", e.getMessage());
    } else if (e.getState() == CosmosExceptionStates.INVALID_ORDER_ID) {
        log.error("Invalid order ID format: {}", e.getMessage());
    }
}
```

## 📈 **Performance Features**

- **Connection Pooling**: Efficient HTTP connection management
- **Response Caching**: Cache order state information for performance
- **Async Operations**: Support for asynchronous order state monitoring
- **Retry Mechanisms**: Automatic retry for transient failures
- **Timeout Management**: Configurable timeouts for API calls

## 🔗 **Integration Points**

### **With Other Modules**
- **Commons**: Uses shared utilities and configuration management
- **Juno**: Integrates for order creation and initial state tracking
- **NEXS**: Provides order state updates for fulfillment tracking
- **SCM**: Shares order state information for supply chain management

### **External Services**
- **Cosmos Stateview Service**: Primary integration for order state management
- **Order Management System**: Receives order state updates
- **Notification Service**: Triggers notifications based on state changes
- **Analytics Service**: Provides order state data for analytics

## 🚀 **Getting Started**

### **1. Add Cosmos Dependency**
```xml
<dependency>
    <groupId>com.lenskart</groupId>
    <artifactId>cosmos</artifactId>
    <version>1.0.0</version>
</dependency>
```

### **2. Configure Cosmos Service**
Update `cosmos.yml` with your Cosmos service configuration.

### **3. Create Your First Test**
```java
public class MyCosmosTest {
    @Test
    @TestCategory(TestCategory.Category.SANITY)
    public void testOrderStateRetrieval() {
        StateviewHelper helper = new StateviewHelper();
        Response response = helper.getOrderById("1930753219");
        assert response.getStatusCode() == 200;
    }
}
```

## 📚 **Examples**

Check the test classes for comprehensive examples:
- **Order State Retrieval**: Get order state and details
- **Order Validation**: Validate order existence and format
- **State Transition Monitoring**: Track order state changes
- **Authentication**: Handle authentication for Cosmos APIs
- **Error Handling**: Handle various error scenarios

## 🔄 **Real API Integration**

The Cosmos module successfully integrates with the real Cosmos Stateview service:

```bash
# Example API call that works
curl --location 'https://stateview.cosmos.preprod.lenskart.com/cosmos-stateview/v1/order/1930753219' \
--header 'X-Auth-Token: Bearer 123'
```

**Response:**
```json
{
  "result": {
    "orderId": "1930753219",
    "currentState": "PROCESSING",
    "mappedState": "PROCESSING",
    "mappedStatus": "PROCESSING",
    "mappedTrackingStatus": "IN_WAREHOUSE",
    "orderCountry": "IN",
    "items": [...]
  },
  "status": 200
}
```

The Cosmos module provides comprehensive automation capabilities for testing Lenskart's order state management and tracking functionality, ensuring reliable order visibility throughout the order lifecycle.

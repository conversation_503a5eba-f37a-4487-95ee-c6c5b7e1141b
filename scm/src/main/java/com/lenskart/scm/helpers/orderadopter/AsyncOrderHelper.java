package com.lenskart.scm.helpers.orderadopter;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.scm.helpers.ScmBaseHelper;
import com.lenskart.scm.model.ScmOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import org.json.JSONArray;

import static com.lenskart.scm.endpoints.ScmEndpoints.ASYNC_ORDER;

@SuperBuilder
public class AsyncOrderHelper extends ScmBaseHelper implements ServiceHelper {

    Response response;
    JSONArray payload;
    String unicomOrderCode;

    @Override
    public ServiceHelper init() {
        payload = new JSONArray(unicomOrderCode);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response =  RestUtils.post(ASYNC_ORDER.getUrl(), null, payload.toString(), 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

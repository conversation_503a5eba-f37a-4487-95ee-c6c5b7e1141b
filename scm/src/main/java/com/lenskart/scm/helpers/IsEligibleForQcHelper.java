package com.lenskart.scm.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

import static com.lenskart.scm.endpoints.ScmEndpoints.ORDER_INTERCEPTOR_IS_ELIGIBLE_FOR_QC;

@SuperBuilder
@Slf4j
public class IsEligibleForQcHelper extends ScmBaseHelper implements ServiceHelper {
Response response;

    @Override
    public ServiceHelper init() {
        return this;
    }

    @Override
    public ServiceHelper process() {
        RestUtils.get(ORDER_INTERCEPTOR_IS_ELIGIBLE_FOR_QC.getUrl(Map.of("unicomOrderCode","")), null, null, 200 );
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

}

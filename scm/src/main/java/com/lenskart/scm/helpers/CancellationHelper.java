package com.lenskart.scm.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.scm.model.CancellationRequest;
import com.lenskart.scm.requestbuilders.SCMRequestBuilders;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

import static com.lenskart.scm.endpoints.ScmEndpoints.CANCEL_INVOICE;

@SuperBuilder
public class CancellationHelper extends ScmBaseHelper implements ServiceHelper {


    String payload;
    OrderContext orderContext;
    Response response;
    CancellationRequest cancellationRequest;

    @Override
    public ServiceHelper init() {
        payload = JsonUtils.convertObjectToJsonString(SCMRequestBuilders.getCancellationRequest(orderContext));
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(CANCEL_INVOICE.getUrl(), null, payload,200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

package com.lenskart.scm.helpers;


import com.lenskart.commons.base.BaseHelper;
import com.lenskart.commons.constants.HeaderMapper;
import com.lenskart.scm.constants.ScmConstants;
import com.lenskart.scm.exceptions.ScmExceptionStates;
import com.lenskart.scm.model.ScmOrderContext;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;


@SuperBuilder
@Slf4j
public class ScmBaseHelper extends BaseHelper<ScmExceptionStates, Object> {

    public Map<String, String> getHeaders() {
        Map<String, String> getHeaders = new HashMap<>();
        getHeaders.put(HeaderMapper.CONTENT_TYPE.getHeaderName(), "application/json");
        return getHeaders;
    }

    public Map<String, String> getHeadersForSensi() {
        Map<String, String> headers = new HashMap<>();
        headers.put(HeaderMapper.X_CLIENT_KEY.getHeaderName(), ScmConstants.ORDER_INTERCEPTOR);
        headers.put(HeaderMapper.AUTHORIZATION.getHeaderName(), ScmConstants.AUTHORIZATION_FOR_SENSEI);
        return headers;
    }

    public Map<String, Object> getQueryParamsForDocumentDetails(String referenceId) {
        queryParams = new HashMap<>();
        queryParams.put("referenceId", referenceId);
        queryParams.put("documentReferenceType", "MAGENTO_ITEM_ID");
        queryParams.put("jsonRequest", "true");
        return queryParams;
    }

    public Map<String, Object> getQueryParamsForLsmServicibility(ScmOrderContext.LsmServicibility lsmServicibility) {
        queryParams = new HashMap<>();
        queryParams.put("is_reverse_pickup", lsmServicibility.getIs_reverse_pickup());
        queryParams.put("is_express", lsmServicibility.getIs_express());
        queryParams.put("facility_code", lsmServicibility.getFacility_code());
        queryParams.put("is_cod", lsmServicibility.getIs_cod());
        queryParams.put("is_prepaid", lsmServicibility.getIs_prepaid());
        queryParams.put("is_liquid", lsmServicibility.getIs_liquid());
        return queryParams;
    }
}


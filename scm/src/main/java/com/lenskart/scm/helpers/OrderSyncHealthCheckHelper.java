package com.lenskart.scm.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import static com.lenskart.scm.endpoints.ScmEndpoints.*;

@SuperBuilder
@Slf4j
public class OrderSyncHealthCheckHelper extends ScmBaseHelper implements ServiceHelper {
    Response response;

    @Override
    public ServiceHelper init() {
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(ORDER_OPS_ACTUATOR_HEALTH.getUrl(), null, null, 200);
        if (response.getStatusCode() != 200) {
            log.error("Order Ops is not up and running");
        }
        log.info("Order Ops is up and running");
        response = RestUtils.get(OPTIMA_ACTUATOR_HEALTH.getUrl(), null, null, 200);
        if (response.getStatusCode() != 200) {
            log.error("Optima is not up and running");
        }
        log.info("Optima is up and running");
        response = RestUtils.get(ORDER_SENSEI_ACTUATOR_HEALTH.getUrl(), null, null, 200);
        if (response.getStatusCode() != 200) {
            log.error("Order Sensei is not up and running");
        }
        log.info("Order Sensei is up and running");
        response = RestUtils.get(ORDER_INTERCEPTOR_ACTUATOR_HEALTH.getUrl(), null, null, 200);
        if (response.getStatusCode() != 200) {
            log.error("Order Interceptor is not up and running");
        }
        log.info("Order Interceptor is up and running");
        response = RestUtils.get(ORDER_ADOPTER_ACTUATOR_HEALTH.getUrl(), null, null, 200);
        if (response.getStatusCode() != 200) {
            log.error("Order Adopter is not up and running");
        }
        log.info("Order Adopter is up and running");
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

package com.lenskart.scm.helpers.ordersensi;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.scm.helpers.ScmBaseHelper;
import com.lenskart.scm.requestbuilders.OrderSenseiRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import static com.lenskart.scm.endpoints.ScmEndpoints.ORDER_SENSI_PROCESS_OTC_SHIPMENT_EVENT;


@SuperBuilder
public class ProcessOTCShipmentEventHelper extends ScmBaseHelper implements ServiceHelper {
    String payload;
    Response response;
    String eventType;
    int shipmentId;

    @Override
    public ServiceHelper init() {
        headers = getHeadersForSensi();
        payload = OrderSenseiRequestBuilder.processOTCShipmentEventPaylod(eventType,shipmentId);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(ORDER_SENSI_PROCESS_OTC_SHIPMENT_EVENT.getUrl(), headers, payload, 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        return init().process().validate();
    }

}

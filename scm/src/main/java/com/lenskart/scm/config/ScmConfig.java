package com.lenskart.scm.config;


import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;


/**
 * Configuration class for the Scm module
 */
@Data
@NoArgsConstructor
public class ScmConfig {

    // Environment configurations
    private Map<String, EnvironmentConfig> environments = new HashMap<>();

    /**
     * Gets the configuration for a specific environment
     *
     * @param environment Environment name (e.g., "preprod", "prod")
     * @return Environment configuration
     */
    public EnvironmentConfig getEnvironment(String environment) {
        return environments.get(environment);
    }

    /**
     * Configuration for a specific environment
     */
    @Data
    @NoArgsConstructor
    public static class EnvironmentConfig {
        // Base URLs for different services
        private Map<String, String> baseUrls = new HashMap<>();

        /**
         * Gets the base URL for a specific service
         *
         * @param serviceName Service name
         * @return Base URL for the service
         */
        public String getBaseUrl(String serviceName) {
            return baseUrls.get(serviceName);
        }
    }
}

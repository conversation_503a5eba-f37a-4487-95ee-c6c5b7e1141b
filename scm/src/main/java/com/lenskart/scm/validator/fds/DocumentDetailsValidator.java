package com.lenskart.scm.validator.fds;

import com.lenskart.commons.base.IValidator;
import com.lenskart.fds.response.thermalInvoice.Address;
import com.lenskart.fds.response.thermalInvoice.ResultUnicomOrder;
import com.lenskart.fds.response.thermalInvoice.ThermalInvoiceResponse;
import com.lenskart.scm.database.ScmDbUtils;
import com.lenskart.scm.helpers.fds.DocumentDetailsHelper;
import com.lenskart.scm.model.ScmOrderContext;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Builder
@Slf4j
public class DocumentDetailsValidator implements IValidator {
    DocumentDetailsHelper documentDetailsHelper;
    ScmOrderContext scmOrderContext;
    String grandTotal;
    Address billingAddress ;
    Address shippingAddress ;
    String shipmentId;
    List<ResultUnicomOrder> resultUnicomOrderQuery;


    @Override
    public void validateNode() {
        if(scmOrderContext.getIsValidationRequired()){
            log.info("Validating document details");
            Assert.assertEquals(documentDetailsHelper.getResponse().getStatusCode(), 200);
            ThermalInvoiceResponse thermalInvoiceResponse = documentDetailsHelper.getThermalInvoiceResponse();
             grandTotal = String.valueOf(thermalInvoiceResponse.getData().getGrand_total());
            billingAddress = thermalInvoiceResponse.getData().getBilling_address();
            shippingAddress = thermalInvoiceResponse.getData().getShipping_address();
            shipmentId = thermalInvoiceResponse.getData().getResultUnicomOrderQuery().getFirst().getShipping_package_id();
            resultUnicomOrderQuery = thermalInvoiceResponse.getData().getResultUnicomOrderQuery();
            validateDBEntities();
        }
    }

    @Override
    public void validateDBEntities() {
        if(scmOrderContext.getIsValidationRequired()){
            log.info("Validating document details in DB");
            List<Map<String, Object>> dbValuesOrderDetails = ScmDbUtils.getReferenceId(scmOrderContext.getShippingId());
            List<Map<String, Object>> dbValuesBillingAddress = ScmDbUtils.getBillingAddress(scmOrderContext.getOrderId());
            List<Map<String, Object>> dbValuesShippingAddress = ScmDbUtils.getShippingAddress(scmOrderContext.getOrderId());
            log.info("Billing Address: {}", billingAddress);
            Assert.assertEquals(billingAddress.getFirstname(),dbValuesBillingAddress.getFirst().get("firstname")+" " + dbValuesBillingAddress.getFirst().get("lastname"));
            Assert.assertEquals(billingAddress.getCity(),dbValuesBillingAddress.getFirst().get("city"));
            Assert.assertEquals(billingAddress.getPostcode(),dbValuesBillingAddress.getFirst().get("postcode"));
            Assert.assertEquals(shippingAddress.getFirstname(),dbValuesShippingAddress.getFirst().get("firstname")+" " + dbValuesShippingAddress.getFirst().get("lastname"));
            Assert.assertEquals(shippingAddress.getCity(),dbValuesShippingAddress.getFirst().get("city"));
            Assert.assertEquals(shippingAddress.getPostcode(),dbValuesShippingAddress.getFirst().get("postcode"));
            Assert.assertEquals(shipmentId,scmOrderContext.getShippingId());
            Assert.assertEquals(new BigDecimal(grandTotal).stripTrailingZeros(),new BigDecimal(dbValuesOrderDetails.getFirst().get("order_grand_total").toString()).stripTrailingZeros());
            if(scmOrderContext.getShippingCharges() > 0) {
                resultUnicomOrderQuery.forEach(resultUnicomOrder -> {
                    if (resultUnicomOrder.getResultGstDetailsQuery().getProd_desc().equals("Fitting / Convenience Fee")) {
                        Assert.assertEquals(resultUnicomOrder.getResultGstDetailsQuery().getTotal_amount(), scmOrderContext.getShippingCharges());
                    }
                });
            }
        }
    }

}

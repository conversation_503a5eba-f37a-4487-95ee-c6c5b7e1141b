package com.lenskart.scm.model;

import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class ScmOrderContext {
    String shippingId;
    String incrementId;
    String magentoItemId;
    String status;
    String state;
    String uwItemId;
    String orderId;
    String source;
    int hsnCode;
    String powerType ;
    String stateCode ;
    double totalValue ;
    String courierCode;
    String packetId;
    @Builder.Default
    Boolean isValidationRequired=false;
    String unicomOrderCode;
    float shippingCharges;
    String pincode;
    String poNumber;
    String customerCode;
    String doType;
    String userName;
    String filePath;
    String facilityCode;

    @Builder
    @Data
    public static class LsmServicibility {
        private Boolean is_reverse_pickup;
        private Boolean is_express;
        private String facility_code;
        private Boolean is_cod;
        private Boolean is_prepaid;
        private Boolean is_liquid;
        private Boolean is_enabled;
    }
}

package com.lenskart.scm.endpoints;

import com.lenskart.commons.endpoints.EndpointManager;
import com.lenskart.scm.config.ScmConfigProvider;
import groovy.util.logging.Slf4j;

import java.util.Map;

@Slf4j
public class ScmEndpointManager extends EndpointManager<ScmEndpoints> {

    // Singleton instance
    private static volatile ScmEndpointManager instance;

    /**
     * Private constructor for singleton pattern
     */
    private ScmEndpointManager() {
        super(ScmConfigProvider.getInstance(), ScmEndpoints.class);
    }

    /**
     * Gets the singleton instance of ScmEndpointManager
     *
     * @return The singleton instance
     */
    public static ScmEndpointManager getInstance() {
        if (instance == null) {
            synchronized (ScmEndpointManager.class) {
                if (instance == null) {
                    instance = new ScmEndpointManager();
                }
            }
        }
        return instance;
    }

    /**
     * Convenience method to get URL for a Scm endpoint
     *
     * @param endpoint The Scm endpoint
     * @return Complete URL for the endpoint
     */
    public static String getEndpointUrl(ScmEndpoints endpoint) {
        return getInstance().getUrl(endpoint);
    }

    /**
     * Convenience method to get URL for a Scm endpoint with path parameters
     *
     * @param endpoint The Scm endpoint
     * @param pathParams Map of path parameters to replace in the URL
     * @return Complete URL with path parameters replaced
     */
    public static String getEndpointUrl(ScmEndpoints endpoint, Map<String, String> pathParams) {
        return getInstance().getUrl(endpoint, pathParams);
    }

    /**
     * Convenience method to refresh all Scm endpoint URLs
     */
    public static void refreshEndpoints() {
        getInstance().refresh();
    }

    /**
     * Convenience method to validate all Scm endpoints
     *
     * @return true if all endpoints are valid, false otherwise
     */
    public static boolean validateAllEndpoints() {
        return getInstance().validateEndpoints();
    }

    /**
     * Convenience method to get all Scm endpoint URLs
     *
     * @return Map of endpoint names to their full URLs
     */
    public static Map<String, String> getAllEndpointUrls() {
        return getInstance().getAllUrls();
    }
}

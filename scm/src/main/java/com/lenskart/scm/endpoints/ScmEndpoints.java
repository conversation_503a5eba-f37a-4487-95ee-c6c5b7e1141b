package com.lenskart.scm.endpoints;

import com.lenskart.scm.config.ScmConfigRegistry;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

@Getter
public enum ScmEndpoints {

    //Order Interceptor Service Endpoints
    ORDER_INTERCEPTOR_IS_ELIGIBLE_FOR_QC("/inbound/order-mgmt/sbrt/isEligibleForQc/{$unicomOrderCode}", "orderInterceptorService");

    private final String endpoint;
    private final String serviceName;

    // Static maps to store base URLs and full URLs
    private static final Map<String, String> serviceBaseUrls = new HashMap<>();
    private static final Map<ScmEndpoints, String> fullUrls = new HashMap<>();

    // Flag to track if initialization has been done
    private static boolean initialized = false;

    // Static initialization block to load all base URLs once
    static {
        initializeUrls();
    }

    ScmEndpoints(String endpoint, String serviceName) {
        this.endpoint = endpoint;
        this.serviceName = serviceName;
    }

    /**
     * Initializes all URLs by loading the configuration once
     */
    private static synchronized void initializeUrls() {
        if (initialized) {
            return;
        }

        // Pre-load all service base URLs
        for (ScmEndpoints endpoint : values()) {
            if (!serviceBaseUrls.containsKey(endpoint.serviceName)) {
                // Get the base URL from SCMConfigRegistry
                String baseUrl = ScmConfigRegistry.getInstance().getBaseUrl(endpoint.serviceName);

                if (baseUrl == null) {
                    throw new IllegalStateException("Base URL not found for service: " + endpoint.serviceName);
                }

                // Remove trailing slash from base URL if present
                if (baseUrl.endsWith("/")) {
                    baseUrl = baseUrl.substring(0, baseUrl.length() - 1);
                }

                serviceBaseUrls.put(endpoint.serviceName, baseUrl);
            }
        }

        // Pre-compute all full URLs
        for (ScmEndpoints endpoint : values()) {
            String baseUrl = serviceBaseUrls.get(endpoint.serviceName);

            // Ensure endpoint starts with a slash
            String path = endpoint.endpoint;
            if (!path.startsWith("/")) {
                path = "/" + path;
            }

            // Store the full URL
            fullUrls.put(endpoint, baseUrl + path);
        }

        initialized = true;
    }

    /**
     * Gets the complete URL for this endpoint
     *
     * @return Complete URL for the endpoint
     */
    public String getUrl() {
        // Ensure URLs are initialized
        if (!initialized) {
            initializeUrls();
        }

        // Return the pre-computed URL
        return fullUrls.get(this);
    }

    /**
     * Gets the complete URL for this endpoint with path parameters replaced
     *
     * @param pathParams Map of path parameters to replace in the URL
     * @return Complete URL with path parameters replaced
     */
    public String getUrl(Map<String, String> pathParams) {
        String url = getUrl();

        if (pathParams != null) {
            for (Map.Entry<String, String> entry : pathParams.entrySet()) {
                url = url.replace("{$" + entry.getKey() + "}", entry.getValue());
            }
        }

        return url;
    }


}

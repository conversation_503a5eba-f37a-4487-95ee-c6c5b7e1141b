package com.lenskart.scm.endpoints;

import com.lenskart.commons.endpoints.BaseEndpoint;
import lombok.Getter;

import java.util.Map;

@Getter
public enum ScmEndpoints implements BaseEndpoint {

    //Order Interceptor Service Endpoints
    ORDER_INTERCEPTOR_IS_ELIGIBLE_FOR_QC("/inbound/order-mgmt/sbrt/isEligibleForQc/{$unicomOrderCode}", "orderInterceptorService"),
    CANCEL_INVOICE("/{$orderID}/cancel-invoice", "orderOpsService"),
    SYNC_ORDER("/order/sync_order","orderAdopterService");

    private final String endpoint;
    private final String serviceName;


    ScmEndpoints(String endpoint, String serviceName) {
        this.endpoint = endpoint;
        this.serviceName = serviceName;
    }


    @Override
    public String getUrl() {
        return ScmEndpointManager.getEndpointUrl(this);
    }

    @Override
    public String getUrl(Map<String, String> pathParams) {
        return ScmEndpointManager.getEndpointUrl(this, pathParams);
    }



}

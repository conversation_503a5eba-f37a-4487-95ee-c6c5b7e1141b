package com.lenskart.scm.endpoints;

import com.lenskart.commons.endpoints.BaseEndpoint;
import lombok.Getter;

import java.util.Map;

@Getter
public enum ScmEndpoints implements BaseEndpoint {

    //Order Interceptor Service Endpoints
    ORDER_INTERCEPTOR_IS_ELIGIBLE_FOR_QC("/inbound/order-mgmt/sbrt/isEligibleForQc/{$unicomOrderCode}", "orderInterceptorService"),

    //Order Adopter Service Endpoints
    SYNC_ORDER("/order/sync_order","orderAdopterService"),
    ASYNC_ORDER("/sync-unicom/async","orderAdopterService"),

    //FDS Service Endpoints
    DOCUMENT_DETAILS("/fds/api/v1/document/getDocumentDetails","fdsService"),

    //lsm shipments Endpoints
    LSM_CHANGE_COURIER("/change-courier","lsmShipmentsService"),
    LSM_SERVICEABILITY("/serviceability/courier-codes/country/{$country}/pincode/{$pincode}","lsmServiceabilityService"),

    //OrderOps Service Endpoints
    GET_HSN_TAX_RATES("/tax/getHsnTaxRates","orderOpsService"),
    ORDER_OPS_CONFIRM_PAYMENT("/items/status","orderOpsService"),
    ORDER_OPS_STATUS_UPDATE("/status/update","orderOpsService"),
    ORDER_OPS_ITEM_STATUS("/items/status","orderOpsService"),

    // Order Sensei service Endpoints
    ORDER_SENSEI_GET_ORDER_DETAILS("/inbound/order-sensei/oms/api/v1/distributorOrder/create/{$doOrderId}", "nexsService"),
    ORDER_SENSEI_REJECT_DO("/inbound/order-sensei/oms/api/v1/distributorOrder/reject/{$distributorOrder}", "nexsService"),
    ORDER_SENSEI_DO_ORDER_UPLOAD("/inbound/order-sensei/oms/api/v1/distributorOrder/upload", "nexsService"),
    ORDER_SENSEI_APPROVE_DO("/inbound/order-sensei/oms/api/v1/distributorOrder/approve/{$orderId}","nexsService"),
    ORDER_SENSI_PROCESS_OTC_SHIPMENT_EVENT("/oms/api/v1/shipment/processOTCShipmentEvent","orderSenseiService"),

    ORDER_OPS_ACTUATOR_HEALTH("/actuator/health","orderOpsService"),
    OPTIMA_ACTUATOR_HEALTH("/actuator/health","optimaService"),
    ORDER_INTERCEPTOR_ACTUATOR_HEALTH("/actuator/health","orderInterceptorService"),
    ORDER_SENSEI_ACTUATOR_HEALTH("/actuator/health","orderSenseiService"),
    ORDER_ADOPTER_ACTUATOR_HEALTH("/","orderAdopterService"),
    //LSM Actuator Health
    LSM_SHIPMENTS_ACTUATOR_HEALTH("/","lsmShipmentsService"),
    LSM_COURIERS_ACTUATOR_HEALTH("/","lsmCourierService"),
    SHIPPING_ACTUATOR_HEALTH("/actuator/health","shippingService"),
    LSM_SERVICEABILITY_ACTUATOR_HEALTH("/pincode/{$pincode}","lsmServiceabilityService"),
    /*not working in preprod*/
    LSM_COURIER_ADAPTER_ACTUATOR_HEALTH("/","lsmCourierAdaptorService"),


    //Order management service Endpoints
    ORDER_MANAGEMENT("/order/getInvoiceDetails/{$shippingId}/{$orderId}/{$facilityCode}" ,"orderManagementService");


    private final String endpoint;
    private final String serviceName;


    ScmEndpoints(String endpoint, String serviceName) {
        this.endpoint = endpoint;
        this.serviceName = serviceName;
    }


    @Override
    public String getUrl() {
        return ScmEndpointManager.getEndpointUrl(this);
    }

    @Override
    public String getUrl(Map<String, String> pathParams) {
        return ScmEndpointManager.getEndpointUrl(this, pathParams);
    }



}

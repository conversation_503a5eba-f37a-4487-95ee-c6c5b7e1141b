package com.lenskart.scm.util;

import com.lenskart.commons.database.mysql.DynamicQueryExecutor;
import com.lenskart.commons.model.MySQLCluster;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.scm.constants.ScmConstants;
import com.lenskart.scm.database.ScmQueries;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.lenskart.scm.endpoints.ScmEndpoints.SYNC_ORDER;

@Slf4j
public class OrderAdopterUtil {

    public static void syncOrder(String incrementId) {
        List<Map<String, Object>> syncOrderDetails = DynamicQueryExecutor
                .executeQuery(MySQLCluster.SCM_CLUSTER.getClusterName(),
                        ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_DETAILS_TO_SYNC_ORDER,
                        incrementId);
        if (syncOrderDetails.isEmpty())
            throw new RuntimeException("Order is not created in SCM");
        for (Map<String, Object> map : syncOrderDetails) {
            if (map.get("shipping_package_id").toString().isEmpty()) {
                if (map.get("unicom_syn_status").equals(ScmConstants.READY_TO_SYNC) && map.get("shipment_status").equals(ScmConstants.PROCESSING)) {
                    Map<String, Object> queryParam = new HashMap<>();
                    queryParam.put("uniOrderCode", map.get("unicom_order_code"));
                    RestUtils.get(SYNC_ORDER.getUrl(), null, queryParam, 200);
                } else {
                    throw new RuntimeException("Order is not in ready to sync, status = " + map.get("unicom_syn_status") + " order status = " + map.get("shipment_status"));
                }
            }
        }
    }
}

package com.lenskart.scm.util;

import com.lenskart.commons.database.mysql.MySQLQueryExecutor;
import com.lenskart.commons.model.Cluster;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.scm.constants.ScmConstants;
import com.lenskart.scm.database.ScmDbUtils;
import com.lenskart.scm.database.ScmQueries;
import com.lenskart.scm.helpers.OrderSyncHealthCheckHelper;
import com.lenskart.scm.helpers.orderadopter.AsyncOrderHelper;
import com.lenskart.scm.helpers.ordersensi.ProcessOTCShipmentEventHelper;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.lenskart.scm.endpoints.ScmEndpoints.SYNC_ORDER;

@Slf4j
public class OrderAdopterUtil {

    public static void syncOrder(String incrementId) {
        AwaitUtils.sleep(Duration.ofSeconds(20));
        OrderSyncHealthCheckHelper.builder().build().test();
        boolean success = AwaitUtils.retryOperation(
                () -> {
                    try {
                        log.debug("Attempting to sync order for increment id: {}", incrementId);
                        List<Map<String, Object>> syncOrderDetails = MySQLQueryExecutor
                                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(),
                                        ScmConstants.INVENTORY_DB,
                                        ScmQueries.GET_DETAILS_TO_SYNC_ORDER,
                                        incrementId);
                        if (syncOrderDetails.isEmpty())
                            throw new RuntimeException("Order is not created in SCM");
                        for (Map<String, Object> map : syncOrderDetails) {
                            String unicomOrderCode = map.get("unicom_order_code").toString();
                            if (map.get("shipping_package_id").toString().isEmpty()) {
                                if (map.get("unicom_syn_status").equals(ScmConstants.READY_TO_SYNC) && map.get("shipment_status").equals(ScmConstants.PROCESSING)) {
                                    boolean isOMSOrder = false;
                                    List<Map<String, Object>> omsDetails = ScmDbUtils.getOMSOrderDetails(unicomOrderCode);
                                    if (omsDetails.isEmpty()) {
                                        isOMSOrder = false;
                                    } else {
                                        if (omsDetails.getFirst().get("is_enabled").equals(true)) {
                                            isOMSOrder = true;
                                            if(map.get("nav_channel").toString().toLowerCase().contains("otc")){
                                                log.info("Order is an OTC order, calling OTC sync helper");
                                                List<Map<String, Object>> shipmentDetailsForSensei = ScmDbUtils.getShipmentDetailsForSensei(unicomOrderCode);
                                                ProcessOTCShipmentEventHelper.builder()
                                                        .shipmentId(shipmentDetailsForSensei.getFirst().get("id"))
                                                        .eventType(ScmConstants.INVOICED)
                                                        .build()
                                                        .test();
                                            }else {
                                                AsyncOrderHelper.builder()
                                                        .unicomOrderCode(unicomOrderCode)
                                                        .build()
                                                        .test();
                                            }
                                        } else {
                                            isOMSOrder = false;
                                        }
                                    }
                                    if (!isOMSOrder) {
                                        Map<String, Object> queryParam = new HashMap<>();
                                        queryParam.put("uniOrderCode", map.get("unicom_order_code"));
                                        RestUtils.get(SYNC_ORDER.getUrl(), null, queryParam, 200);
                                    }
                                } else {
                                    throw new RuntimeException("Order is not in ready to sync, status = " + map.get("unicom_syn_status") + " order status = " + map.get("shipment_status"));
                                }
                            }
                        }
                        return true;
                    } catch (Exception e) {
                        log.warn("Exception during sync order attempt: {}", e.getMessage());
                        return false;
                    }
                },
                "Sync Order Retry",
                2, // max attempts
                Duration.ofSeconds(15) // 15 second poll interval
        );
        if (!success) {
            log.error("❌ Sync Order failed after polling timeout for increment id: {}",
                    incrementId);
            throw new RuntimeException("Sync Order failed after multiple attempts for increment id: " +
                    incrementId);
        }
    }
}

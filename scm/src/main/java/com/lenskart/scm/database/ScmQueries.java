package com.lenskart.scm.database;

public class ScmQueries {

    public static final String GET_MANIFEST_ID = "select shipping_manifest_id from manifest.manifest_shipments where shipping_package_id=?";
    public static final String GET_DETAILS_TO_SYNC_ORDER = "select distinct (`unicom_order_code`), `shipment_status`, unicom_syn_status, `shipping_package_id`  from uw_orders where `increment_id`=?";
    public static final String GET_SHIPMENT_STATE = "select distinct shipment_state from uw_orders where increment_id=?";
    public static final String GET_SHIPPING_PACKAGE_ID = "select distinct shipping_package_id from uw_orders where increment_id=?";
    public static final String ORDER_STATUS ="select shipment_status from uw_orders where increment_id=? and product_id=?";



}

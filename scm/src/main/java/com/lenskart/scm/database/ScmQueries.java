package com.lenskart.scm.database;

public class ScmQueries {

    //manifest table Queries
    public static final String GET_MANIFEST_ID = "select shipping_manifest_id from manifest.manifest_shipments where shipping_package_id=?";

    //uw_orders table Queries
    public static final String GET_DETAILS_TO_SYNC_ORDER = "select distinct (`unicom_order_code`), `shipment_status`, unicom_syn_status, `shipping_package_id`, `nav_channel` from uw_orders where `increment_id`=?";
    public static final String GET_SHIPMENT_STATE = "select distinct shipment_state from uw_orders where increment_id=?";
    public static final String GET_SHIPPING_PACKAGE_ID = "select distinct shipping_package_id from uw_orders where increment_id=?";
    public static final String ORDER_STATUS = "select shipment_status from uw_orders where increment_id=? and product_id=?";
    public static final String GET_REFRENCE_ID = "SELECT o.* FROM orders o JOIN (SELECT item_id FROM uw_orders WHERE shipping_package_id =? AND parent_uw = '0' ORDER BY uw_item_id ASC LIMIT 1) u ON o.item_id = u.item_id ";
    public static final String GET_SHIPMENT_STATUS = "select shipment_status from uw_orders where =?";
    public static final String GET_DISPATCHED_SHIPMENT = "select * from uw_orders where unicom_shipment_status ='DISPATCHED'  and product_delivery_type != 'OTC' order by uw_item_id desc limit 1 ";
    public static final String GET_SHIPPING_ID_FOR_SHIPPING_CHARGES = "SELECT u.`shipping_package_id`, i.`shipping_charges` FROM item_wise_prices i JOIN uw_orders u ON i.item_id = u.item_id WHERE i.shipping_charges != 0 AND u.unicom_shipment_status = 'DISPATCHED' and u.facility_code in ('NXS2', 'QNXS2') ORDER BY i.id DESC limit 1";

    //order_address_update table Queries
    public static final String GET_BILLING_ADDRESS =  "select * from order_address_update where parent_id=? and address_type ='billing'" ;
    public static final String GET_SHIPPING_ADDRESS =  "select * from order_address_update where parent_id=? and address_type ='shipping'" ;

    //OMS table Queries
    public static final String GET_OMS_ORDERS = "select * from oms_orders where unicom_order_code=?";

    //Order Sensi table queries
    public static final String GET_SHIPMENT_DETAILS_FOR_SENSEI = " select * from order_sensei.shipment where `wms_order_code`=?";

    //Orders table Queries
    public static final String GET_MAGENTO_ITEM_ID = "SELECT o.magento_item_id FROM orders o JOIN uw_orders u ON o.item_id = u.item_id WHERE u.shipping_package_id =? LIMIT 1;";

    //virtual_shipment_cancel_tasks table
    public static final String GET_VIRTUAL_SHIPMENT_CANCEL_TASKS = "select * from inventory.virtual_shipment_cancel_tasks where `increment_id`=?";

}

package com.lenskart.scm.database;

import com.lenskart.commons.database.mysql.MySQLQueryExecutor;
import com.lenskart.commons.model.Cluster;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.scm.constants.ScmConstants;

import java.util.List;
import java.util.Map;

public class ScmDbUtils {
    public static String getShipmentState(String incrementId) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_SHIPMENT_STATE,
                        incrementId).getFirst().get("shipment_state").toString();
    }

    public static List<Map<String, Object>> getShippingPackageId(String incrementId) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_SHIPPING_PACKAGE_ID,
                        incrementId);
    }

    public static List<Map<String, Object>> getOrderStatus(String incrementId,String productID) {
        List<Map<String, Object>> orderStatus = MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.ORDER_STATUS,
                        incrementId,productID) ;
        return orderStatus;
    }

    public static List<Map<String, Object>> getReferenceId(String shippingPackageId) {
        return  MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_REFRENCE_ID,
                        shippingPackageId);
    }

    public static List<Map<String, Object>> getBillingAddress(String orderID) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_BILLING_ADDRESS,
                        orderID);
    }

    public static List<Map<String, Object>> getShippingAddress(String orderID) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_SHIPPING_ADDRESS,
                        orderID);
    }

    public static List<Map<String, Object>> getDispatchedShipment() {
        return MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_DISPATCHED_SHIPMENT);
    }

    public static List<Map<String, Object>> getOMSOrderDetails(String unicomOrderCode) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_OMS_ORDERS,
                        unicomOrderCode);
    }

    public static List<Map<String, Object>> getShipmentDetailsForSensei(String unicomOrderCode) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.NEXS_CLUSTER.getClusterName(), ScmConstants.ORDER_SENSEI_DB,
                        ScmQueries.GET_SHIPMENT_DETAILS_FOR_SENSEI,
                        unicomOrderCode);
    }

    public static String getMagentoItemId(String shippingPackageId) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_MAGENTO_ITEM_ID,
                        shippingPackageId).getFirst().get("magento_item_id").toString();
    }

    public static List<Map<String, Object>> getShippingIdForShippingCharges() {
        return MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_SHIPPING_ID_FOR_SHIPPING_CHARGES);
    }

    public static List<Map<String, Object>> getVirtualShipmentCancelTasks(String incrementId) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_VIRTUAL_SHIPMENT_CANCEL_TASKS,
                        incrementId);
    }
}

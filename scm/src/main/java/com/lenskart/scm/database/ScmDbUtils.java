package com.lenskart.scm.database;

import com.lenskart.commons.database.mysql.MySQLQueryExecutor;
import com.lenskart.commons.model.Cluster;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.scm.constants.ScmConstants;

import java.util.List;
import java.util.Map;

public class ScmDbUtils {
    public static String getShipmentState(String incrementId) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_SHIPMENT_STATE,
                        incrementId).getFirst().get("shipment_state").toString();
    }

    public static List<Map<String, Object>> getShippingPackageId(String incrementId) {
        return MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.GET_SHIPPING_PACKAGE_ID,
                        incrementId);
    }

    public static List<Map<String, Object>> getOrderStatus(String incrementId, String productId) {
        List<Map<String, Object>> orderStatus = MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), ScmConstants.INVENTORY_DB,
                        ScmQueries.ORDER_STATUS,
                        incrementId,productId) ;
        return orderStatus;
    }
}

package com.lenskart.scm.requestbuilders;

import com.lenskart.commons.model.OrderContext;
import com.lenskart.scm.model.CancellationRequest;


public class SCMRequestBuilders {

    public static CancellationRequest getCancellationRequest(OrderContext orderContext) {
        return CancellationRequest.builder()
                .initiated_by("<EMAIL>")
                .source("vsm")
                .payment_method(orderContext.getPaymentMethod())
                .reason_detail("")
                .reason_id(188)
                .cancellation_type("full_cancellation")
                .build();
    }
}

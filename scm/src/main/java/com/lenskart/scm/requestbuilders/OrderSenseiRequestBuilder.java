package com.lenskart.scm.requestbuilders;

import com.lenskart.scm.model.ScmOrderContext;
import com.lenskart.scm.model.ordersensi.GetPayloadForDoOrderUploadApiModel;
import org.json.JSONObject;

public class OrderSenseiRequestBuilder {

    public static String processOTCShipmentEventPaylod(String eventType,Object shipmentId) {
        JSONObject payload = new JSONObject();
        payload.put("shipmentId", shipmentId);
        payload.put("eventType", eventType);
        return payload.toString();
    }

    public static GetPayloadForDoOrderUploadApiModel getPayloadForDoOrder(ScmOrderContext scmOrderContext) {
        // Get the path to the CSV file from resources
        String filePath = OrderSenseiRequestBuilder.class.getClassLoader()
                .getResource(scmOrderContext.getFilePath()).getPath();

        return GetPayloadForDoOrderUploadApiModel.builder()
                .filePath(filePath)
                .facilityCode(scmOrderContext.getFacilityCode())
                .poNumber(scmOrderContext.getPoNumber())
                .customerCode(scmOrderContext.getCustomerCode())
                .doType(scmOrderContext.getDoType())
                .userName(scmOrderContext.getUserName())
                .build();
    }

}

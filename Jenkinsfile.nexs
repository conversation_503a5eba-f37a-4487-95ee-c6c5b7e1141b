@Library('jenkins/shared-functions.groovy') _

// NEXS Module Jenkins Pipeline
// This pipeline is specifically configured for the nexs module

def sharedFunctions

node {
    // Load shared functions
    sharedFunctions = load 'jenkins/shared-functions.groovy'
}

// Module-specific configuration
def moduleConfig = [
    moduleName: 'nexs',
    suiteXmlFile: 'src/test/resources/nexs-regression.xml',
    defaultEnvironment: 'preprod',
    defaultTestCategory: 'REGRESSION'
]

// Execute the pipeline using shared functions
sharedFunctions.executeModulePipeline(moduleConfig)

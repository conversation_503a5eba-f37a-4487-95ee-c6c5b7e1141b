# BE Automation Framework

A comprehensive backend automation framework for running service-level and end-to-end automated functional tests across Juno, SCM, NEXS, CSE, POS, and other Lenskart services.

## Overview

This framework provides a modular, maintainable, and extensible solution for testing backend services. It follows a multi-layered architecture with a strong emphasis on reusability and configurability. The framework is designed to support testing at different levels - from individual service APIs to complex end-to-end flows across multiple services.

### Architecture

The framework follows a layered architecture:

1. **Core Layer (Commons Module)**: Contains reusable components, utilities, and base classes
2. **Service Layer**: Service-specific modules with their own configurations and test implementations
3. **Integration Layer**: E2E module that orchestrates tests across multiple services
4. **Execution Layer**: Test runners, CI/CD integration, and reporting mechanisms

### Key Features

- **Modular Architecture**: Independent modules that can be used separately or together, allowing teams to work on different service tests simultaneously without conflicts

- **Reusable Components**: Common utilities extracted into a shared commons module, including:
  - Database connection managers with connection pooling
  - REST API utilities with request/response logging
  - Configuration management with environment-specific settings
  - Test execution helpers and base classes
  - Reporting and logging utilities

- **Design Patterns Implementation**:
  - <PERSON><PERSON> for configuration registries
  - <PERSON><PERSON><PERSON> Pattern for test data and request construction
  - <PERSON> Pattern for database connections
  - Template Method Pattern for standardized test execution flow
  - Strategy Pattern for different validation approaches
  - Facade Pattern for simplifying complex subsystems

- **Flexible Configuration**: Environment-specific configuration through YAML files with:
  - Hierarchical configuration structure
  - Environment-specific overrides (preprod, prod)
  - Runtime configuration changes
  - Centralized configuration registry

- **Database Connectivity**: Support for multiple database types with secure SSH tunneling:
  - MySQL with HikariCP connection pooling
  - MongoDB with type-safe query execution
  - Redis for caching and key-value operations
  - Elasticsearch for search and analytics operations

- **Comprehensive Reporting**: Multi-format reporting for detailed test analysis:
  - Allure Reports with test steps, attachments, and categorization
  - Extent Reports with visual dashboards and charts
  - Detailed logging with module-specific log files
  - HTML test reports for easy sharing

- **Test Categorization**: Hierarchical test organization:
  - By module (juno, scm, nexs, etc.)
  - By category (SANITY, REGRESSION, E2E)
  - By functionality (using TestNG groups)
  - Custom filtering through TestNG listeners

- **CI/CD Integration**: Parameterized Jenkins pipeline for flexible test execution:
  - Environment selection (preprod, prod)
  - Test suite selection (by module)
  - Test category selection (by test type)
  - Automated reporting and notification

## Project Structure

```
be-automation/
├── commons/                                # Core reusable components
│   ├── src/main/java/com/lenskart/commons/
│   │   ├── annotations/                   # Custom annotations
│   │   ├── base/                          # Base classes and interfaces
│   │   ├── config/                        # Configuration classes
│   │   ├── constants/                     # Constants and enums
│   │   ├── database/                      # Database connectivity
│   │   │   ├── mysql/                     # MySQL connection and queries
│   │   │   ├── mongodb/                   # MongoDB connection and queries
│   │   │   ├── redis/                     # Redis connection and operations
│   │   │   └── elasticsearch/             # Elasticsearch connection and operations
│   │   ├── examples/                      # Example implementations
│   │   ├── listeners/                     # TestNG listeners
│   │   ├── loader/                        # Configuration loaders
│   │   ├── model/                         # Common data models
│   │   ├── reporting/                     # Reporting utilities
│   │   └── utils/                         # Utility classes
│   └── src/main/resources/                # Configuration files
│
├── juno/                                  # Juno service tests
│   ├── src/main/java/com/lenskart/juno/
│   │   ├── api/                           # API clients
│   │   ├── config/                        # Juno-specific configuration
│   │   ├── endpoints/                     # API endpoints
│   │   ├── exceptions/                    # Exception handling
│   │   ├── helpers/                       # Test helpers
│   │   ├── model/                         # Data models
│   │   ├── requestbuilder/                # Request builders
│   │   └── validator/                     # Response validators
│   ├── src/main/resources/                # Juno-specific resources
│   └── src/test/java/com/lenskart/juno/   # Test classes
│
├── scm/                                   # SCM service tests (similar structure)
├── nexs/                                  # NEXS service tests (similar structure)
├── cse/                                   # CSE service tests (similar structure)
├── pos/                                   # POS service tests (similar structure)
│
├── e2e/                                   # End-to-end tests across services
│   ├── src/main/java/com/lenskart/e2e/
│   │   ├── helper/                        # E2E test helpers
│   │   └── validator/                     # E2E validators
│   ├── src/test/java/com/lenskart/e2e/    # E2E test classes
│   └── src/test/resources/                # TestNG configuration
│
├── example/                               # Example implementation
│   ├── src/main/java/com/lenskart/example/
│   └── src/test/java/com/lenskart/example/
│
├── docs/                                  # Documentation
│   └── TestCategories.md                  # Test categories documentation
│
├── Jenkinsfile                            # CI/CD pipeline definition
├── BE_Automation_Framework_Design.md      # Detailed design document
└── pom.xml                                # Maven project configuration
```

## Getting Started

### Prerequisites

- Java 17 or higher
- Maven 3.8 or higher
- Git

### Setup

1. Clone the repository:
   ```bash
   git clone https://github.com/lenskart/be-automation.git
   cd be-automation
   ```

2. Install dependencies:
   ```bash
   mvn clean install -DskipTests
   ```

### Running Tests

#### Run All Tests

```bash
mvn test
```

#### Run Tests for a Specific Module

```bash
mvn test -pl juno
```

#### Run Tests by Category

```bash
mvn test -DtestCategory=SANITY
```

#### Run Tests for a Specific Environment

```bash
mvn test -Denvironment=preprod
```

#### Combining Parameters

```bash
mvn test -pl juno -DtestCategory=SANITY -Denvironment=preprod
```

## Test Categories

Tests in this framework are categorized into three types:

1. **SANITY**: Basic smoke tests that verify core functionality
2. **REGRESSION**: More comprehensive tests that verify all functionality
3. **E2E**: End-to-end tests that verify complete user flows

For more details, see [Test Categories Documentation](docs/TestCategories.md).

## Configuration

The framework uses YAML configuration files for environment-specific settings. The configuration system is designed to be hierarchical, with common settings in the central configuration and service-specific settings in module-specific files.

### Configuration Files

- `commons/src/main/resources/config.yml`: Common configuration for all modules
- `juno/src/main/resources/juno.yml`: Juno-specific configuration
- `scm/src/main/resources/scm.yml`: SCM-specific configuration
- `nexs/src/main/resources/nexs.yml`: NEXS-specific configuration
- `cse/src/main/resources/cse.yml`: CSE-specific configuration
- `pos/src/main/resources/pos.yml`: POS-specific configuration

### Configuration Structure

Each configuration file follows a similar structure with environment-specific sections:

```yaml
# Environment-specific configuration
preprod:
  # Database configurations
  databases:
    default:
      url: ***************************************************
      username: preprod_user
      password: preprod_password
      # Other database settings...

  # Base URLs for services
  baseUrls:
    serviceA: https://api-gateway.servicea.preprod.example.com
    serviceB: https://api-gateway.serviceb.preprod.example.com

# Production configuration
prod:
  # Similar structure with production values
  # ...
```

### Configuration Management

The framework provides several classes for configuration management:

- `ConfigLoader`: Loads raw configuration from YAML files
- `ConfigRegistry`: Central registry for all configurations with caching
- `DatabaseConfigLoader`: Specialized loader for database configurations
- `MongoDBConfigLoader`: Specialized loader for MongoDB configurations
- `RedisConfigLoader`: Specialized loader for Redis configurations
- `ElasticsearchConfigLoader`: Specialized loader for Elasticsearch configurations

### Usage Example

```java
// Get a database configuration
DatabaseConfig dbConfig = ConfigRegistry.getInstance().getDatabaseConfig("default");

// Get a base URL
String serviceUrl = ConfigRegistry.getInstance().getBaseUrl("serviceA");

// Refresh configurations (e.g., after environment change)
ConfigRegistry.getInstance().refresh();
```

## Database Connectivity

The framework provides robust database connectivity with support for multiple database types. All database connections can be secured through SSH tunneling when accessing databases in protected environments.

### Supported Database Types

- **MySQL**: For relational database access
  - Connection pooling with HikariCP
  - Type-safe query execution
  - Parameterized queries to prevent SQL injection
  - Transaction support

- **MongoDB**: For NoSQL document database access
  - Connection pooling
  - CRUD operations with type mapping
  - Query builders and filters
  - Aggregation pipeline support

- **Redis**: For caching and key-value storage
  - Connection pooling
  - Support for all Redis data structures (strings, lists, sets, hashes, etc.)
  - Pub/Sub functionality
  - Lua scripting support

- **Elasticsearch**: For search and analytics
  - High-level REST client
  - Index management
  - Document CRUD operations
  - Search and aggregation queries

### SSH Tunneling

All database connections support SSH tunneling for secure access to databases in protected environments:

```yaml
# SSH Tunneling configuration in config.yml
sshConfig:
  hostname: ssh-bastion.example.com
  port: 22
  username: tunnel_user
  password: tunnel_password  # Or use privateKeyPath
  privateKeyPath: /path/to/private/key
  enabled: true  # Enable/disable tunneling
```

### Usage Examples

#### MySQL Example

```java
// Get a connection from the pool
Connection connection = DatabaseConnectionManager.getConnection("userdb");

// Execute a query with parameters
List<User> users = QueryExecutor.executeQuery(
    "userdb",
    "SELECT * FROM users WHERE status = ? AND created_at > ?",
    User.MAPPER,
    "active",
    yesterday
);

// Execute an update
int rowsAffected = QueryExecutor.executeUpdate(
    "userdb",
    "UPDATE users SET status = ? WHERE id = ?",
    "inactive",
    userId
);
```

#### MongoDB Example

```java
// Get a MongoDB database
MongoDatabase db = MongoDBConnectionManager.getMongoDatabase("productdb");

// Find documents with filters
List<Document> products = MongoDBQueryExecutor.find(
    "productdb",
    "products",
    Filters.and(
        Filters.eq("category", "electronics"),
        Filters.gt("price", 100)
    )
);
```

## Logging

The framework implements a comprehensive logging system using SLF4J with Logback as the implementation. Logs are organized by module and stored in the `logs` directory.

### Log File Structure

- `logs/automation.log`: Main log file containing all logs
- `logs/http-requests.log`: Dedicated log file for HTTP request/response details
- `logs/modules/`: Directory containing module-specific log files:
  - `logs/modules/commons.log`: Commons module logs
  - `logs/modules/juno.log`: Juno module logs
  - `logs/modules/scm.log`: SCM module logs
  - `logs/modules/nexs.log`: NEXS module logs
  - `logs/modules/cse.log`: CSE module logs
  - `logs/modules/pos.log`: POS module logs
  - `logs/modules/e2e.log`: E2E module logs
  - `logs/modules/example.log`: Example module logs
- `logs/test-report.html`: HTML-formatted log report

### Logging Configuration

Logging is configured in `commons/src/main/resources/logback.xml` with the following features:

- **Log Levels**: Different log levels (DEBUG, INFO, WARN, ERROR) for different components
- **Log Rotation**: Daily log rotation with compression
- **Size Limits**: Maximum log file sizes and history to prevent disk space issues
- **Formatted Output**: Consistent timestamp and thread information
- **Console Output**: Real-time logs to console during test execution

### Usage Example

```java
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MyClass {
    public void myMethod() {
        log.debug("Debug message with details: {}", someVariable);
        log.info("Information message");
        log.warn("Warning message");
        log.error("Error message", exception);
    }
}
```

## Reporting

The framework generates comprehensive test reports in multiple formats to provide detailed insights into test execution.

### Allure Reports

Allure provides detailed test execution reports with steps, attachments, and statistics:

- **Test Execution Timeline**: Visual representation of test execution
- **Categorized Tests**: Tests grouped by features, stories, and severity
- **Detailed Steps**: Step-by-step execution details
- **Attachments**: Screenshots, logs, and other artifacts
- **Environment Information**: Test environment details
- **History Trends**: Test execution trends over time

To generate Allure reports after test execution:

```bash
mvn allure:report
```

To view the Allure report in a browser:

```bash
mvn allure:serve
```

Allure report files are generated in the `allure-results` directory and the HTML report is created in the `allure-report` directory.

### Extent Reports

Extent Reports provide visually appealing HTML reports with charts and graphs:

- **Dashboard**: Overview of test execution with charts
- **Test Details**: Detailed information about each test
- **Tags and Categories**: Tests organized by tags and categories
- **Screenshots**: Visual evidence of test execution
- **System Information**: Environment and system details

Extent Reports are automatically generated during test execution and stored in the `test-output/extent-reports` directory.

### TestNG Reports

TestNG generates its own HTML reports with test execution details:

- **Suite Summary**: Overview of test suite execution
- **Test Methods**: Details of each test method
- **Chronological View**: Tests in execution order
- **Reporter Output**: Custom output from tests

TestNG reports are generated in the `test-output` directory.

## CI/CD Integration

The repository includes a Jenkins pipeline configuration in the `Jenkinsfile` that enables automated test execution as part of the CI/CD process.

### Pipeline Stages

1. **Checkout**: Cleans the workspace and checks out the code from Git
2. **Build**: Builds the project without running tests (`mvn clean package -DskipTests`)
3. **Test**: Runs tests based on selected parameters

### Pipeline Parameters

The pipeline is parameterized to allow flexible test execution:

- **ENVIRONMENT**: Environment to run tests against
  - `preprod`: Pre-production environment
  - `prod`: Production environment

- **SUITE_TYPE**: Test suite to run
  - `juno`: Juno service tests
  - `scm`: SCM service tests
  - `nexs`: NEXS service tests
  - `cs`: Customer Service tests
  - `e2e`: End-to-end tests
  - `pos`: POS service tests
  - `all`: All test suites

- **TEST_CATEGORY**: Test category to run
  - `SANITY`: Basic smoke tests
  - `REGRESSION`: Comprehensive regression tests
  - `E2E`: End-to-end tests
  - `ALL`: All test categories

### Pipeline Artifacts

The pipeline archives the following artifacts:

- **Test Results**: JUnit XML reports
- **Allure Results**: Raw Allure report data
- **Test Output**: Generated test output files
- **Logs**: Application and test logs

### Pipeline Notifications

The pipeline sends email notifications with build status and details to:

- Developers who made changes
- Test requesters

### Example Pipeline Execution

```bash
# Run sanity tests for the Juno module in preprod environment
java -jar jenkins-cli.jar -s http://jenkins-server build be-automation \
    -p ENVIRONMENT=preprod \
    -p SUITE_TYPE=juno \
    -p TEST_CATEGORY=SANITY
```

## Test Development

### Creating a New Test

To create a new test, follow these steps:

1. **Create a Test Context Class**:

```java
@Builder
@Data
public class MyTestContext {
    private String param1;
    private String param2;
    @Builder.Default
    private int statusCode = 200;
}
```

2. **Create a Request Builder**:

```java
@Builder
public class MyRequestBuilder implements IRequestBuilder<MyRequest> {
    private MyTestContext testContext;

    @Override
    public MyRequest init() {
        return MyRequest.builder()
                .param1(testContext.getParam1())
                .param2(testContext.getParam2())
                .build();
    }
}
```

3. **Create a Service Helper**:

```java
@SuperBuilder
public class MyServiceHelper extends BaseHelper<MyExceptionState, MyRequest> implements ServiceHelper {
    private MyTestContext testContext;
    private MyResponse response;
    private MyServiceAPI client;

    @Override
    public ServiceHelper init() {
        // Initialize the test
        statusCode = testContext.getStatusCode();
        requestBuilders = MyRequestBuilder.builder()
                .testContext(testContext)
                .build()
                .init();
        client = new MyServiceAPI();
        return this;
    }

    @Override
    public ServiceHelper process() {
        // Execute the API call
        response = parseResponse(client.callApi(requestBuilders, headers), MyResponse.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        // Validate the response
        MyValidator validator = MyValidator.builder()
                .request(requestBuilders)
                .response(response)
                .testContext(testContext)
                .build();
        validator.validateNode();
        return this;
    }

    @Override
    public ServiceHelper test() {
        // Orchestrate the test flow
        init();
        process();
        validate();
        return this;
    }
}
```

4. **Create a Validator**:

```java
@Builder
public class MyValidator implements IValidator {
    private MyTestContext testContext;
    private MyRequest request;
    private MyResponse response;
    private static SoftAssert softAssert = new SoftAssert();

    @Override
    public void validateNode() {
        softAssert.assertNotNull(response, "Response should not be null");
        // Add more validations
        softAssert.assertAll();
    }

    @Override
    public void validateDBEntities() {
        // Validate database state if needed
    }
}
```

5. **Create a Test Class**:

```java
@TestCategory(TestCategory.Category.SANITY)
public class MyTest {
    @Test(dataProvider = "myDataProvider")
    public void testMyFeature(String param1, String param2) {
        MyServiceHelper.builder()
                .testContext(MyTestContext.builder()
                        .param1(param1)
                        .param2(param2)
                        .build())
                .build()
                .test();
    }

    @DataProvider(name = "myDataProvider")
    public Object[][] myDataProvider() {
        return new Object[][] {
            {"value1", "value2"},
            {"value3", "value4"}
        };
    }
}
```

### Best Practices

1. **Use Builder Pattern**: Use Lombok's `@Builder` and `@SuperBuilder` for creating objects
2. **Separate Concerns**: Keep request building, API calls, and validation separate
3. **Use Data Providers**: Parameterize tests with TestNG data providers
4. **Add Test Categories**: Categorize tests as SANITY, REGRESSION, or E2E
5. **Implement Soft Assertions**: Use TestNG's SoftAssert for multiple validations
6. **Document Tests**: Add clear comments and documentation to tests
7. **Follow Naming Conventions**: Use consistent naming for test classes and methods

## Contributing

1. Create a feature branch from `main`
2. Make your changes
3. Add tests for your changes
4. Ensure all tests pass
5. Submit a pull request

## License

Proprietary - Lenskart

## Contact

For questions or support, contact the QA Automation team.
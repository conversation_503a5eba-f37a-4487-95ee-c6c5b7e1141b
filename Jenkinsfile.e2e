@Library('jenkins/shared-functions.groovy') _

// E2E Module Jenkins Pipeline
// This pipeline is specifically configured for the e2e module

def sharedFunctions

node {
    // Load shared functions
    sharedFunctions = load 'jenkins/shared-functions.groovy'
}

// Module-specific configuration
def moduleConfig = [
    moduleName: 'e2e',
    suiteXmlFile: 'src/test/resources/testng.xml',
    defaultEnvironment: 'preprod',
    defaultTestCategory: 'SANITY'
]

// Execute the pipeline using shared functions
sharedFunctions.executeModulePipeline(moduleConfig)

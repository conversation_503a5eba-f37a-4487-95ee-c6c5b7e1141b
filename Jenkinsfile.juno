@Library('jenkins/shared-functions.groovy') _

// Juno Module Jenkins Pipeline
// This pipeline is specifically configured for the juno module

def sharedFunctions

node {
    // Load shared functions
    sharedFunctions = load 'jenkins/shared-functions.groovy'
}

// Module-specific configuration
def moduleConfig = [
    moduleName: 'juno',
    suiteXmlFile: 'src/test/resources/juno-regression.xml',
    defaultEnvironment: 'preprod',
    defaultTestCategory: 'REGRESSION'
]

// Execute the pipeline using shared functions
sharedFunctions.executeModulePipeline(moduleConfig)

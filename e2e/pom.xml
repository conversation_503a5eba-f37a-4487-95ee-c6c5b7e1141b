<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.lenskart</groupId>
        <artifactId>be-automation</artifactId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>e2e</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <commons.version>1.0-SNAPSHOT</commons.version>
        <juno.version>1.0-SNAPSHOT</juno.version>
        <pos.version>1.0-SNAPSHOT</pos.version>
        <nexs.version>1.0-SNAPSHOT</nexs.version>
        <lombok.version>1.18.30</lombok.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>commons</artifactId>
            <version>${commons.version}</version>
        </dependency>
        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>juno</artifactId>
            <version>${juno.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>pos</artifactId>
            <version>${pos.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>nexs</artifactId>
            <version>${nexs.version}</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <!-- Lombok Annotation Processor -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.13.0</version>
                <configuration>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
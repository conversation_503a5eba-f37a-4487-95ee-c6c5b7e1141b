<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.lenskart</groupId>
        <artifactId>be-automation</artifactId>
        <version>1.0.0</version>
    </parent>

    <artifactId>e2e</artifactId>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <commons.version>1.0.0</commons.version>
        <juno.version>1.0.0</juno.version>
        <pos.version>1.0.0</pos.version>
        <nexs.version>1.0.0</nexs.version>
        <cs.version>1.0.0</cs.version>
        <cosmos.version>1.0.0</cosmos.version>
        <lombok.version>1.18.30</lombok.version>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>commons</artifactId>
            <version>${commons.version}</version>
        </dependency>
        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>juno</artifactId>
            <version>${juno.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>pos</artifactId>
            <version>${pos.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>nexs</artifactId>
            <version>${nexs.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>cs</artifactId>
            <version>${cs.version}</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.lenskart</groupId>
            <artifactId>cosmos</artifactId>
            <version>${cosmos.version}</version>
            <scope>test</scope>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <!-- Lombok Annotation Processor -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.13.0</version>
                <configuration>
                    <release>21</release>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
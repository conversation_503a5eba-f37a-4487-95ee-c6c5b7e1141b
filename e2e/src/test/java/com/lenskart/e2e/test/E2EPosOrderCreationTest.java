package com.lenskart.e2e.test;

import com.lenskart.commons.model.OrderContext;
import com.lenskart.e2e.dataprovider.E2EDataProvider;
import com.lenskart.e2e.helper.E2EHelpers;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import static com.lenskart.commons.utils.JsonUtils.convertObjectToJsonString;

@Slf4j
public class E2EPosOrderCreationTest {


    @Test(dataProviderClass = E2EDataProvider.class, dataProvider = "posOrderContext")
    public void createPosOrder(OrderContext orderContext) {

        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        E2EHelpers.builder()
                .orderContext(orderContext)
                .build()
                .test();

    }
}

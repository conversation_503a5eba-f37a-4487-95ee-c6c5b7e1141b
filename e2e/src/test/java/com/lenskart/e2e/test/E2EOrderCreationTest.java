package com.lenskart.e2e.test;

import com.lenskart.commons.model.OrderContext;
import com.lenskart.e2e.helper.E2EHelpers;
import com.lenskart.e2e.dataprovider.E2EDataProvider;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import static com.lenskart.commons.utils.JsonUtils.convertObjectToJsonString;

@Slf4j
public class E2EOrderCreationTest {


    @Test(dataProviderClass = E2EDataProvider.class, dataProvider = "orderContext")
    public void createOrder(OrderContext orderContext) {

        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        E2EHelpers.builder()
                .orderContext(orderContext)
                .build()
                .test();

    }


//    @Test(enabled = false, dataProviderClass = E2EDataProvider.class, dataProvider = "orderContext")
//    public void getData(OrderContext orderContext) {
//
//        List<Map<String, Object>> rows = QueryExecutor
//                .executeQueryAsMap("tracking_middleware", "SELECT * FROM tracking_mapping where id=?", 1);
//
//        System.out.println("rows are " + rows);
//    }


}

package com.lenskart.e2e.test;

import com.lenskart.commons.database.mysql.MySQLQueryExecutor;
import com.lenskart.commons.model.Client;
import com.lenskart.commons.model.Countries;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.cs.helpers.CsReturnHelper;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.e2e.helper.E2EHelpers;
import com.lenskart.e2e.dataprovider.E2EDataProvider;
import com.lenskart.commons.model.*;
import com.lenskart.nexs.helpers.NexsOrderStateHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import java.util.List;
import java.util.Map;

import static com.lenskart.commons.utils.JsonUtils.convertObjectToJsonString;

@Slf4j
public class E2EOrderCreationTest {


    @Test(dataProviderClass = E2EDataProvider.class, dataProvider = "orderContext")
    public void createOrder(OrderContext orderContext) {

        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        E2EHelpers.builder()
                .orderContext(orderContext)
                .build()
                .test();

    }


    @Test(dataProviderClass = E2EDataProvider.class, dataProvider = "returnContext")
    public void returnTest(OrderContext orderContext, CsOrderContext csOrderContext) {

        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        E2EHelpers.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();

    }




    @Test(enabled = false, dataProviderClass = E2EDataProvider.class, dataProvider = "orderContext")
    public void getData(OrderContext orderContext) {

        List<Map<String, Object>> rows = MySQLQueryExecutor
                .executeQuery(MySQLCluster.NEXS_CLUSTER.getClusterName(), "wms",
                        "SELECT * FROM tracking_mapping where id=?", 1);
        log.info("Rows: {}", rows);

    }


    @Test(enabled = false)
    public void createNexsOrder( ) {

        OrderContext orderContext = OrderContext.builder()
                .phoneNumber("**********")
                .productLists(List.of(
                        OrderContext.ProductList.builder()
                                .productId("148248")
//                                .powerType(ZERO_POWER)
                                .finalState(NexsOrderState.DISPATCHED)
                                .build(),
                        OrderContext.ProductList.builder()
                                .productId("131932")
//                                .powerType(ZERO_POWER)
                                .finalState(NexsOrderState.DISPATCHED)
                                .build()
                        ))
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN)
                        .pinCode("121004")
                        .build())
                .paymentMethod(PaymentMethod.COD)
                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                .build();


        NexsOrderContext nexsOrderContext = NexsOrderContext.builder().headers(NexsOrderContext.Headers.builder()
                        .build())
                .incrementId("1930750660")
                .build();

        NexsOrderStateHelper stateManager = NexsOrderStateHelper.builder()
                .orderContext(orderContext)
                .nexsOrderContext(nexsOrderContext)
                .sequentialTransition(true)
                .build();

        stateManager.test();
    }

    @Test(enabled = true)
    public void createNexsOrderCS( ) {

        OrderContext orderContext = OrderContext.builder()
                .phoneNumber("**********")
                .productLists(List.of(
                        OrderContext.ProductList.builder()
                                .productId("148248")
                                .finalState(NexsOrderState.DISPATCHED)
                                .facilityCode("QNXS2")
                                .shippingPackageId("SQNXS226000004433154")
                                .itemId(279151)
                                .build()))
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN)
                        .pinCode("121004")
                        .build())
                .paymentMethod(PaymentMethod.COD)
                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                .orderId(1930754979)
                .build();
        CsReturnHelper.builder()
                .orderContext(orderContext)
                .csOrderContext(CsOrderContext.builder().productIDToBeReturned("148248").build())
                .build()
                .test();


    }

    @Test(enabled = true)
    public void testCosmos() {

        OrderContext orderContext = OrderContext.builder()
                .phoneNumber("**********")
                .productLists(List.of(
                        OrderContext.ProductList.builder()
                                .productId("148248")
                                .finalState(NexsOrderState.IN_QC)
                                .build()))
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN)
                        .pinCode("121004")
                        .build())
                .paymentMethod(PaymentMethod.COD)
                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                .orderId(1930753219)
                .build();

//        CosmosOrderDetailsHelper.builder()
//                .orderContext(orderContext)
//                .build()
//                .test();

    }





}


package com.lenskart.e2e.test;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lenskart.commons.database.mysql.QueryExecutor;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.e2e.helper.E2EHelpers;
import com.lenskart.e2e.dataprovider.E2EDataProvider;
import com.lenskart.juno.helpers.CreateCartHelper;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.lenskart.commons.utils.JsonUtils.printJson;

@Slf4j
public class E2EOrderCreationTest {


    @Test(dataProviderClass = E2EDataProvider.class, dataProvider = "orderContext")
    public void getDiscoveryTest(OrderContext orderContext) throws JsonProcessingException {

        log.info("Printing order context: {}", printJson(orderContext));

        E2EHelpers.builder()
                .orderContext(orderContext)
                .build()
                .test();

    }


//    @Test(enabled = false, dataProviderClass = E2EDataProvider.class, dataProvider = "orderContext")
//    public void getData(OrderContext orderContext) {
//
//        List<Map<String, Object>> rows = QueryExecutor
//                .executeQueryAsMap("tracking_middleware", "SELECT * FROM tracking_mapping where id=?", 1);
//
//        System.out.println("rows are " + rows);
//    }


}

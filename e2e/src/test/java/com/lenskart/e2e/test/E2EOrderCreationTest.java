package com.lenskart.e2e.test;

import com.lenskart.commons.database.mysql.DynamicQueryExecutor;
import com.lenskart.commons.model.Client;
import com.lenskart.commons.model.Countries;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.e2e.helper.E2EHelpers;
import com.lenskart.e2e.dataprovider.E2EDataProvider;
import com.lenskart.commons.model.*;
import com.lenskart.nexs.helpers.NexsOrderStateHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import java.util.List;
import java.util.Map;

import static com.lenskart.commons.model.PowerTypes.ZERO_POWER;
import static com.lenskart.commons.utils.JsonUtils.convertObjectToJsonString;

@Slf4j
public class E2EOrderCreationTest {


    @Test(dataProviderClass = E2EDataProvider.class, dataProvider = "orderContext")
    public void createOrder(OrderContext orderContext) {

        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        E2EHelpers.builder()
                .orderContext(orderContext)
                .build()
                .test();

    }


    @Test(enabled = true, dataProviderClass = E2EDataProvider.class, dataProvider = "orderContext")
    public void getData(OrderContext orderContext) {

        List<Map<String, Object>> rows = DynamicQueryExecutor
                .executeQuery("nexs_cluster", "wms",  "SELECT * FROM tracking_mapping where id=?", 1);

        log.info("Rows: {}", rows);

    }


    @Test(enabled = false)
    public void createNexusOrder( ) {

        OrderContext orderContext = OrderContext.builder()
                .phoneNumber("**********")
                .productLists(List.of(
                        OrderContext.ProductList.builder()
                                .productId("148248")
                                .powerType(ZERO_POWER)
                                .finalState(NexsOrderState.DISPATCHED)
                                .build()))
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN)
                        .pinCode("121004")
                        .build())
                .paymentMethod(PaymentMethod.COD)
                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                .build();


        NexsOrderContext nexsOrderContext = NexsOrderContext.builder().headers(NexsOrderContext.Headers.builder()
                        .build())
                .incrementId("1930752835")
                .build();

        NexsOrderStateHelper stateManager = NexsOrderStateHelper.builder()
                .orderContext(orderContext)
                .nexsOrderContext(nexsOrderContext)
                .sequentialTransition(true)
                .build();

        stateManager.test();
    }


}


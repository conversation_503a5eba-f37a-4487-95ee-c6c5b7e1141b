package com.lenskart.e2e.test;

import com.lenskart.commons.model.*;
import com.lenskart.juno.helpers.JunoOrderCreationHelper;
import org.testng.annotations.Test;

import java.util.List;

import static com.lenskart.commons.model.PowerTypes.ZERO_POWER;

public class JunoOrderPlacementTest {

    @Test
    public void junoOrderPlacement() {
        OrderContext  orderContext = OrderContext.builder()
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder().country(Countries.IN).pinCode("121004").build())
                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                .phoneNumber("2035164014")
                .productLists(List.of(
                        OrderContext.ProductList.builder().productId("148248").powerType(ZERO_POWER).finalState(NexsOrderState.DISPATCHED).build()))
                .paymentMethod(PaymentMethod.COD)
                .build();

        JunoOrderCreationHelper orderCreationHelper = JunoOrderCreationHelper
                .builder()
                .orderContext(orderContext)
                .build();
        orderCreationHelper.test();
    }
}

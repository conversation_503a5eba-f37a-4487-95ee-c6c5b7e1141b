package com.lenskart.e2e.test.cs;

import com.lenskart.commons.model.OrderContext;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.e2e.dataprovider.E2EDataProvider;
import com.lenskart.e2e.helper.cs.CsCancellationE2EHelpers;
import com.lenskart.e2e.helper.cs.CsRTOE2EHelpers;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import static com.lenskart.commons.utils.JsonUtils.convertObjectToJsonString;

@Slf4j
public class CsCancellationTest {


    @Test(dataProviderClass = E2EDataProvider.class, dataProvider = "cancellationContext")
    public void verifyRTOOrder(OrderContext orderContext, CsOrderContext csOrderContext) {

        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsCancellationE2EHelpers.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();

    }
}


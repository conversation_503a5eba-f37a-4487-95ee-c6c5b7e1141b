package com.lenskart.e2e.test.cs;

import com.lenskart.commons.model.*;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.e2e.dataprovider.E2EDataProvider;
import com.lenskart.e2e.helper.cs.CsReturnE2EHelpers;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import static com.lenskart.commons.utils.JsonUtils.convertObjectToJsonString;

@Slf4j
public class CsReturnTest {


    @Test(dataProviderClass = E2EDataProvider.class, dataProvider = "returnContext")
    public void verifyReturnOrder(OrderContext orderContext, CsOrderContext csOrderContext) {

        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsReturnE2EHelpers.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();

    }
}


package com.lenskart.e2e.test;

import com.lenskart.nexs.helpers.NexsOrderCompletionHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import org.testng.annotations.Test;

public class NexSOrderCompletionTest {

    @Test
    public void testOrderCompletion() {
        // Create order context with necessary data
        NexsOrderContext orderContext = NexsOrderContext.builder()
                .shippingId("SNXS2260000004438033")
           //  .incrementId("1930751544")
                .headers(NexsOrderContext.Headers.builder()
                        .build())
                .build();

        // Execute the order completion flow
        NexsOrderCompletionHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }
}
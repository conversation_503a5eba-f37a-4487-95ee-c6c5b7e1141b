package com.lenskart.e2e.test;

import com.lenskart.commons.database.mysql.QueryExecutor;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.e2e.dataprovider.E2EDataProvider;
import com.lenskart.nexs.helpers.NexsOrderCompletionHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import org.awaitility.Awaitility;
import org.testng.annotations.Test;

import java.util.List;
import java.util.Map;

import static com.lenskart.commons.utils.AwaitUtils.DEFAULT_POLL_INTERVAL;
import static com.lenskart.commons.utils.AwaitUtils.SHORT_TIMEOUT;

public class NexSOrderCompletionTest {
    NexsOrderContext nexsOrderContext;
    @Test(enabled = true)
    public void testOrderCompletion() {
        // Create order context with necessary data
        nexsOrderContext = NexsOrderContext.builder()
                .shippingId("SNXS2260000004438906")
           //  .incrementId("**********")
                .headers(NexsOrderContext.Headers.builder()
                        .build())
                .build();

        // Execute the order completion flow
        NexsOrderCompletionHelper.builder()
                .nexsOrderContext(nexsOrderContext)
                .build()
                .test();
    }

    @Test(enabled = false)
    public void getData() {

        List<Map<String, Object>> rows = QueryExecutor
                .executeQueryAsMap("wms", "select `status` from wms.order_items where shipping_package_id=?", "123");


    }
}
package com.lenskart.e2e.test;

import com.lenskart.nexs.helpers.NexsOrderCompletionHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import org.testng.annotations.Test;

public class NexSOrderCompletionTest {
    NexsOrderContext nexsOrderContext;
    @Test(enabled = true)
    public void testOrderCompletion() {
        // Create order context with necessary data
        nexsOrderContext = NexsOrderContext.builder()
           //     .shippingId("SNXS2260000004438906")
             .incrementId("1930753269")
                .headers(NexsOrderContext.Headers.builder()
                        .build())
                .build();

        // Execute the order completion flow
        NexsOrderCompletionHelper.builder()
                .nexsOrderContext(nexsOrderContext)
                .build()
                .test();
    }

    @Test(enabled = false)
    public void getData() {

//        List<Map<String, Object>> rows = QueryExecutor
//                .executeQueryAsMap("wms", "select `status` from wms.order_items where shipping_package_id=?", "123");


    }
}
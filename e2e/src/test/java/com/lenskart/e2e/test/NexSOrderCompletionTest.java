package com.lenskart.e2e.test;

import com.lenskart.nexs.helpers.NexsOrderCompletionHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import org.testng.annotations.Test;

public class NexSOrderCompletionTest {
    @Test(enabled = true)
    public void testOrderCompletion() {
        // Create order context with necessary data
        NexsOrderContext  nexsOrderContext = NexsOrderContext
                .builder()
                .shippingId("SNXS2260000004443208")
           //  .incrementId("1930753269")
                .headers(NexsOrderContext.Headers.builder()
                        .build())
                .build();

        // Execute the order completion flow
        NexsOrderCompletionHelper.builder()
                .nexsOrderContext(nexsOrderContext)
                .build()
                .test();
    }
}
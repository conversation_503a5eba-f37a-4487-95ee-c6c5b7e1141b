package com.lenskart.e2e.pos;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.e2e.dataprovider.E2EDataProvider;
import com.lenskart.e2e.dataprovider.PosE2EDataProvider;
import com.lenskart.e2e.helper.pos.PosE2EHelper;
import com.lenskart.pos.helpers.PosMonthlyMarginHelper;
import com.lenskart.pos.helpers.ProcessStoreCommissionPayoutHelper;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import static com.lenskart.commons.utils.JsonUtils.convertObjectToJsonString;


@Slf4j
@TestCategory(TestCategory.Category.SANITY)
public class E2EPosOrderCreationTest {


    @Test(dataProviderClass = PosE2EDataProvider.class, dataProvider = "posOrderContext")
    public void createPosOrder(OrderContext orderContext) {

        PosE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

    }
    @Test(dataProviderClass = E2EDataProvider.class, dataProvider = "orderContext")
    public void TestNavCommission(OrderContext orderContext) {
        ProcessStoreCommissionPayoutHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

    }

    @Test(dataProviderClass = E2EDataProvider.class, dataProvider = "orderContext")
    public void TestMonthlyMargin(OrderContext orderContext) {
        PosMonthlyMarginHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

    }

}

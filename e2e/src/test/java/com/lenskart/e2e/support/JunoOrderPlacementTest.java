package com.lenskart.e2e.support;

import com.lenskart.commons.model.*;
import com.lenskart.juno.helpers.JunoOrderCreationHelper;
import org.testng.annotations.Test;

import java.util.List;

import static com.lenskart.commons.model.PowerTypes.ZERO_POWER;

public class JunoOrderPlacementTest {

    @Test
    public void junoOrderPlacement() {
        OrderContext orderContext = OrderContext.builder()
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder().country(Countries.IN).pinCode("121004").build())
                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                .phoneNumber("2035164014")
                .productLists(List.of(
                        OrderContext.ProductList.builder().productId("148248").powerType(ZERO_POWER).build()))
                .paymentMethod(PaymentMethod.COD)
                .build();

        JunoOrderCreationHelper orderCreationHelper = JunoOrderCreationHelper
                .builder()
                .orderContext(orderContext)
                .build();
        orderCreationHelper.test();
    }

    @Test
    public void junoOrderPlacementWithDefaultValues() {
        // Default values are taken from Countries in Commons Model
        OrderContext  orderContext = OrderContext.builder()
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder().country(Countries.SA).build())
                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                .productLists(List.of(
                        OrderContext.ProductList.builder().productId("151243").powerType(ZERO_POWER).build()))
                .build();
        JunoOrderCreationHelper orderCreationHelper = JunoOrderCreationHelper
                .builder()
                .orderContext(orderContext)
                .build();
        orderCreationHelper.test();
    }
}

package com.lenskart.e2e.nexs;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.e2e.dataprovider.NexsE2EDataProvider;
import com.lenskart.e2e.helper.nexs.NexsE2EHelper;
import com.lenskart.nexs.database.WMSDbUtils;
import com.lenskart.scm.util.OrderAdopterUtil;
import org.testng.Assert;
import org.testng.annotations.Test;

import java.util.List;

@TestCategory(TestCategory.Category.SANITY)
public class PickingPriorityTests {

    @Test(dataProviderClass = NexsE2EDataProvider.class, dataProvider = "NDDPincode")
    public void verifyPickingPriorityForNDDPincode(OrderContext orderContext) {
        NexsE2EHelper nexsE2EHelper =  NexsE2EHelper.builder()
                .orderContext(orderContext)
                .build();
        nexsE2EHelper.test();
        String orderPriority = WMSDbUtils.getOrderItemHeaderDetails(nexsE2EHelper.getNexsOrderContext()).getFirst().get("order_priority").toString();
        Assert.assertEquals(orderPriority, "1");
    }

    @Test(dataProviderClass = NexsE2EDataProvider.class, dataProvider = "NonNDDPincode")
    public void verifyPickingPriorityForNonNDDPincode(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        NexsE2EHelper nexsE2EHelper =  NexsE2EHelper.builder()
                .orderContext(orderContext)
                .build();
        orderContext.setProductLists(productLists);
        nexsE2EHelper.test();
        String orderPriority = WMSDbUtils.getOrderItemHeaderDetails(nexsE2EHelper.getNexsOrderContext()).getFirst().get("order_priority").toString();
        Assert.assertEquals(orderPriority, "10");
    }


    @Test
    public void hhhh() {
        OrderAdopterUtil.syncOrder("**********");
    }
}

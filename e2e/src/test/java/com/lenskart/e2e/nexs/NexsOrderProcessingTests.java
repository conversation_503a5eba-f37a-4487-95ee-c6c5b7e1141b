package com.lenskart.e2e.nexs;

import com.lenskart.commons.model.OrderContext;
import com.lenskart.e2e.dataprovider.NexsE2EDataProvider;
import com.lenskart.e2e.helper.E2EHelpers;
import com.lenskart.e2e.helper.nexs.NexsE2EHelper;
import org.testng.annotations.Test;

import static com.lenskart.commons.utils.JsonUtils.convertObjectToJsonString;

public class NexsOrderProcessingTests {

    @Test(dataProviderClass = NexsE2EDataProvider.class, dataProvider = "orderContext")
    public void NexsOrderCompletion(OrderContext orderContext) {
        NexsE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }
}

package com.lenskart.e2e.nexs;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.*;
import com.lenskart.cs.model.CsOrderContext;

import com.lenskart.e2e.dataprovider.NexsE2EDataProvider;
import com.lenskart.e2e.helper.cs.CsCancellationE2EHelpers;
import org.testng.annotations.Test;

@TestCategory(TestCategory.Category.SANITY)
public class NexsCancellationTest {

    @Test(dataProviderClass = NexsE2EDataProvider.class, dataProvider = "nexsCancellationContext")
    public void cancelTheOrderAndVerifyTheStatusInNexs(OrderContext orderContext, CsOrderContext csOrderContext) {
        CsCancellationE2EHelpers.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();

    }
}

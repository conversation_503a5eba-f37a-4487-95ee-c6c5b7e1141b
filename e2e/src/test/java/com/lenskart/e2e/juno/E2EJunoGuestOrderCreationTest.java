package com.lenskart.e2e.juno;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.e2e.dataprovider.JunoE2EDataProvider;
import com.lenskart.e2e.helper.juno.JunoE2EGuestHelper;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import java.util.List;

@Slf4j
@TestCategory(TestCategory.Category.E2E)
public class E2EJunoGuestOrderCreationTest {

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithPower")
    public void sunglassWithPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EGuestHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithoutPowerAndGold")
    public void sunglassWithoutPowerAndGold(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EGuestHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithPowerAndGold")
    public void sunglassWithPowerAndGold(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EGuestHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassFrameOnly")
    public void eyeglassFrameOnly(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EGuestHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassAndContactLensWithoutPower")
    public void eyeglassAndContactLensWithoutPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EGuestHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassAndSunglassWithoutPower")
    public void eyeglassAndSunglassWithoutPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EGuestHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassFrameOnlyWithContactLens")
    public void eyeglassFrameOnlyWithContactLens(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EGuestHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithoutPowerAndContactLens")
    public void sunglassWithoutPowerAndContactLens(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EGuestHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassZeroPowerWithSunglassWithPower")
    public void eyeglassZeroPowerWithSunglassWithPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EGuestHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassAndSunglassWithoutPower")
    public void eyeglassWithPowerAndSunglassWithPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EGuestHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassWithoutPower")
    public void eyeglassWithoutPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EGuestHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassZeroPower")
    public void eyeglassZeroPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EGuestHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }
}



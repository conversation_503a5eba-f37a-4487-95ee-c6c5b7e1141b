package com.lenskart.e2e.juno;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.e2e.dataprovider.JunoE2EDataProvider;
import com.lenskart.e2e.helper.juno.JunoE2EHelper;
import com.lenskart.juno.helpers.JunoCartUpdatePrescriptionHelper;
import com.lenskart.juno.helpers.JunoOrderUpdatePrescriptionHelper;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import java.util.List;

@Slf4j
@TestCategory(TestCategory.Category.E2E)
public class E2EJunoOrderCreationTest {

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassSingleVisionPower")
    public void eyeglassSingleVisionPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassBifocalPower")
    public void eyeglassBifocalPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassZeroPower")
    public void eyeglassZeroPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassWithoutPower")
    public void eyeglassWithoutPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithoutPower")
    public void sunglassWithoutPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithPower")
    public void sunglassWithPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "goldPid")
    public void goldPid(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "frameOnly")
    public void frameOnly(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "contactLens")
    public void contactLens(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "contactLensZeroPower")
    public void contactLensZeroPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "accessories")
    public void accessories(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassWithoutPowerAndInsurance")
    public void eyeglassWithoutPowerAndInsurance(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithPowerAndInsurance")
    public void sunglassWithPowerAndInsurance(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithoutPowerAndInsurance")
    public void sunglassWithoutPowerAndInsurance(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeAndSunglassWithPowerAndGold")
    public void eyeAndSunglassWithPowerAndGold(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeWithoutPowerAndSunglassWithPowerAndGold")
    public void eyeWithoutPowerAndSunglassWithPowerAndGold(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassWithPowerAndInsurance")
    public void eyeglassWithPowerAndInsurance(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "updatePrescriptionforEyeglassSingeVision")
    public void updatePrescriptionForEyeGlassSingeVision(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoOrderUpdatePrescriptionHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "updatePrescriptionforEyeglassBifocal")
    public void updatePrescriptionForEyeGlassBifocal(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoOrderUpdatePrescriptionHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "cartupdatePrescriptionforEyeglassSingleVision")
    public void cartUpdatePrescriptionForEyeglassSingleVisionOrderCreation(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoCartUpdatePrescriptionHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "cartupdatePrescriptionforEyeglassBifocal")
    public void cartUpdatePrescriptionForEyeglassBifocalOrderCreation(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoCartUpdatePrescriptionHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "cartupdatePrescriptionforSunglass")
    public void cartUpdatePrescriptionForSunglassWithPowerOrderCreation(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoCartUpdatePrescriptionHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }
    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "cashbackCheckOnOrderPlacement")
    public void cashBackFlowOrderPlacement(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }
}


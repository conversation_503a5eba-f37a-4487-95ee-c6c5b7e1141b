package com.lenskart.e2e.juno;

import com.lenskart.commons.annotations.TestCategory;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.e2e.dataprovider.JunoE2EDataProvider;
import com.lenskart.e2e.helper.juno.JunoE2EDiscountHelper;
import com.lenskart.e2e.helper.juno.JunoE2EHelper;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import java.util.List;

@Slf4j
@TestCategory(TestCategory.Category.E2E)
public class E2EJunoOrderCreationTest {

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassSingleVisionPower")
    public void eyeglassSingleVisionPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassBifocalPower")
    public void eyeglassBifocalPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassZeroPower")
    public void eyeglassZeroPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassWithoutPower")
    public void eyeglassWithoutPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithoutPower")
    public void sunglassWithoutPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithPower")
    public void sunglassWithPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(enabled = false, dataProviderClass = JunoE2EDataProvider.class, dataProvider = "goldPid")
    public void goldPid(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "frameOnly")
    public void frameOnly(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "contactLens")
    public void contactLens(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "contactLensZeroPower")
    public void contactLensZeroPower(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "accessories")
    public void accessories(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassWithoutPowerAndInsurance")
    public void eyeglassWithoutPowerAndInsurance(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithPowerAndInsurance")
    public void sunglassWithPowerAndInsurance(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithoutPowerAndInsurance")
    public void sunglassWithoutPowerAndInsurance(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeAndSunglassWithPowerAndGold")
    public void eyeAndSunglassWithPowerAndGold(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeWithoutPowerAndSunglassWithPowerAndGold")
    public void eyeWithoutPowerAndSunglassWithPowerAndGold(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassWithPowerAndInsurance")
    public void eyeglassWithPowerAndInsurance(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "updatePrescriptionforEyeglassSingeVision")
    public void updatePrescriptionForEyeGlassSingeVision(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isGuestUser(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "updatePrescriptionforEyeglassBifocal")
    public void updatePrescriptionForEyeGlassBifocal(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isGuestUser(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "cartupdatePrescriptionforEyeglassSingleVision")
    public void cartUpdatePrescriptionForEyeglassSingleVisionOrderCreation(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "cartupdatePrescriptionforEyeglassBifocal")
    public void cartUpdatePrescriptionForEyeglassBifocalOrderCreation(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "cartupdatePrescriptionforSunglass")
    public void cartUpdatePrescriptionForSunglassWithPowerOrderCreation(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "cashbackCheckOnOrderPlacement")
    public void cashBackFlowOrderPlacement(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithPower")
    public void sunglassWithPowerGuest(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isGuestUser(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithoutPowerAndGold")
    public void sunglassWithoutPowerAndGold(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isGuestUser(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithPowerAndGold")
    public void sunglassWithPowerAndGold(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isGuestUser(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassFrameOnly")
    public void eyeglassFrameOnlyGuest(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isGuestUser(true)
                .build()
                .test();
    }



    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassAndSunglassWithoutPower")
    public void eyeglassAndSunglassWithoutPowerGuest(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isGuestUser(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassFrameOnlyWithContactLens")
    public void eyeglassFrameOnlyWithContactLensGuest(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isGuestUser(true)
                .build()
                .test();
    }


    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassZeroPowerWithSunglassWithPower")
    public void eyeglassZeroPowerWithSunglassWithPowerGuest(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isGuestUser(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassWithPowerAndSunglassWithPower")
    public void eyeglassWithPowerAndSunglassWithPowerGuest(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isGuestUser(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassWithoutPower")
    public void eyeglassWithoutPowerGuest(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isGuestUser(true)
                .build()
                .test();
    }

    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassZeroPower")
    public void eyeglassZeroPowerGuest(OrderContext orderContext, List<OrderContext.ProductList> productLists) {
        orderContext.setProductLists(productLists);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isGuestUser(true)
                .build()
                .test();
    }

    //GV Case
    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassSingleVisionPowerWithGV")
    public void eyeglassSingleVisionPowerWithGVOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.GiftVoucherList giftVoucherList) {
        orderContext.setProductLists(productLists);
        orderContext.setGiftVoucherList(giftVoucherList);
        JunoE2EHelper.builder()
                .orderContext(orderContext)
                .isGuestUser(true)
                .isPrescriptionRequired(true)
                .build()
                .test();
    }



    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "sunglassWithPowerWithGV")
    public void sunglassWithPowerWithGVOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.GiftVoucherList giftVoucherList) {
        orderContext.setProductLists(productLists);
        orderContext.setGiftVoucherList(giftVoucherList);
        JunoE2EDiscountHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }


    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "eyeglassAndSunglassWithGV")
    public void eyeglassAndSunglassWithGVOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.GiftVoucherList giftVoucherList) {
        orderContext.setProductLists(productLists);
        orderContext.setGiftVoucherList(giftVoucherList);
        JunoE2EDiscountHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
    }


    @Test(dataProviderClass = JunoE2EDataProvider.class, dataProvider = "multiProductWithGV")
    public void multiProductWithGVOrder(OrderContext orderContext, List<OrderContext.ProductList> productLists, OrderContext.GiftVoucherList giftVoucherList) {
        orderContext.setProductLists(productLists);
        orderContext.setGiftVoucherList(giftVoucherList);
        JunoE2EDiscountHelper.builder()
                .orderContext(orderContext)
                .isGuestUser(true)
                .build()
                .test();
    }
}


package com.lenskart.e2e.cs;

import com.lenskart.commons.model.OrderContext;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.e2e.dataprovider.E2EDataProvider;
import com.lenskart.e2e.helper.cs.CsRTOE2EHelpers;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import static com.lenskart.commons.utils.JsonUtils.convertObjectToJsonString;

@Slf4j
public class CsRTOTest {


    @Test(dataProviderClass = E2EDataProvider.class, dataProvider = "returnContext")
    public void verifyRTOOrder(OrderContext orderContext, CsOrderContext csOrderContext) {

        log.info("Printing order context: {}", convertObjectToJsonString(orderContext));

        CsRTOE2EHelpers.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();

    }
}


package com.lenskart.e2e.sample;

import com.lenskart.commons.model.*;
import com.lenskart.pos.helpers.PosOrderCreationHelper;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.Test;

import java.util.List;

import static com.lenskart.commons.model.PowerTypes.ZERO_POWER;

@Slf4j
public class PosOrderPlacementTest {

    @Test
    public void posOrderPlacement() {

        OrderContext orderContext = OrderContext.builder()
                .phoneNumber("2035164014")
                .productLists(List.of(
                        OrderContext.ProductList.builder()
                                .productId("148248")
                                .powerType(ZERO_POWER)
                                .itemType(ItemType.DTC)
                                .isPrescriptionRequired(false)
                                .build()))
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN)
                        .pinCode("560075")
                        .build())
                .isPosOrder(true)
                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                        .country(Countries.IN)
                        .storeId("STR_FOFO")
                        .build())
                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                .build();


        PosOrderCreationHelper posOrderCreationHelper = PosOrderCreationHelper
                .builder()
                .orderContext(orderContext)
                .build();
        posOrderCreationHelper.test();

        log.info("Order ID - {}", orderContext.getOrderId());
    }
}
package com.lenskart.e2e.dataprovider;

import com.lenskart.commons.model.*;
import org.testng.annotations.DataProvider;

import java.util.List;

import static com.lenskart.commons.model.PowerTypes.*;

public class E2EDataProvider {

    @DataProvider(name = "orderContext")
    public Object[][] orderContext() {
        return new Object[][]{

                {

                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId("148248")
                                                .powerType(BIFOCAL)
                                                .powerType(ZERO_POWER)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("121004")
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build()

                }
        };
    }

    @DataProvider(name = "posOrderContext")
    public Object[][] posOrderContext() {
        return new Object[][]{

                {

                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId("131932")
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId("STR_MUM_001")
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                }
        };
    }
}

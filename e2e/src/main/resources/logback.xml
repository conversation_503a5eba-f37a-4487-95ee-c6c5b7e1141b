<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- Define properties for centralized logging at root level -->
    <property name="LOG_FILE" value="../automation.log" />
    <property name="HTTP_LOG_FILE" value="../http-requests.log" />

    <!-- Console Appender -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Main File Appender - Centralized at root level, overwrites each run -->
    <appender name="FILE" class="ch.qos.logback.core.FileAppender">
        <file>${LOG_FILE}</file>
        <append>false</append> <!-- Overwrite file each time -->
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>


    <!-- HTTP Log File Appender - Centralized at root level, overwrites each run -->
    <appender name="HTTP_FILE" class="ch.qos.logback.core.FileAppender">
        <file>${HTTP_LOG_FILE}</file>
        <append>false</append> <!-- Overwrite file each time -->
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>



    <!-- Logger for API requests and responses -->
    <logger name="com.lenskart.commons.utils.RestUtils" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
        <appender-ref ref="HTTP_FILE" />
    </logger>

    <!-- Logger for Rest Assured -->
    <logger name="io.restassured" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="HTTP_FILE" />
    </logger>

    <!-- Logger for HTTP wire traffic -->
    <logger name="org.apache.http" level="DEBUG" additivity="false">
        <appender-ref ref="HTTP_FILE" />
    </logger>

    <!-- Logger for database operations -->
    <logger name="com.lenskart.commons.database" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </logger>

    <!-- Module-specific loggers -->
    <!-- Commons Module Logger -->
    <logger name="com.lenskart.commons" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </logger>

    <!-- Juno Module Logger -->
    <logger name="com.lenskart.juno" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </logger>

    <!-- Cosmos Module Logger -->
    <logger name="com.lenskart.cosmos" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </logger>

    <!-- SCM Module Logger -->
    <logger name="com.lenskart.scm" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </logger>

    <!-- NEXS Module Logger -->
    <logger name="com.lenskart.nexs" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </logger>

    <!-- CS Module Logger -->
    <logger name="com.lenskart.cs" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </logger>

    <!-- POS Module Logger -->
    <logger name="com.lenskart.pos" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </logger>

    <!-- E2E Module Logger -->
    <logger name="com.lenskart.e2e" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </logger>

    <!-- Example Module Logger -->
    <logger name="com.lenskart.example" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </logger>

    <!-- TestNG Logger -->
    <logger name="org.testng" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </logger>

    <!-- Root logger -->
    <root level="INFO">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </root>
</configuration>
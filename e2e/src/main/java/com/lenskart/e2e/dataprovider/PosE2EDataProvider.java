package com.lenskart.e2e.dataprovider;

import com.lenskart.commons.model.*;
import com.lenskart.pos.model.POS;
import org.testng.annotations.DataProvider;

import java.util.List;

import static com.lenskart.commons.model.PowerTypes.*;
import static com.lenskart.commons.model.ProductId.*;

public class PosE2EDataProvider {

    @DataProvider(name = "posOrderContext")
    public Object[][] posOrderContext() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                }, {
                OrderContext.builder()
                        .phoneNumber("**********")
                        .productLists(List.of(
                                OrderContext.ProductList.builder()
                                        .productId(IN_EYEGLASSES.getProductId())
                                        .powerType(ZERO_POWER)
                                        .itemType(ItemType.DTC)
                                        .finalState(NexsOrderState.DISPATCHED)
                                        .isPrescriptionRequired(false)
                                        .build()))
                        .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                .country(Countries.IN)
                                .pinCode("560075")
                                .build())
                        .isPosOrder(true)
                        .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                .country(Countries.IN)
                                .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                .build())
                        .paymentMethod(PaymentMethod.OFFLINE_CASH)
                        .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                        .build()
                },
                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_CONTACT_LENS.getProductId())
                                                .itemType(ItemType.CONTACT_LENS)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                }
                ,
                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(SINGLE_VISION)
                                                .itemType(ItemType.LOCAL_FITTING)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                }, {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_SUNGLASSES.getProductId())
                                                .powerType(SUNGLASSES)
                                                .itemType(ItemType.DTC)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(true)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_FOFO_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },
                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_SUNGLASSES.getProductId())
                                                .powerType(SUNGLASSES)
                                                .itemType(ItemType.B2B)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .isPrescriptionRequired(false)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("560075")
                                        .build())
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                        .country(Countries.IN)
                                        .storeId(POS.IN_FOFO_STORE.getStoreId())
                                        .build())
                                .paymentMethod(PaymentMethod.OFFLINE_CASH)
                                .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                                .build()
                },
                {
                OrderContext.builder()
                        .phoneNumber("**********")
                        .productLists(List.of(
                                OrderContext.ProductList.builder()
                                        .productId(IN_EYEGLASSES.getProductId())
                                        .powerType(SINGLE_VISION)
                                        .itemType(ItemType.DTC)
                                        .finalState(NexsOrderState.DISPATCHED)
                                        .isPrescriptionRequired(true)
                                        .build()))
                        .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                .country(Countries.IN)
                                .pinCode("560075")
                                .build())
                        .isPosOrder(true)
                        .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                .country(Countries.IN)
                                .storeId(POS.IN_FOFO_STORE.getStoreId())
                                .build())
                        .paymentMethod(PaymentMethod.OFFLINE_CASH)
                        .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                        .build()
                }, {
                    OrderContext.builder()
                            .phoneNumber("**********")
                            .productLists(List.of(
                                    OrderContext.ProductList.builder()
                                            .productId(IN_EYEGLASSES.getProductId())
                                            .powerType(SINGLE_VISION)
                                            .itemType(ItemType.LAST_PIECE_WAREHOUSE)
                                            .finalState(NexsOrderState.DISPATCHED)
                                            .isPrescriptionRequired(true)
                                            .build()))
                            .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                    .country(Countries.IN)
                                    .pinCode("560075")
                                    .build())
                            .isPosOrder(true)
                            .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                    .country(Countries.IN)
                                    .storeId(POS.IN_MUMBAI_STORE.getStoreId())
                                    .build())
                            .paymentMethod(PaymentMethod.OFFLINE_CASH)
                            .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                            .build()
                },
                {
                    OrderContext.builder()
                            .phoneNumber("63274324")
                            .productLists(List.of(
                                    OrderContext.ProductList.builder()
                                            .productId(SG_EYEGLASSES.getProductId())
                                            .powerType(SINGLE_VISION)
                                            .itemType(ItemType.PLANT_FITTINGS)
                                            .finalState(NexsOrderState.DISPATCHED)
                                            .isPrescriptionRequired(true)
                                            .build()))
                            .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                    .country(Countries.SG)
                                    .pinCode("540563")
                                    .build())
                            .isPosOrder(true)
                            .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                    .country(Countries.SG)
                                    .storeId(POS.SG_PREPROD_STORE.getStoreId())
                                    .build())
                            .paymentMethod(PaymentMethod.OFFLINE_CASH)
                            .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                            .build()
                },
                {
                    OrderContext.builder()
                            .phoneNumber("63274324")
                            .productLists(List.of(
                                    OrderContext.ProductList.builder()
                                            .productId(SG_SUNGLASSES.getProductId())
                                            .powerType(SUNGLASSES)
                                            .itemType(ItemType.DTC)
                                            .finalState(NexsOrderState.DISPATCHED)
                                            .isPrescriptionRequired(true)
                                            .build()))
                            .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                    .country(Countries.SG)
                                    .pinCode("540563")
                                    .build())
                            .isPosOrder(true)
                            .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                    .country(Countries.SG)
                                    .storeId(POS.SG_PREPROD_STORE.getStoreId())
                                    .build())
                            .paymentMethod(PaymentMethod.OFFLINE_CASH)
                            .headers(OrderContext.Headers.builder().client(Client.POS_IOS).build())
                            .build()
                }

//                {
//                            OrderContext.builder()
//                                    .productLists(List.of(
//                                            OrderContext.ProductList.builder()
//                                                    .productId("148248")
//                                                    .itemType(ItemType.BULK_ORDER)
//                                                    .finalState(NexsOrderState.DISPATCHED)
//                                                    .build()))
//                                    .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
//                                    .country(Countries.IN)
//    //                                .pinCode("560075")
//                                    .build())
//                                    .isPosOrder(true)
//                                    .posStoreMapper(OrderContext.PosStoreMapper.builder()
//                                            .country(Countries.IN)
//                                            .storeId(POS.IN_ADMIN_STORE.getStoreCode())
//                                            .build())
//                                    .headers(OrderContext.Headers.builder().client(Client.POS_Web).build())
//                                    .build()
//                    }
        };
    }
}

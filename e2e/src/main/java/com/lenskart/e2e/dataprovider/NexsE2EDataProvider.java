package com.lenskart.e2e.dataprovider;

import com.lenskart.commons.model.*;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.nexs.model.NexsOrderContext;
import org.testng.annotations.DataProvider;

import java.util.List;

import static com.lenskart.commons.model.PowerTypes.SUNGLASSES;
import static com.lenskart.commons.model.PowerTypes.ZERO_POWER;
import static com.lenskart.commons.model.ProductId.*;

public class NexsE2EDataProvider {

    private static final OrderContext baseOrderContext = OrderContext.builder()
            .phoneNumber("**********")
            .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                    .country(Countries.IN)
                    .pinCode(Countries.IN.getDefaultPinCode())
                    .build())
            .paymentMethod(PaymentMethod.COD)
            .headers(OrderContext.Headers.builder().client(Client.DESKTOP).build())
            .build();


    @DataProvider(name = "eyeglassWithPower")
    public Object[][] eyeglassWithPower() {
        return new Object[][]{
                {
                        baseOrderContext,
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(ZERO_POWER)
                                .finalState(NexsOrderState.DISPATCHED)
                                .build())
                }
        };
    }

    @DataProvider(name = "sunglassesWithPower")
    public Object[][] sunglassesWithPower() {
        return new Object[][]{
                {
                        baseOrderContext,
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .powerType(SUNGLASSES)
                                .isPrescriptionRequired(true)
                                .finalState(NexsOrderState.DISPATCHED)
                                .build())
                }
        };
    }

    @DataProvider(name = "eyeglassFrameOnly")
    public Object[][] eyeglassFrameOnly() {
        return new Object[][]{
                {
                        baseOrderContext,
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .finalState(NexsOrderState.DISPATCHED)
                                .build())
                }
        };
    }

    @DataProvider(name = "sunglasses")
    public Object[][] sunglasses() {
        return new Object[][]{
                {
                        baseOrderContext,
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .finalState(NexsOrderState.DISPATCHED)
                                .build())
                }
        };
    }

    @DataProvider(name = "accessories")
    public Object[][] accessories() {
        return new Object[][]{
                {
                        baseOrderContext,
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_ACCESSORIES.getProductId())
                                .finalState(NexsOrderState.DISPATCHED)
                                .build())
                }
        };
    }

    @DataProvider(name = "contactLens")
    public Object[][] contactLens() {
        return new Object[][]{
                {
                        baseOrderContext,
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_CONTACT_LENS.getProductId())
                                .isPrescriptionRequired(true)
                                .finalState(NexsOrderState.DISPATCHED)
                                .build())
                }
        };
    }

    @DataProvider(name = "contactLensZeroPower")
    public Object[][] contactLensZeroPower() {
        return new Object[][]{
                {
                        baseOrderContext,
                        List.of(OrderContext.ProductList.builder()
                                .productId(IN_CONTACT_LENS_ZERO_POWER.getProductId())
                                .finalState(NexsOrderState.DISPATCHED)
                                .build())
                }
        };
    }

    @DataProvider(name = "fr0_Loyalty")
    public Object[][] fr0_Loyalty() {
        return new Object[][]{
                {
                        baseOrderContext,
                        List.of(OrderContext.ProductList.builder()
                                        .productId(IN_SUNGLASSES.getProductId())
                                        .finalState(NexsOrderState.AWB_CREATED)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_LOYALTY.getProductId())
                                        .build())
                }
        };
    }

    @DataProvider(name = "nexsCancellationContext")
    public Object[][] nexsCancellationContext() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode(Countries.IN.getDefaultPinCode())
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build(), CsOrderContext.builder().cancellationType("full_cancellation")
                        .cancellationReason("test order")
                        .cancelledBy("<EMAIL>")
                        .cancellationSource("vsm")
                        .paymentMethod("source")
                        .cancellationReasonID(204).build()

                }
        };
    }

    @DataProvider(name = "nexsOrderReassignmnet")
    public Object[][] nexsReturnContext() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_SUNGLASSES.getProductId())
                                                .finalState(NexsOrderState.IN_PICKING)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode(Countries.IN.getDefaultPinCode())
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build()

                }
        };
    }

    @DataProvider(name = "NonNDDPincode")
    public Object[][] nonNDDPincode() {
        return new Object[][]{
                {
                        baseOrderContext,
                        List.of(OrderContext.ProductList.builder()
                                        .productId(IN_SUNGLASSES.getProductId())
                                        .finalState(NexsOrderState.IN_PICKING)
                                        .build())
                }
        };
    }

    @DataProvider(name = "NDDPincode")
    public Object[][] nddPincode() {
        return new Object[][]{
                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .finalState(NexsOrderState.IN_PICKING)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("121004")
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build()
                }
        };
    }
}

package com.lenskart.e2e.dataprovider;

import com.lenskart.commons.model.*;
import org.testng.annotations.DataProvider;

import java.util.List;

import static com.lenskart.commons.model.PowerTypes.*;
import static com.lenskart.commons.model.ProductId.*;

public class JunoE2EDataProvider {

    private static final OrderContext baseOrderContext = OrderContext.builder()
            .phoneNumber(Countries.IN.getDefaultPhoneNumber())
            .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                    .country(Countries.IN)
                    .pinCode(Countries.IN.getDefaultPinCode())
                    .build())
            .paymentMethod(PaymentMethod.COD)
            .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
            .build();

    @DataProvider(name = "eyeglassSingleVisionPower")
    public Object[][] eyeglassSingleVisionPower() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(OrderContext.ProductList.builder()
                            .productId(IN_EYEGLASSES.getProductId())
                            .powerType(SINGLE_VISION)
                            .build())
                }
        };
    }

    @DataProvider(name = "eyeglassBifocalPower")
    public Object[][] eyeglassBifocalPower() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(OrderContext.ProductList.builder()
                            .productId(IN_EYEGLASSES.getProductId())
                            .powerType(BIFOCAL)
                            .build())
                }
        };
    }

    @DataProvider(name = "eyeglassZeroPower")
    public Object[][] eyeglassZeroPower() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(OrderContext.ProductList.builder()
                                                .productId(IN_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                        .build())
                }
        };
    }

    @DataProvider(name = "eyeglassWithoutPower")
    public Object[][] eyeglassWithoutPower() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(OrderContext.ProductList.builder()
                            .productId(IN_EYEGLASSES.getProductId())
                            .build())
                }
        };
    }

    @DataProvider(name = "sunglassWithoutPower")
    public Object[][] sunglassWithoutPower() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(OrderContext.ProductList.builder()
                            .productId(IN_SUNGLASSES.getProductId())
                            .build())
                }
        };
    }

    @DataProvider(name = "sunglassWithPower")
    public Object[][] sunglassWithPower() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(OrderContext.ProductList.builder()
                                                .productId(IN_SUNGLASSES.getProductId())
                                                .powerType(SUNGLASSES)
                            .build())
                }
        };
    }

    @DataProvider(name = "goldPid")
    public Object[][] goldPid() {
        OrderContext goldContext = OrderContext.builder()
                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                        .pinCode(Countries.IN.getDefaultPinCode())
                                        .build())
                .paymentMethod(PaymentMethod.CREDIT_CARD)
                                .headers(OrderContext.Headers.builder().client(Client.MOBILESITE).build())
                .build();
                
        return new Object[][]{
                {
                    goldContext,
                    List.of(OrderContext.ProductList.builder()
                            .productId(IN_LOYALTY.getProductId())
                            .build())
                }
        };
    }

    @DataProvider(name = "frameOnly")
    public Object[][] frameOnly() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(OrderContext.ProductList.builder()
                            .productId(IN_FRAME_ONLY.getProductId())
                            .build())
                }
        };
    }

    @DataProvider(name = "contactLens")
    public Object[][] contactLens() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(OrderContext.ProductList.builder()
                            .productId(IN_CONTACT_LENS.getProductId())
                            .build())
                }
        };
    }

    @DataProvider(name = "contactLensZeroPower")
    public Object[][] contactLensZeroPower() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(OrderContext.ProductList.builder()
                            .productId(IN_CONTACT_LENS_ZERO_POWER.getProductId())
                            .build())
                }
        };
    }

    @DataProvider(name = "accessories")
    public Object[][] accessories() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(OrderContext.ProductList.builder()
                            .productId(IN_ACCESSORIES.getProductId())
                            .build())
                }
        };
    }

    @DataProvider(name = "eyeglassWithPowerAndSunglassAndContactLens")
    public Object[][] eyeglassWithPowerAndSunglassAndContactLens() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(SINGLE_VISION)
                                .build(),
                        OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .powerType(SUNGLASSES)
                                .build(),
                        OrderContext.ProductList.builder()
                                .productId(IN_CONTACT_LENS.getProductId())
                                .powerType(CONTACT_LENS)
                                .build()
                    )
                }
        };
    }

    @DataProvider(name = "eyeglassWithPowerAndInsurance")
    public Object[][] eyeglassWithPowerAndInsurance() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(SINGLE_VISION)
                                .build(),
                        OrderContext.ProductList.builder()
                                .productId(IN_INSURANCE.getProductId())
                                .build()
                    )
                }
        };
    }

    @DataProvider(name = "eyeglassWithoutPowerAndInsurance")
    public Object[][] eyeglassWithoutPowerAndInsurance() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .build(),
                        OrderContext.ProductList.builder()
                                .productId(IN_INSURANCE.getProductId())
                                .build()
                    )
                }
        };
    }

    @DataProvider(name = "sunglassWithPowerAndInsurance")
    public Object[][] sunglassWithPowerAndInsurance() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .powerType(SUNGLASSES)
                                .build(),
                        OrderContext.ProductList.builder()
                                .productId(IN_INSURANCE.getProductId())
                                .build()
                    )
                }
        };
    }

    @DataProvider(name = "sunglassWithoutPowerAndInsurance")
    public Object[][] sunglassWithoutPowerAndInsurance() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .build(),
                        OrderContext.ProductList.builder()
                                .productId(IN_INSURANCE.getProductId())
                                .build()
                    )
                }
        };
    }

    @DataProvider(name = "eyeAndSunglassWithPowerAndGold")
    public Object[][] eyeAndSunglassWithPowerAndGold() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_LOYALTY.getProductId())
                                .build(),
                        OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(SINGLE_VISION)
                                .build(),
                        OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .powerType(SUNGLASSES)
                                .build()
                    )
                }
        };
    }

    @DataProvider(name = "eyeAndSunglassWithoutPowerAndGold")
    public Object[][] eyeAndSunglassWithoutPowerAndGold() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_LOYALTY.getProductId())
                                .build(),
                        OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .build(),
                        OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .build()
                    )
                }
        };
    }

    @DataProvider(name = "eyeWithPowerAndSunglassWithoutPowerAndGold")
    public Object[][] eyeWithPowerAndSunglassWithoutPowerAndGold() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_LOYALTY.getProductId())
                                .build(),
                        OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(SINGLE_VISION)
                                .build(),
                        OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .build()
                    )
                }
        };
    }

    @DataProvider(name = "eyeWithoutPowerAndSunglassWithPowerAndGold")
    public Object[][] eyeWithoutPowerAndSunglassWithPowerAndGold() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_LOYALTY.getProductId())
                                .build(),
                        OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .build(),
                        OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .powerType(SUNGLASSES)
                                .build()
                    )
                }
        };
    }

    @DataProvider(name = "eyeglassFrameOnlyWithSunglass")
    public Object[][] eyeglassFrameOnlyWithSunglass() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_FRAME_ONLY.getProductId())
                                .build(),
                        OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .build()
                    )
                }
        };
    }

    @DataProvider(name = "eyeglassAndSunglassWithoutPower")
    public Object[][] eyeglassAndSunglassWithoutPower() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .build(),
                        OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .build()
                    )
                }
        };
    }

    @DataProvider(name = "eyeglassFrameOnlyWithContactLens")
    public Object[][] eyeglassFrameOnlyWithContactLens() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_FRAME_ONLY.getProductId())
                                .build(),
                        OrderContext.ProductList.builder()
                                .productId(IN_CONTACT_LENS.getProductId())
                                .build()
                    )
                }
        };
    }

    @DataProvider(name = "eyeglassAndContactLensWithoutPower")
    public Object[][] eyeglassAndContactLensWithoutPower() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .build(),
                        OrderContext.ProductList.builder()
                                .productId(IN_CONTACT_LENS.getProductId())
                                .build()
                    )
                }
        };
    }

    @DataProvider(name = "sunglassWithoutPowerAndContactLens")
    public Object[][] sunglassWithoutPowerAndContactLens() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .build(),
                        OrderContext.ProductList.builder()
                                .productId(IN_CONTACT_LENS.getProductId())
                                .build()
                    )
                }
        };
    }

    @DataProvider(name = "sunglassWithoutPowerAndGold")
    public Object[][] sunglassWithoutPowerAndGold() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .build(),
                        OrderContext.ProductList.builder()
                                .productId(IN_LOYALTY.getProductId())
                                .build()
                    )
                }
        };
    }

    @DataProvider(name = "sunglassWithPowerAndGold")
    public Object[][] sunglassWithPowerAndGold() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .powerType(SUNGLASSES)
                                .build(),
                        OrderContext.ProductList.builder()
                                .productId(IN_LOYALTY.getProductId())
                                .build()
                    )
                }
        };
    }

    @DataProvider(name = "eyeglassFrameOnly")
    public Object[][] eyeglassFrameOnly() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(OrderContext.ProductList.builder()
                            .productId(IN_FRAME_ONLY.getProductId())
                            .build())
                }
        };
    }

    @DataProvider(name = "eyeglassZeroPowerWithSunglassWithPower")
    public Object[][] eyeglassZeroPowerWithSunglassWithpower() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(ZERO_POWER)
                                .build(),
                        OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .powerType(SUNGLASSES)
                                .build()
                    )
                }
        };
    }

    @DataProvider(name = "updatePrescriptionforEyeglassSingeVision")
    public Object[][] updatePrescriptionforEyeglassSingeVision() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(OrderContext.ProductList.builder()
                            .productId(IN_EYEGLASSES.getProductId())
                            .powerType(SINGLE_VISION)
                            .build())
                }
        };
    }

    @DataProvider(name = "updatePrescriptionforEyeglassBifocal")
    public Object[][] updatePrescriptionforEyeglassBifocal() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(OrderContext.ProductList.builder()
                            .productId(IN_EYEGLASSES.getProductId())
                            .powerType(BIFOCAL)
                            .build())
                }
        };
    }

    @DataProvider(name = "updatePrescriptionforSunglass")
    public Object[][] updatePrescriptionforSunglass() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(OrderContext.ProductList.builder()
                            .productId(IN_SUNGLASSES.getProductId())
                            .powerType(SUNGLASSES)
                            .build())
                }
        };
    }

    @DataProvider(name = "cartupdatePrescriptionforEyeglassSingleVision")
    public Object[][] cartupdatePrescriptionforEyeglassSingleVision() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(OrderContext.ProductList.builder()
                            .productId(IN_EYEGLASSES.getProductId())
                            .powerType(SINGLE_VISION)
                            .build())
                }
        };
    }

    @DataProvider(name = "cartupdatePrescriptionforEyeglassBifocal")
    public Object[][] cartupdatePrescriptionforEyeglassBifocal() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(OrderContext.ProductList.builder()
                            .productId(IN_EYEGLASSES.getProductId())
                            .powerType(BIFOCAL)
                            .build())
                }
        };
    }

    @DataProvider(name = "cashbackCheckOnOrderPlacement")
    public Object[][] cashbackCheckOnOrderPlacement() {
        return new Object[][]{
                {
                        baseOrderContext,
                        List.of(
                                OrderContext.ProductList.builder()
                                        .productId(IN_SUNGLASSES.getProductId())
                                        .powerType(SUNGLASSES)
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_EYEGLASSES.getProductId())
                                        .build(),
                                OrderContext.ProductList.builder()
                                        .productId(IN_LOYALTY.getProductId())
                                        .build()
                        )
                }
        };
    }

    @DataProvider(name = "cartupdatePrescriptionforSunglass")
    public Object[][] cartupdatePrescriptionforSunglass() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(OrderContext.ProductList.builder()
                            .productId(IN_SUNGLASSES.getProductId())
                            .powerType(SUNGLASSES)
                            .build())
                }
        };

    }

    @DataProvider(name = "eyeglassWithPowerAndSunglassWithPower")
    public Object[][] eyeglassWithPowerAndSunglassWithPower() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(SINGLE_VISION)
                                .build(),
                        OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .powerType(SUNGLASSES)
                                .build()
                    )
                }
        };
    }

    //GV cases
    @DataProvider(name = "eyeglassSingleVisionPowerWithGV")
    public Object[][] eyeglassSingleVisionPowerWithGV() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(OrderContext.ProductList.builder()
                            .productId(IN_EYEGLASSES.getProductId())
                            .powerType(SINGLE_VISION)
                            .build()),
                    OrderContext.GiftVoucherList.builder()
                            .giftVoucher(GiftVoucher.TESTGV75_IN)
                            .isGiftVoucherApplicable(true)
                            .build()
                }
        };
    }

    @DataProvider(name = "eyeglassBifocalPowerWithGV")
    public Object[][] eyeglassBifocalPowerWithGV() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(OrderContext.ProductList.builder()
                            .productId(IN_EYEGLASSES.getProductId())
                            .powerType(BIFOCAL)
                            .build()),
                    OrderContext.GiftVoucherList.builder()
                            .giftVoucher(GiftVoucher.TESTGV100_IN)
                            .isGiftVoucherApplicable(true)
                            .build()
                }
        };
    }

    @DataProvider(name = "sunglassWithPowerWithGV")
    public Object[][] sunglassWithPowerWithGV() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(OrderContext.ProductList.builder()
                            .productId(IN_SUNGLASSES.getProductId())
                            .powerType(SUNGLASSES)
                            .build()),
                    OrderContext.GiftVoucherList.builder()
                            .giftVoucher(GiftVoucher.TESTGV75_IN)
                            .isGiftVoucherApplicable(true)
                            .build()
                }
        };
    }

    @DataProvider(name = "contactLensWithGV")
    public Object[][] contactLensWithGV() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(OrderContext.ProductList.builder()
                            .productId(IN_CONTACT_LENS.getProductId())
                            .powerType(CONTACT_LENS)
                            .build()),
                    OrderContext.GiftVoucherList.builder()
                            .giftVoucher(GiftVoucher.TESTGV75_IN)
                            .isGiftVoucherApplicable(true)
                            .build()
                }
        };
    }

    @DataProvider(name = "eyeglassAndSunglassWithGV")
    public Object[][] eyeglassAndSunglassWithGV() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(SINGLE_VISION)
                                .build(),
                        OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .powerType(SUNGLASSES)
                                .build()
                    ),
                    OrderContext.GiftVoucherList.builder()
                            .giftVoucher(GiftVoucher.TESTGV100_IN)
                            .isGiftVoucherApplicable(true)
                            .build()
                }
        };
    }

    @DataProvider(name = "eyeglassAndContactLensWithGV")
    public Object[][] eyeglassAndContactLensWithGV() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(SINGLE_VISION)
                                .build(),
                        OrderContext.ProductList.builder()
                                .productId(IN_CONTACT_LENS.getProductId())
                                .powerType(CONTACT_LENS)
                                .build()
                    ),
                    OrderContext.GiftVoucherList.builder()
                            .giftVoucher(GiftVoucher.TESTGV100_IN)
                            .isGiftVoucherApplicable(true)
                            .build()
                }
        };
    }

    @DataProvider(name = "sunglassAndContactLensWithGV")
    public Object[][] sunglassAndContactLensWithGV() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .powerType(SUNGLASSES)
                                .build(),
                        OrderContext.ProductList.builder()
                                .productId(IN_CONTACT_LENS.getProductId())
                                .powerType(CONTACT_LENS)
                                .build()
                    ),
                    OrderContext.GiftVoucherList.builder()
                            .giftVoucher(GiftVoucher.TESTGV100_IN)
                            .isGiftVoucherApplicable(true)
                            .build()
                }
        };
    }

    @DataProvider(name = "multiProductWithGV")
    public Object[][] multiProductWithGV() {
        return new Object[][]{
                {
                    baseOrderContext,
                    List.of(
                        OrderContext.ProductList.builder()
                                .productId(IN_EYEGLASSES.getProductId())
                                .powerType(SINGLE_VISION)
                                .build(),
                        OrderContext.ProductList.builder()
                                .productId(IN_SUNGLASSES.getProductId())
                                .powerType(SUNGLASSES)
                                .build()

                    ),
                    OrderContext.GiftVoucherList.builder()
                            .giftVoucher(GiftVoucher.TESTGV100_IN)
                            .isGiftVoucherApplicable(true)
                            .build()
                }
        };
    }
}

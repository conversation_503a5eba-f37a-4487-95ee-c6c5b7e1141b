package com.lenskart.e2e.dataprovider;

import com.lenskart.commons.model.*;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.pos.model.POS;
import org.testng.annotations.DataProvider;

import java.util.List;

import static com.lenskart.commons.model.PowerTypes.SINGLE_VISION;
import static com.lenskart.commons.model.PowerTypes.ZERO_POWER;

public class CsE2EDataProvider {

    @DataProvider(name = "returnContext")
    public Object[][] returnContext() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId("148248")
                                                .powerType(ZERO_POWER)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("121004")
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build(), CsOrderContext.builder().productIDToBeReturned("148248").build()

                }
        };
    }

    @DataProvider(name = "returnContextForPos")
    public Object[][] returnContextForPos() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId(ProductId.ID_EYEGLASSES.getProductId())
                                                .powerType(ZERO_POWER)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("121004")
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .isPosOrder(true)
                                .posStoreMapper(OrderContext.PosStoreMapper.builder()
                                .country(Countries.IN)
                                .storeId(POS.IN_MUMBAI_STORE.getStoreId()).build())
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build(), CsOrderContext.builder().productIDToBeReturned(ProductId.ID_EYEGLASSES.getProductId()).build()

                }
        };
    }

    @DataProvider(name = "cancellationContext")
    public Object[][] cancellationContext() {
        return new Object[][]{

                {
                        OrderContext.builder()
                                .phoneNumber("**********")
                                .productLists(List.of(
                                        OrderContext.ProductList.builder()
                                                .productId("148248")
                                                .powerType(ZERO_POWER)
                                                .finalState(NexsOrderState.DISPATCHED)
                                                .build()))
                                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                                        .country(Countries.IN)
                                        .pinCode("121004")
                                        .build())
                                .paymentMethod(PaymentMethod.COD)
                                .headers(OrderContext.Headers.builder().client(Client.ANDROID).build())
                                .build(), CsOrderContext.builder()
                                            .productIDToBeCancelled("148248")
                                            .cancellationType("full_cancellation")
                                            .cancelledBy("<EMAIL>")
                                            .cancellationReason("")
                                            .cancellationReasonID(188)
                                            .paymentMethod("cc")
                                            .cancelledOrderShipmentStatus("closed")
                                            .cancellationSource("vsm")
                                            .build()

                }
        };
    }
}

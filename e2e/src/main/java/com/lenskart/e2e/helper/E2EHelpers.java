package com.lenskart.e2e.helper;

import com.lenskart.commons.base.BaseHelper;
import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.JsonUtils;

import com.lenskart.e2e.validator.E2EValidator;
import com.lenskart.juno.helpers.JunoOrderCreationHelper;
import com.lenskart.nexs.helpers.NexsOrderStateHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.pos.helpers.PosOrderCreationHelper;
import com.lenskart.scm.util.OrderAdopterUtil;
import lombok.SneakyThrows;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;


@SuperBuilder
@Slf4j
public class E2EHelpers extends BaseHelper<Object, Object> implements ServiceHelper {

    JunoOrderCreationHelper orderCreationHelper;
    PosOrderCreationHelper posOrderCreationHelper;
    OrderContext orderContext;


    @SneakyThrows
    @Override
    public ServiceHelper init() {

        /* Juno Helper to create order */
        if (orderContext.isPosOrder()) {
            posOrderCreationHelper = PosOrderCreationHelper
                    .builder()
                    .orderContext(orderContext)
                    .build();
            posOrderCreationHelper.test();
        } else {
            orderCreationHelper = JunoOrderCreationHelper
                    .builder()
                    .orderContext(orderContext)
                    .build();
            orderCreationHelper.test();
        }

        /* Scm helper to move the order to scm states */
        AwaitUtils.sleep(Duration.ofSeconds(20));
        OrderAdopterUtil.syncOrder(String.valueOf(orderContext.getOrderId()));

        NexsOrderContext nexsOrderContext = NexsOrderContext.builder().headers(NexsOrderContext.Headers.builder()
                        .build())
                .incrementId(String.valueOf(orderContext.getOrderId()))
                .build();

        NexsOrderStateHelper stateManager = NexsOrderStateHelper.builder()
                .orderContext(orderContext)
                .nexsOrderContext(nexsOrderContext)
                .sequentialTransition(true)
                .build();

        stateManager.test();


        log.info("Order context is {}", JsonUtils.convertObjectToJsonString(orderContext));

        /* cse helper to move the order to cse states */
        CsRTOHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();


        return this;
    }

    @Override
    public ServiceHelper process() {
        return this;
    }

    @Override
    public ServiceHelper validate() {
        E2EValidator validator = E2EValidator
                .builder()
                .orderContext(orderContext)
                .build();




        // juno validator
        // nexs validator
        // cse validator
        // pos validator


        validator.validateNode();
        validator.validateDBEntities();
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

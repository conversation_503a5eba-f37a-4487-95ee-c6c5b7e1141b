package com.lenskart.e2e.helper.juno;

import com.lenskart.commons.base.BaseHelper;
import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.juno.helpers.JunoOrderCreationGuestHelper;
import lombok.experimental.SuperBuilder;

@SuperBuilder
public class JunoE2EGuestHelper extends BaseHelper<Object, Object> implements ServiceHelper {

    OrderContext orderContext;
    CsOrderContext csOrderContext;


    @Override
    public ServiceHelper init() {
        JunoOrderCreationGuestHelper
                .builder()
                .orderContext(orderContext)
                .build()
                .test();
        return this;

    }

    @Override
    public ServiceHelper process() {
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

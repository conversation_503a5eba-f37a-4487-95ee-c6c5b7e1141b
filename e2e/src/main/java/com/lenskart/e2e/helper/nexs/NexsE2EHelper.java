package com.lenskart.e2e.helper.nexs;

import com.lenskart.commons.base.BaseHelper;
import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.e2e.validator.E2EValidator;
import com.lenskart.juno.helpers.JunoOrderCreationHelper;
import com.lenskart.nexs.helpers.NexsOrderStateHelper;
import com.lenskart.nexs.model.NexsOrderContext;
import com.lenskart.pos.helpers.PosOrderCreationHelper;
import com.lenskart.scm.util.OrderAdopterUtil;
import lombok.Getter;
import lombok.SneakyThrows;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;


@SuperBuilder
@Slf4j
public class NexsE2EHelper extends BaseHelper<Object, Object> implements ServiceHelper {

    JunoOrderCreationHelper orderCreationHelper;
    PosOrderCreationHelper posOrderCreationHelper;
    OrderContext orderContext;
    @Getter
    NexsOrderContext nexsOrderContext;


    @SneakyThrows
    @Override
    public ServiceHelper init() {

        /* Juno Helper to create order */
        if (orderContext.isPosOrder()) {
            posOrderCreationHelper = PosOrderCreationHelper
                    .builder()
                    .orderContext(orderContext)
                    .build();
            posOrderCreationHelper.test();
        } else {
            orderCreationHelper = JunoOrderCreationHelper
                    .builder()
                    .orderContext(orderContext)
                    .build();
            orderCreationHelper.test();
        }

        /* Scm helper to move the order to scm states */
        OrderAdopterUtil.syncOrder(String.valueOf(orderContext.getOrderId()));

         nexsOrderContext = NexsOrderContext.builder().headers(NexsOrderContext.Headers.builder()
                        .build())
                .incrementId(String.valueOf(orderContext.getOrderId()))
                .build();

        NexsOrderStateHelper stateManager = NexsOrderStateHelper.builder()
                .orderContext(orderContext)
                .nexsOrderContext(nexsOrderContext)
                .sequentialTransition(true)
                .build();

        stateManager.test();
        return this;
    }

    @Override
    public ServiceHelper process() {
        return this;
    }

    @Override
    public ServiceHelper validate() {return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

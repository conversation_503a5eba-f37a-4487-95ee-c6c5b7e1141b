package com.lenskart.e2e.helper.cs;

import com.lenskart.commons.base.BaseHelper;
import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.cs.helpers.CancellationHelper;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.juno.helpers.JunoOrderCreationHelper;
import com.lenskart.nexs.validator.wmsvalidator.OrderCancellationValidator;
import com.lenskart.pos.helpers.PosOrderCreationHelper;
import lombok.SneakyThrows;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;


@SuperBuilder
@Slf4j
public class CsCancellationE2EHelpers extends BaseHelper<Object, Object> implements ServiceHelper {

    JunoOrderCreationHelper orderCreationHelper;
    PosOrderCreationHelper posOrderCreationHelper;
    OrderContext orderContext;
    CsOrderContext csOrderContext;


    @SneakyThrows
    @Override
    public ServiceHelper init() {

        /* Juno Helper to create order */
        if (orderContext.isPosOrder()) {
            posOrderCreationHelper = PosOrderCreationHelper
                    .builder()
                    .orderContext(orderContext)
                    .build();
            posOrderCreationHelper.test();
        } else {
            orderCreationHelper = JunoOrderCreationHelper
                    .builder()
                    .orderContext(orderContext)
                    .build();
            orderCreationHelper.test();
        }
        AwaitUtils.sleepSeconds(15);
        return this;
    }

    @Override
    public ServiceHelper process() {
        CancellationHelper.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build()
                .test();
        return this;
    }

    @Override
    public ServiceHelper validate() {
        OrderCancellationValidator.builder()
                .orderContext(orderContext)
                .build()
                .validateNode();

        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

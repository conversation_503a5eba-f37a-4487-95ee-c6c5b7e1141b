package com.lenskart.e2e.helper.juno;

import com.lenskart.commons.base.BaseHelper;
import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.juno.helpers.JunoOrderCreationHelper;
import lombok.experimental.SuperBuilder;

@SuperBuilder
public class JunoE2EHelper extends BaseHelper<Object, Object> implements ServiceHelper {

    OrderContext orderContext;
    CsOrderContext csOrderContext;
    boolean isGuestUser;
    boolean isPrescriptionRequired;


    @Override
    public ServiceHelper init() {
        JunoOrderCreationHelper
                .builder()
                .orderContext(orderContext)
                .isGuestUser(isGuestUser)
                .isPrescriptionRequired(isPrescriptionRequired)
                .build()
                .test();
        return this;
    }

    @Override
    public ServiceHelper process() {
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

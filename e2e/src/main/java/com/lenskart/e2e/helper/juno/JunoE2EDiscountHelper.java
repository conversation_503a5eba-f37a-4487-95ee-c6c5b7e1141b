package com.lenskart.e2e.helper.juno;

import com.lenskart.commons.base.BaseHelper;
import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.juno.helpers.JunoOrderCreationDiscountHelper;
import com.lenskart.juno.helpers.JunoOrderCreationHelper;
import lombok.experimental.SuperBuilder;

@SuperBuilder
public class JunoE2EDiscountHelper extends BaseHelper<Object, Object> implements ServiceHelper {

    OrderContext orderContext;
    CsOrderContext csOrderContext;
    boolean isGuestUser;

    @Override
    public ServiceHelper init() {
        JunoOrderCreationDiscountHelper
                .builder()
                .orderContext(orderContext)
                .isGuestUser(isGuestUser)
                .build()
                .test();
        return this;
    }

    @Override
    public ServiceHelper process() {
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

package com.lenskart.e2e.validator;

import com.lenskart.commons.base.IValidator;
import com.lenskart.commons.model.OrderContext;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.testng.asserts.SoftAssert;


@SuperBuilder
@Slf4j
public class E2EValidator implements IValidator {

    static SoftAssert softAssert;
    OrderContext orderContext;


    static {
        softAssert = new SoftAssert();
    }

    @Override
    public void validateNode() {

    }

    @Override
    public void validateDBEntities() {

    }
}

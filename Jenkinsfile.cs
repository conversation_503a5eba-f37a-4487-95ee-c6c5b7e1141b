@Library('jenkins/shared-functions.groovy') _

// CS Module Jenkins Pipeline
// This pipeline is specifically configured for the cs module

def sharedFunctions

node {
    // Load shared functions
    sharedFunctions = load 'jenkins/shared-functions.groovy'
}

// Module-specific configuration
def moduleConfig = [
    moduleName: 'cs',
    suiteXmlFile: 'src/test/resources/testng.xml',
    defaultEnvironment: 'preprod',
    defaultTestCategory: 'REGRESSION'
]

// Execute the pipeline using shared functions
sharedFunctions.executeModulePipeline(moduleConfig)

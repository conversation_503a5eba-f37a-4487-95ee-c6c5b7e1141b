package Validator;

import com.lenskart.commons.base.IValidator;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.juno.database.JunoSessionDbUtils;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.testng.Assert;
import org.testng.asserts.SoftAssert;

@Slf4j
@Builder
public class SessionValidator implements IValidator {

    static SoftAssert softAssert = new SoftAssert();
    OrderContext orderContext;

    @Override
    public void validateNode() {
        validateDBEntities();
    }

    @Override
    public void validateDBEntities() {
        String sessionToken = orderContext.getHeaders().getSessionToken();
        Document sessionDetails = JunoSessionDbUtils.getSessionDetails(sessionToken);

        // Fail fast if sessionDetails is null or empty
        if (sessionDetails == null || sessionDetails.isEmpty()) {
            log.error("Session details are null or empty for session token: {}", sessionToken);
            Assert.fail("Session details should not be null or empty for session token: " + sessionToken);
        }

        // Validate session ID
        Object dbSessionId = sessionDetails.get("_id");
        log.info("Session ID from DB: {}", dbSessionId);

        softAssert.assertEquals(
                dbSessionId.toString(),
                sessionToken,
                "Session ID in DB does not match session token from request"
        );

        softAssert.assertAll(); // Report any soft assertion failures
    }
}
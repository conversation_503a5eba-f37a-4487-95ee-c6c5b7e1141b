package Validator;

import com.lenskart.commons.base.IValidator;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.juno.database.PaymentDbUtils;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.testng.Assert;
import org.testng.asserts.SoftAssert;

@Slf4j
@Builder
public class PaymentValidator implements IValidator {

    static SoftAssert softAssert = new SoftAssert();
    OrderContext orderContext;

    @Override
    public void validateNode() {
        validateDBEntities();
    }

    @Override
    public void validateDBEntities() {
        String orderId = String.valueOf(orderContext.getOrderId());
        Document paymentDetails = PaymentDbUtils.getPaymentDetails(orderId);

        Assert.assertNotNull(paymentDetails, "Payment details should not be null for order ID: " + orderId);

        if (paymentDetails.isEmpty()) {
            log.error("Payment details are empty for order ID: {}", orderId);
            Assert.fail("Payment details should not be empty for order ID: " + orderId);
        }

        // Extract nested document
        Document paymentGatewayInfo = (Document) paymentDetails.get("paymentGatewayInfo");

        if (paymentGatewayInfo != null) {
            String paymentMethod = paymentGatewayInfo.getString("paymentMethod");
            log.info("Payment Method from DB: {}", paymentMethod);
            softAssert.assertEquals(
                    paymentMethod,
                    orderContext.getPaymentMethod(),
                    "Payment method does not match the one in the order context"
            );
        } else {
            log.error("paymentGatewayInfo is missing in payment details for order ID: {}", orderId);
            Assert.fail("paymentGatewayInfo is missing in the DB record");
        }

        // Optionally check root-level paymentMethod if it exists
        if (paymentDetails.containsKey("paymentMethod")) {
            softAssert.assertEquals(
                    paymentDetails.getString("paymentMethod"),
                    orderContext.getPaymentMethod(),
                    "Root-level paymentMethod does not match the one in the order context"
            );
        }
    }
}
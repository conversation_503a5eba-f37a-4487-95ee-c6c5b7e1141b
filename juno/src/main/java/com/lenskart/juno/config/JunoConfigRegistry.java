package com.lenskart.juno.config;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Central registry for Juno configurations
 */
@Slf4j
public class JunoConfigRegistry {
    // Singleton instance
    private static JunoConfigRegistry instance;
    
    // Configuration caches
    private final Map<String, String> baseUrls = new ConcurrentHashMap<>();
    
    // Constants
    private static final String DEFAULT_ENVIRONMENT = "preprod";
    private static final String BASE_URLS_SECTION = "baseUrls";
    
    // Private constructor for singleton
    private JunoConfigRegistry() {
        // Initialize configurations
        loadConfigurations();
    }
    
    /**
     * Get singleton instance
     * 
     * @return JunoConfigRegistry instance
     */
    public static synchronized JunoConfigRegistry getInstance() {
        if (instance == null) {
            instance = new JunoConfigRegistry();
        }
        return instance;
    }
    
    /**
     * Load all configurations
     */
    private void loadConfigurations() {
        loadBaseUrls();
    }
    
    /**
     * Load base URLs for services
     */
    @SuppressWarnings("unchecked")
    private void loadBaseUrls() {
        try {
            // Load the Juno configuration
            JunoConfig config = JunoConfigLoader.loadConfig();
            
            // Get the environment configuration
            JunoConfig.EnvironmentConfig envConfig = config.getEnvironment(DEFAULT_ENVIRONMENT);
            
            if (envConfig == null) {
                log.warn("Environment not found: {}", DEFAULT_ENVIRONMENT);
                return;
            }
            
            // Get the base URLs
            Map<String, String> urls = envConfig.getBaseUrls();
            
            if (urls == null || urls.isEmpty()) {
                log.warn("No base URLs found in environment: {}", DEFAULT_ENVIRONMENT);
                return;
            }
            
            // Store the base URLs
            baseUrls.putAll(urls);
            
            log.info("Loaded {} base URLs", baseUrls.size());
        } catch (Exception e) {
            log.error("Failed to load base URLs: {}", e.getMessage(), e);
        }
    }
    
    /**
     * Get base URL for a service
     * 
     * @param serviceName Service name
     * @return Base URL
     */
    public String getBaseUrl(String serviceName) {
        return baseUrls.get(serviceName);
    }
    
    /**
     * Get all base URLs
     * 
     * @return Map of base URLs
     */
    public Map<String, String> getAllBaseUrls() {
        return new HashMap<>(baseUrls);
    }
    
    /**
     * Refresh all configurations
     */
    public synchronized void refresh() {
        // Clear caches
        baseUrls.clear();
        
        // Clear the config loader cache
        JunoConfigLoader.clearCache();
        
        // Reload configurations
        loadConfigurations();
        
        log.info("All configurations refreshed");
    }
}

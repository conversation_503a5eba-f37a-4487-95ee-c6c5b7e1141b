package com.lenskart.juno.requestbuilder;

import com.lenskart.commons.model.OrderContext;
import org.json.JSONObject;

import java.util.Objects;

public class JunoOrderRequestBuilder {

    public static JSONObject createPayloadForSession(OrderContext orderContext) {
        JSONObject payload = new JSONObject();
        payload.put("phoneCode", orderContext.getCountryCodeMapper().getCountry().getDialingCode());
        payload.put("telephone", Objects.isNull(orderContext.getPhoneNumber()) ?
                orderContext.getCountryCodeMapper().getCountry().getDefaultPhoneNumber() : orderContext.getPhoneNumber());
        payload.put("code", orderContext.getOtp());
        return payload;
    }

    public static JSONObject createPayloadForAddCart(OrderContext.ProductList product) {
        JSONObject payload = new JSONObject();
        payload.put("productId", product.getProductId());
        payload.put("addOns", "");
        payload.put("packageId", product.getPackageId());
        payload.put("powerType", product.getPowerType().getDisplayName());
        payload.put("isScApplied", false);
        return payload;
    }

    public static JSONObject createPayloadForShippingAddress(OrderContext orderContext) {
        JSONObject payload = new JSONObject();
        JSONObject address = new JSONObject();

        payload.put("firstName", "Automation");
        payload.put("lastName", "user");
        payload.put("country", orderContext.getCountryCodeMapper().getCountry().name());
        payload.put("gender", "Male");
        payload.put("phone", orderContext.getCountryCodeMapper().getCountry().getDefaultPhoneNumber());  // which phone number to use?
        payload.put("city", "bangalore");
        payload.put("postcode", orderContext.getCountryCodeMapper().getPinCode());
        payload.put("phoneCode", orderContext.getCountryCodeMapper().getCountry().getDialingCode());
        payload.put("addressline1", "Test order");
        payload.put("state", "test");
        payload.put("email", "<EMAIL>");
        address.put("address", payload);
        return address;
    }


    public static JSONObject createPayloadForOrderPayment(OrderContext orderContext) {
        JSONObject paymentInfo = new JSONObject();
        JSONObject payload = new JSONObject();

        paymentInfo.put("paymentMethod", orderContext.getPaymentMethod().getDisplayName());
        paymentInfo.put("gatewayId", "PU");

        payload.put("paymentInfo", paymentInfo);
        payload.put("device", orderContext.getHeaders().getClient().getDisplayName());
        return payload;
    }


    public static JSONObject createPayloadForPrescription(OrderContext orderContext) {
        JSONObject payload = new JSONObject();
        payload.put("powerType", orderContext.getProductLists().get(0).getPowerType().getDisplayName());
        payload.put("left", new JSONObject().put("sph", "-0.25"));
        payload.put("right", new JSONObject().put("sph", "-0.25"));
        payload.put("userName", "Automation User");
        return payload;
    }


}

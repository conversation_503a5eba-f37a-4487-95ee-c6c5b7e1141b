package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.GiftVoucher;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.juno.util.GiftVoucherUtil;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.util.Map;

import static com.lenskart.juno.endpoints.JunoEndpoints.V2_CART_GIFT_VOUCHER;

@Slf4j
@SuperBuilder
public class UpdateGiftVoucherHelper extends JunoBaseHelper implements ServiceHelper {

    private OrderContext orderContext;
    private Response response;

    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        // Verify we have a voucher to apply
        if (orderContext.getGiftVoucher() == null) {
            log.warn("No gift voucher specified in order context");
            return this;
        }

        String newVoucherCode = orderContext.getGiftVoucher().getCode();
        log.info("Processing gift voucher update request for code: {}", newVoucherCode);

        // Get current cart details
        GetCartDetailsHelper cartDetailsHelper = (GetCartDetailsHelper)
                GetCartDetailsHelper.builder()
                        .orderContext(orderContext)
                        .build()
                        .test();

        // Check cart state
        JSONObject cartJson = new JSONObject(cartDetailsHelper.response.asString());
        String existingVoucherCode = GiftVoucherUtil.getFirstApplicableVoucher(cartJson);
        
        // Case 1: No voucher in cart
        if (existingVoucherCode == null) {
            log.info("No existing voucher found in cart");
            Map<String, String> params = Map.of("giftVoucherCode", newVoucherCode);
            response = GiftVoucherUtil.tryApplyVoucher(newVoucherCode, headers, V2_CART_GIFT_VOUCHER.getUrl(params));
            if (response == null) {
                log.warn("Failed to apply voucher {} to empty cart", newVoucherCode);
            }
            return this;
        }
        
        // Case 2: Same voucher already exists - skip application
        if (existingVoucherCode.equals(newVoucherCode)) {
            log.info("Desired voucher {} is already applied - skipping application", newVoucherCode);
            return this;
        }
        
        // Case 3: Different voucher exists - remove old and apply new
        log.info("Found different voucher {} in cart, removing it first", existingVoucherCode);
        Map<String, String> removeParams = Map.of("giftVoucherCode", existingVoucherCode);
        response = GiftVoucherUtil.removeVoucher(existingVoucherCode, headers, V2_CART_GIFT_VOUCHER.getUrl(removeParams));
        if (response == null) {
            log.warn("Failed to remove existing voucher {} - skipping new voucher application", existingVoucherCode);
            return this;
        }
        
        // Verify removal
        cartDetailsHelper = (GetCartDetailsHelper)
                GetCartDetailsHelper.builder()
                        .orderContext(orderContext)
                        .build()
                        .test();
        cartJson = new JSONObject(cartDetailsHelper.response.asString());
        if (GiftVoucherUtil.getGiftVoucherCount(cartJson) > 0) {
            log.warn("Failed to remove existing voucher from cart - skipping new voucher application");
            return this;
        }

        // Apply new voucher
        log.info("Applying new voucher {}", newVoucherCode);
        Map<String, String> newParams = Map.of("giftVoucherCode", newVoucherCode);
        response = GiftVoucherUtil.tryApplyVoucher(newVoucherCode, headers, V2_CART_GIFT_VOUCHER.getUrl(newParams));
        if (response == null) {
            log.warn("Failed to apply new voucher {}", newVoucherCode);
            return this;
        }

        // Verify final state
        cartDetailsHelper = (GetCartDetailsHelper)
                GetCartDetailsHelper.builder()
                        .orderContext(orderContext)
                        .build()
                        .test();
        cartJson = new JSONObject(cartDetailsHelper.response.asString());
        
        if (!GiftVoucherUtil.verifyVoucherState(cartJson, newVoucherCode)) {
            log.warn("Final voucher state verification failed for {}", newVoucherCode);
        }

        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
} 
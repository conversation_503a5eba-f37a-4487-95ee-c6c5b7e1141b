package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.juno.requestbuilder.JunoOrderRequestBuilder;
import com.lenskart.juno.validator.payment.PaymentValidator;
import com.lenskart.juno.validator.order.OrderValidator;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.math.BigDecimal;
import java.util.List;

import static com.lenskart.juno.endpoints.JunoEndpoints.V2_ORDER_PAYMENT;

@SuperBuilder
@Slf4j
public class CreateOrderPaymentHelper extends JunoBaseHelper implements ServiceHelper {

    JSONObject payload;
    OrderContext orderContext;
    Response response;


    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext);
        payload = JunoOrderRequestBuilder.createPayloadForOrderPayment(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(V2_ORDER_PAYMENT.getUrl(), headers, payload.toString(), 200);
        // Check if response contains rollback error message
        String responseBody = response.getBody().asString();
        if (!responseBody.contains("Your order is rolled back")) {
            // Only set final order amount if there's no rollback message
            orderContext.setFinalOrderAmount(BigDecimal.valueOf((float) RestUtils.getValueFromResponse(response, "result.payment.amount.price")));
        } else {
            log.info("Order rollback detected, skipping price validation");
        }
        // Continue with rest of the order processing regardless of rollback
        orderContext.setOrderId((int) RestUtils.getValueFromResponse(response, "result.order.id"));
        log.info("Order created with id: {}", orderContext.getOrderId());
        List<OrderContext.ProductList> productLists = orderContext.getProductLists();
        int itemCount = 0;
        for (OrderContext.ProductList productList : productLists) {
            productList.setItemId((int) RestUtils.getValueFromResponse(response, "result.order.items["
                    .concat(String.valueOf(itemCount))
                    .concat("].id")));
            itemCount++;
        }
        return this;
    }

    @Override
    public ServiceHelper validate() {
        OrderValidator.builder()
                .orderContext(orderContext)
                .build()
                .validateNode();
        PaymentValidator.builder()
                .orderContext(orderContext)
                .build()
                .validateNode();
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

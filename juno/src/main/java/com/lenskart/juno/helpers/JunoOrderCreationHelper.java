package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.commons.model.Client;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.model.PaymentMethod;
import com.lenskart.commons.model.PowerTypes;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.util.List;


@SuperBuilder
@Slf4j
public class JunoOrderCreationHelper extends JunoBaseHelper implements ExecutionHelper {

    OrderContext orderContext;
    JSONObject payload;
    Response response;


    /*
     * method to create order
     * call all the apis in sequential order
     * and store intermediate state and headers in
     * the order context
     *  */


    @Override
    public ExecutionHelper init() {
        return this;
    }

    @Override
    public ExecutionHelper orchestrateFlow() {

        /* Authenticate User */
        AuthenticationHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        /* Search for product and get package id */
        GetProductAndPackageHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        /* Clear Cart */
        ClearCartHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        /* Create Cart */
        CreateCartHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        /* Add Shipping Address */
        AddShippingAddressHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        /* Create Order Payment */
        CreateOrderPaymentHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        /* Get Order Details */
        GetOrderDetailsHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        /* Confirm Order */
        if (orderContext.getPaymentMethod().equals(PaymentMethod.COD) && orderContext.isConfirmOrder() && orderContext.getHeaders().getClient().equals(Client.DESKTOP)) {
            ConfirmOrderHelper.builder()
                    .orderContext(orderContext)
                    .build()
                    .test();
        }

        /* Create Prescription */
        List<OrderContext.ProductList> productLists = orderContext.getProductLists();
        for (OrderContext.ProductList productList : productLists) {
            if (productList.isPrescriptionRequired() && (!productList.getPowerType().getDisplayName().equals(PowerTypes.ZERO_POWER.getDisplayName()))) {
                CreatePrescriptionHelper.builder()
                        .orderContext(orderContext)
                        .build()
                        .test();
            }
        }
        log.info("OrderId : {}", orderContext.getOrderId());
        return this;
    }

    @Override
    public ExecutionHelper test() {
        init();
        orchestrateFlow();
        return this;
    }


}

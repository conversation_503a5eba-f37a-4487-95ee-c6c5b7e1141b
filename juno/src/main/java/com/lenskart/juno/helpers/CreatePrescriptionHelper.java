package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.juno.requestbuilder.JunoOrderRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.util.Map;

import static com.lenskart.juno.endpoints.JunoEndpoints.V2_ORDERS_ITEMS_PRESCRIPTION;

@SuperBuilder
@Slf4j
public class CreatePrescriptionHelper extends JunoBaseHelper implements ServiceHelper {

    JSONObject payload;
    OrderContext orderContext;
    Response response;


    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext);
        payload = JunoOrderRequestBuilder.createPayloadForPrescription(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.put(V2_ORDERS_ITEMS_PRESCRIPTION.getUrl(Map.of("orderID", String.valueOf(orderContext.getOrderId()))),
                headers, payload.toString(), 204);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

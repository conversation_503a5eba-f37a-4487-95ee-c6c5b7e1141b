package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.juno.requestbuilder.JunoOrderRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.json.JSONObject;

import java.util.stream.IntStream;


import java.util.Map;

import static com.lenskart.juno.endpoints.JunoEndpoints.V2_ORDERS_ITEMS_PRESCRIPTION;

@SuperBuilder
@Slf4j
public class CreatePrescriptionHelper extends JunoBaseHelper implements ServiceHelper {
    private JSONObject payload;
    private OrderContext orderContext;
    private Response response;
    private String powerType;


    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        GetOrderDetailsHelper orderDetailsHelper = (GetOrderDetailsHelper) GetOrderDetailsHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();
        Response orderDetailsResponse = orderDetailsHelper.response;

        JSONObject orderResponse = new JSONObject(orderDetailsResponse.asString());
        log.info("Order response: {}", orderResponse);
        JSONArray items = orderResponse.getJSONObject("result").getJSONArray("items");

        IntStream.range(0, items.length()).forEach(i -> {

            int itemId = items.getJSONObject(i).getInt("id");
            if (items.getJSONObject(i).getString("powerRequired").equals("POWER_REQUIRED")) {
                String powerType = items.getJSONObject(i).getJSONObject("prescription").getString("powerType");
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                payload = JunoOrderRequestBuilder.createPayloadForPrescription(powerType);
                response = RestUtils.put(V2_ORDERS_ITEMS_PRESCRIPTION.getUrl(Map.of("orderID", String.valueOf(orderContext.getOrderId())
                                , "itemID", String.valueOf(itemId))),
                        headers, payload.toString(), 200);
            }

        });
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

package com.lenskart.juno.helpers;

import com.lenskart.juno.validator.session.SessionValidator;
import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import static com.lenskart.juno.endpoints.JunoEndpoints.V2_SESSIONS;


@SuperBuilder
@Slf4j
public class CreateSessionHelper extends JunoBaseHelper implements ServiceHelper {
    JSONObject payload;
    OrderContext orderContext;
    Response response;
    OrderContext.Headers orderContextHeader;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(orderContext);
        orderContextHeader = orderContext.getHeaders();
        return this;

    }

    @Override
    public ServiceHelper process() {


        response = RestUtils.post(V2_SESSIONS.getUrl(), headers, null, 200);
        orderContextHeader.setSessionToken((String) RestUtils.getValueFromResponse(response, "result.id"));



        return this;
    }

    @Override
    public ServiceHelper validate() {
        SessionValidator.builder()
                .orderContext(orderContext)
                .build()
                .validateNode();
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }}
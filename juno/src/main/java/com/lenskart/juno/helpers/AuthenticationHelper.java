package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.constants.HeaderMapper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.juno.requestbuilder.JunoOrderRequestBuilder;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import static com.lenskart.juno.endpoints.JunoEndpoints.V2_CUSTOMERS_AUTHENTICATE_MOBILE;
import static com.lenskart.juno.endpoints.JunoEndpoints.V2_SESSIONS;


@SuperBuilder
@Slf4j
public class AuthenticationHelper extends JunoBaseHelper implements ServiceHelper {

    JSONObject payload;
    OrderContext orderContext;
    Response response;
    OrderContext.Headers orderContextHeader;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(orderContext);
        payload = JunoOrderRequestBuilder.createPayloadForSession(orderContext);
        orderContextHeader = orderContext.getHeaders();
        return this;
    }

    @Override
    public ServiceHelper process() {

        /* Fetch session Token */
        response = RestUtils.post(V2_SESSIONS.getUrl(), headers, payload.toString(), 200);
        orderContextHeader.setSessionToken((String) RestUtils.getValueFromResponse(response, "result.id"));


        /* Authenticate Mobile */
        headers.put(HeaderMapper.X_SESSION_TOKEN.getHeaderName(), orderContext.getHeaders().getSessionToken());
        response = RestUtils.post(V2_CUSTOMERS_AUTHENTICATE_MOBILE.getUrl(), headers, payload.toString(), 200);
            orderContextHeader.setSessionToken((String) RestUtils.getValueFromResponse(response, "result.token"));

        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

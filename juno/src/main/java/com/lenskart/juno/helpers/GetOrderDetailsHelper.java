package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.juno.util.OrderUtil;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

import static com.lenskart.juno.endpoints.JunoEndpoints.V2_GET_ORDER_DETAILS;

@SuperBuilder
@Slf4j
public class GetOrderDetailsHelper extends JunoBaseHelper implements ServiceHelper {
    OrderContext orderContext;
    Response response;

    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(V2_GET_ORDER_DETAILS.getUrl(Map.of("orderID", String.valueOf(orderContext.getOrderId()))), headers, null, 200);
        orderContext.setConfirmOrder(OrderUtil.setConfirmOrder(response));
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }

}

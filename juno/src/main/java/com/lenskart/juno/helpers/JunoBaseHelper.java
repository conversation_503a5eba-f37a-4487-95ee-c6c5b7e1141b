package com.lenskart.juno.helpers;

import com.lenskart.commons.base.BaseHelper;
import com.lenskart.commons.constants.HeaderMapper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.model.PowerTypes;
import com.lenskart.juno.exceptions.JunoExceptionStates;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

@SuperBuilder
@Slf4j
public class JunoBaseHelper extends BaseHelper<JunoExceptionStates, Object> {


    public Map<String, String> getHeaders(OrderContext orderContext) {
        headers = new HashMap<>();
        headers.put(HeaderMapper.X_COUNTRY_CODE.getHeaderName(), orderContext.getCountryCodeMapper().getCountry().name());
        headers.put(HeaderMapper.X_API_CLIENT.getHeaderName(), orderContext.getHeaders().getClient().getDisplayName());
        return headers;
    }

    public Map<String, String> getHeadersWithSessionToken(OrderContext orderContext) {
        headers = getHeaders(orderContext);
        headers.put(HeaderMapper.X_SESSION_TOKEN.getHeaderName(), orderContext.getHeaders().getSessionToken());
        return headers;
    }

    public Map<String, String> getHeadersForAdminAPI(OrderContext orderContext) {
        headers = getHeaders(orderContext);
        headers.put(HeaderMapper.X_SESSION_TOKEN.getHeaderName(), orderContext.getHeaders().getSessionToken());
        headers.put(HeaderMapper.X_AUTH_TOKEN.getHeaderName(), "123");
        return headers;
    }

    public Map<String, Object> getQueryParams(String frameType, PowerTypes powerType) {
        queryParams = new HashMap<>();
        queryParams.put("frame_type", frameType);
        queryParams.put("power_type", Objects.isNull(powerType) ? PowerTypes.SINGLE_VISION.getDisplayName() : powerType.getDisplayName());
        return queryParams;
    }

    public Map<String, Object> getQueryParamsForAdminAPI(OrderContext orderContext) {
        queryParams = new HashMap<>();
        queryParams.put("phoneCode", orderContext.getCountryCodeMapper().getCountry().getDialingCode());
        queryParams.put("mobile", orderContext.getPhoneNumber());
        return queryParams;
    }


}

package com.lenskart.juno.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.model.PowerTypes;
import com.lenskart.commons.model.ProductTypes;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.commons.utils.ProductUtil;
import com.lenskart.juno.schema.product.BuyPackagesResponse;
import com.lenskart.juno.schema.product.ProductResult;
import io.restassured.response.Response;
import lombok.SneakyThrows;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;

import java.util.List;
import java.util.Map;

import static com.lenskart.commons.constants.Constants.PRODUCT_ID;
import static com.lenskart.commons.constants.Constants.RESULT;
import static com.lenskart.juno.endpoints.JunoEndpoints.GET_PACKAGE_DETAILS;
import static com.lenskart.juno.endpoints.JunoEndpoints.GET_PRODUCT_DETAILS;


@SuperBuilder
@Slf4j
public class GetProductAndPackageHelper extends JunoBaseHelper implements ServiceHelper {

    JSONObject payload;
    OrderContext orderContext;
    Response response;
    ProductResult productResult;
    BuyPackagesResponse buyPackagesResponse;

    @SneakyThrows
    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext);
        statusCode = orderContext.getStatusCode();
        return this;
    }

    @Override
    public ServiceHelper process() {

        if (orderContext.getProductLists().isEmpty()) {
            throw new RuntimeException("No products added to the order context");
        } else {
            List<OrderContext.ProductList> productLists = orderContext.getProductLists();
            for (OrderContext.ProductList productList : productLists) {

                // get product details
                response = RestUtils.get(GET_PRODUCT_DETAILS.getUrl(Map.of(PRODUCT_ID, productList.getProductId())),
                        headers, null, 200);
                productResult = parseResponse(RestUtils.getValueFromResponse(response, RESULT), ProductResult.class);

                // Set Frame type from technical specifications
                ProductUtil.setFrameType(productList, productResult.getSpecifications());
                productList.setProductType(ProductTypes.getByDisplayName(productResult.getType()));
                log.info("Product type: {}", productList.getProductType());

                // query param to fetch package details
                if (productList.getPowerType() != null && (productList.getPowerType().getDisplayName().equalsIgnoreCase(PowerTypes.ZERO_POWER.getDisplayName()) ||
                        productList.getPowerType().getDisplayName().equalsIgnoreCase(PowerTypes.SUNGLASSES.getDisplayName()) ||
                        productList.getPowerType().getDisplayName().equalsIgnoreCase(PowerTypes.SINGLE_VISION.getDisplayName()) ||
                        productList.getPowerType().getDisplayName().equalsIgnoreCase(PowerTypes.BIFOCAL.getDisplayName()))) {

                    queryParams = getQueryParams(productList.getFrameType().getQueryName(), productList.getPowerType());
                    response = RestUtils.get(GET_PACKAGE_DETAILS.getUrl(Map.of(PRODUCT_ID, productList.getProductId())),
                            headers, queryParams, 200);
                    buyPackagesResponse = parseResponse(RestUtils.getValueFromResponse(response, RESULT), BuyPackagesResponse.class);

                    // set package id
                    if (buyPackagesResponse.getPackages().isEmpty()) {
                        throw new RuntimeException("No packages found for product id: " + productList.getProductId());
                    } else {
                        // we are always defaulting to first package id
                        productList.setPackageId(buyPackagesResponse.getPackages().get(0).getId());
                    }
                }
            }
        }
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

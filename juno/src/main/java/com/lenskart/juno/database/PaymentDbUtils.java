package com.lenskart.juno.database;

import com.lenskart.commons.database.mongodb.MongoDBQueryExecutor;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;

@Builder
@Slf4j
public class PaymentDbUtils {

    public static Document getPaymentDetails(String orderId){
        Document data = MongoDBQueryExecutor.findOne(
                "juno_cluster",
                "juno_v2_preprod_payment",
                "payment_v2",
                new Document("orderId", orderId)
        );
        log.info("Data: {}", data);
        return data;
    }
}
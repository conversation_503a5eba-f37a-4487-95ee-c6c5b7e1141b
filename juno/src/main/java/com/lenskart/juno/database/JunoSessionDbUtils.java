package com.lenskart.juno.database;

import com.lenskart.commons.database.mongodb.MongoDBQueryExecutor;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;

import java.util.List;

@Builder
@Slf4j
public class JunoSessionDbUtils
{

    public static Document getSessionDetails(String sessionToken){
   Document data = MongoDBQueryExecutor.findOne(
            "juno_cluster",
            "juno_v2_preprod_session_new",
            "session_v2",
            new Document("_id", sessionToken)
    );
    log.info("Data: {}", data);
    return data;
}
}
package com.lenskart.juno.database;

import com.lenskart.commons.database.mongodb.MongoDBQueryExecutor;
import com.lenskart.commons.model.OrderContext;
import groovy.transform.builder.Builder;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;

import java.util.List;
@Builder
@Slf4j
public class JunoMongoDbUtils {

    public static List<Document> getQuantityFromActiveInventoryForPid(int pid, String facilityCode){
        List<Document> data = MongoDBQueryExecutor.find(
                "juno_cluster",
                "catalog",
                "active_inventory",
                new Document("productId", pid).append("facilityCode",facilityCode)
        );
        log.info("Data: {}", data);
        return data;
    }
}

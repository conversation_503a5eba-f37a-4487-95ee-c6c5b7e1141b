package com.lenskart.juno.endpoints;

import com.lenskart.commons.endpoints.EndpointManager;
import com.lenskart.juno.config.JunoConfigProvider;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * Endpoint manager for Juno module that provides URL generation
 * and management for all Juno endpoints.
 */
@Slf4j
public class JunoEndpointManager extends EndpointManager<JunoEndpoints> {

    // Singleton instance
    private static volatile JunoEndpointManager instance;

    /**
     * Private constructor for singleton pattern
     */
    private JunoEndpointManager() {
        super(JunoConfigProvider.getInstance(), JunoEndpoints.class);
        log.info("JunoEndpointManager initialized");
    }

    /**
     * Gets the singleton instance of JunoEndpointManager
     *
     * @return The singleton instance
     */
    public static JunoEndpointManager getInstance() {
        if (instance == null) {
            synchronized (JunoEndpointManager.class) {
                if (instance == null) {
                    instance = new JunoEndpointManager();
                }
            }
        }
        return instance;
    }

    /**
     * Convenience method to get URL for a Juno endpoint
     *
     * @param endpoint The Juno endpoint
     * @return Complete URL for the endpoint
     */
    public static String getEndpointUrl(JunoEndpoints endpoint) {
        return getInstance().getUrl(endpoint);
    }

    /**
     * Convenience method to get URL for a Juno endpoint with path parameters
     *
     * @param endpoint The Juno endpoint
     * @param pathParams Map of path parameters to replace in the URL
     * @return Complete URL with path parameters replaced
     */
    public static String getEndpointUrl(JunoEndpoints endpoint, Map<String, String> pathParams) {
        return getInstance().getUrl(endpoint, pathParams);
    }

    /**
     * Convenience method to refresh all Juno endpoint URLs
     */
    public static void refreshEndpoints() {
        getInstance().refresh();
    }

    /**
     * Convenience method to validate all Juno endpoints
     *
     * @return true if all endpoints are valid, false otherwise
     */
    public static boolean validateEndpoints() {
        return getInstance().validateEndpoints();
    }

    /**
     * Convenience method to get all Juno endpoint URLs
     *
     * @return Map of endpoint names to their full URLs
     */
    public static Map<String, String> getAllEndpointUrls() {
        return getInstance().getAllUrls();
    }
}

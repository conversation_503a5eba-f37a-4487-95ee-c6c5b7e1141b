package com.lenskart.juno.endpoints;

import com.lenskart.commons.endpoints.BaseEndpoint;
import lombok.Getter;

import java.util.Map;

@Getter
public enum JunoEndpoints implements BaseEndpoint {

    V2_SESSIONS("/v2/sessions", "sessionService"),
    V2_CUSTOMERS_AUTHENTICATE_MOBILE("/v2/customers/authenticate/mobile/", "sessionService"),
    V2_CART("/v2/carts/", "sessionService"),
    V2_CART_ITEMS("/v2/carts/items", "sessionService"),
    V2_CARTS_SHIPPING_ADDRESS("/v2/carts/shippingAddress/", "sessionService"),
    V2_ORDER_PAYMENT("/v2/orderpayment/", "sessionService"),
    V2_ORDERS_ITEMS_PRESCRIPTION("/v2/orders/{$orderID}/items/{$itemID}/prescription", "sessionService"),
    V2_CONFIRM_ORDER("/v2/orders/{$orderID}/confirm", "sessionService"),
    V2_GET_ORDER_DETAILS("/v2/orders/{$orderID}", "sessionService"),

    GET_PRODUCT_DETAILS("/v2/products/product/{$productID}", "sessionService"),
    GET_PACKAGE_DETAILS("/v2/products/product/{$productID}/packages", "sessionService"),

    GET_CUSTOMER_DETAILS("/v2/customers/admin", "sessionService");


    private final String endpoint;
    private final String serviceName;

    JunoEndpoints(String endpoint, String serviceName) {
        this.endpoint = endpoint;
        this.serviceName = serviceName;
    }

    @Override
    public String getUrl() {
        return JunoEndpointManager.getEndpointUrl(this);
    }

    @Override
    public String getUrl(Map<String, String> pathParams) {
        return JunoEndpointManager.getEndpointUrl(this, pathParams);
    }

}

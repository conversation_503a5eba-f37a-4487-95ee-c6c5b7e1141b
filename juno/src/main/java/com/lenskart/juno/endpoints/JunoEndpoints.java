package com.lenskart.juno.endpoints;

import com.lenskart.commons.endpoints.BaseEndpoint;
import lombok.Getter;

import java.util.Map;

@Getter
public enum JunoEndpoints {

    V2_SESSIONS("/v2/sessions", "sessionService"),
    V2_CUSTOMERS_AUTHENTICATE_MOBILE("/v2/customers/authenticate/mobile/", "sessionService"),
    V2_CART("/v2/carts/", "sessionService"),
    V2_CART_ITEMS("/v2/carts/items", "sessionService"),
    V2_CARTS_SHIPPING_ADDRESS("/v2/carts/shippingAddress/", "sessionService"),
    V2_ORDER_PAYMENT("/v2/orderpayment/", "sessionService"),
    V2_ORDERS_ITEMS_PRESCRIPTION("/v2/orders/{$orderID}/items/175986/prescription", "sessionService"),

    GET_PRODUCT_DETAILS("/v2/products/product/{$productID}", "sessionService"),
    GET_PACKAGE_DETAILS("/v2/products/product/{$productID}/packages", "sessionService");

    private final String endpoint;
    private final String serviceName;

    // Static maps to store base URLs and full URLs
    private static final Map<String, String> serviceBaseUrls = new HashMap<>();
    private static final Map<JunoEndpoints, String> fullUrls = new HashMap<>();

    // Flag to track if initialization has been done
    private static boolean initialized = false;

    // Static initialization block to load all base URLs once
    static {
        initializeUrls();
    }

    JunoEndpoints(String endpoint, String serviceName) {
        this.endpoint = endpoint;
        this.serviceName = serviceName;
    }

    /**
     * Initializes all URLs by loading the configuration once
     */
    private static synchronized void initializeUrls() {
        if (initialized) {
            return;
        }

        // Pre-load all service base URLs
        for (JunoEndpoints endpoint : values()) {
            if (!serviceBaseUrls.containsKey(endpoint.serviceName)) {
                // Get the base URL from JunoConfigRegistry
                String baseUrl = JunoConfigRegistry.getInstance().getBaseUrl(endpoint.serviceName);

                if (baseUrl == null) {
                    throw new IllegalStateException("Base URL not found for service: " + endpoint.serviceName);
                }

                // Remove trailing slash from base URL if present
                if (baseUrl.endsWith("/")) {
                    baseUrl = baseUrl.substring(0, baseUrl.length() - 1);
                }

                serviceBaseUrls.put(endpoint.serviceName, baseUrl);
            }
        }

        // Pre-compute all full URLs
        for (JunoEndpoints endpoint : values()) {
            String baseUrl = serviceBaseUrls.get(endpoint.serviceName);

            // Ensure endpoint starts with a slash
            String path = endpoint.endpoint;
            if (!path.startsWith("/")) {
                path = "/" + path;
            }

            // Store the full URL
            fullUrls.put(endpoint, baseUrl + path);
        }

        initialized = true;
    }

    /**
     * Gets the complete URL for this endpoint
     *
     * @return Complete URL for the endpoint
     */
    public String getUrl() {
        // Ensure URLs are initialized
        if (!initialized) {
            initializeUrls();
        }

        // Return the pre-computed URL
        return fullUrls.get(this);
    }

    /**
     * Gets the complete URL for this endpoint with path parameters replaced
     *
     * @param pathParams Map of path parameters to replace in the URL
     * @return Complete URL with path parameters replaced
     */
    public String getUrl(Map<String, String> pathParams) {
        String url = getUrl();

        if (pathParams != null) {
            for (Map.Entry<String, String> entry : pathParams.entrySet()) {
                url = url.replace("{$" + entry.getKey() + "}", entry.getValue());
            }
        }

        return url;
    }

    /**
     * Refreshes the base URLs from the configuration
     * Call this method if the configuration has changed
     */
    public static void refreshUrls() {
        // Refresh the JunoConfigRegistry
        JunoConfigRegistry.getInstance().refresh();

        // Clear our caches
        serviceBaseUrls.clear();
        fullUrls.clear();
        initialized = false;

        // Re-initialize
        initializeUrls();
    }
}

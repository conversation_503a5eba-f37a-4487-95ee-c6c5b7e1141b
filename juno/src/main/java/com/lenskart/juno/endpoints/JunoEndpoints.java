package com.lenskart.juno.endpoints;

import com.lenskart.commons.endpoints.BaseEndpoint;
import lombok.Getter;

import java.util.Map;

@Getter
public enum JunoEndpoints implements BaseEndpoint {

    V2_SESSIONS("/v2/sessions", "sessionService"),
    V2_CUSTOMERS_AUTHENTICATE_MOBILE("/v2/customers/authenticate/mobile/", "sessionService"),
    V2_CART("/v2/carts/", "sessionService"),
    V2_CART_ITEMS("/v2/carts/items", "sessionService"),
    V2_CART_ESTIMATIONS("/v2/carts/estimations", "sessionService"),
    V2_CARTS_SHIPPING_ADDRESS("/v2/carts/shippingAddress/", "sessionService"),
    V2_CART_GIFT_VOUCHER("/v2/carts/giftVoucher/{$giftVoucherCode}", "sessionService"),
    V2_ORDER_PAYMENT("/v2/orderpayment/", "sessionService"),
    V2_ORDERS_ITEMS_PRESCRIPTION("/v2/orders/{$orderID}/items/{$itemID}/prescription", "sessionService"),
    V2_CARTS_ITEMS_PRESCRIPTION("/v2/carts/items/{$itemID}","sessionService"),
    V2_CONFIRM_ORDER("/v2/orders/{$orderID}/confirm", "sessionService"),
    V2_GET_ORDER_DETAILS("/v2/orders/{$orderID}", "sessionService"),
    V2_CANCEL_ORDER("/v2/orders/{$orderID}/cancel", "sessionService"),
    V2_ORDER_SYNC_STATUS("/v2/orders/{$orderID}/sync/status", "sessionService"),

    GET_PRODUCT_DETAILS("/v2/products/product/{$productID}", "sessionService"),
    GET_PACKAGE_DETAILS("/v2/products/product/{$productID}/packages", "sessionService"),
    GET_REVIEW("/v2/products/product/{$productID}", "sessionService"),
    GET_CUSTOMER_DETAILS("/v2/customers/admin", "sessionService"),
    GET_CUSTOMER_DETAILS_PLAIN("/v2/customers/me/plain", "sessionService"),
    ATHENA_NOTIFICATION_BULK("/v2/orders/athena-notification-bulk", "sessionService"),
    GET_CASHBACK_DETAILS("/v2/customers/membership/cashbackSummary", "sessionService");



    private final String endpoint;
    private final String serviceName;

    JunoEndpoints(String endpoint, String serviceName) {
        this.endpoint = endpoint;
        this.serviceName = serviceName;
    }

    @Override
    public String getUrl() {
        return JunoEndpointManager.getEndpointUrl(this);
    }

    @Override
    public String getUrl(Map<String, String> pathParams) {
        return JunoEndpointManager.getEndpointUrl(this, pathParams);
    }

}
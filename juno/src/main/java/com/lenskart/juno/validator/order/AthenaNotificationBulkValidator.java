package com.lenskart.juno.validator.order;

import com.lenskart.commons.base.IValidator;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.juno.database.JunoCommonDbUtils;
import com.lenskart.juno.helpers.AthenaNotificationBulkHelper;
import lombok.extern.slf4j.Slf4j;
import lombok.Builder;
import org.bson.Document;
import org.json.JSONObject;
import org.testng.asserts.SoftAssert;

import java.time.Duration;
import java.util.Objects;

@Builder
@Slf4j
public class AthenaNotificationBulkValidator implements IValidator {

    OrderContext orderContext;

    @Override
    public void validateNode() {
        validateDBEntities();
    }

    @Override
    public void validateDBEntities() {
        boolean success = AwaitUtils.retryOperation(
                () -> {
                    try {
                        Document document = JunoCommonDbUtils.getAthenaOrderLogDetails(orderContext.getOrderId());
                        JSONObject result = null;
                        if (Objects.nonNull(document)) {
                            result = JsonUtils.convertStringToJson(document.toJson());
                        }
                        log.info("Athena Notification Bulk Document: {}", document);
                        log.info("Athena Notification Bulk Json: {}", result);
                        if (document.isEmpty()) {
                            AthenaNotificationBulkHelper.builder()
                                    .orderContext(orderContext)
                                    .build()
                                    .test();
                            return false;
                        } else if (result.getString("orderSyncStage").equals("SYNCED")) {
                            return true;
                        } else {
                            AthenaNotificationBulkHelper.builder()
                                    .orderContext(orderContext)
                                    .build()
                                    .test();
                            return false;
                        }
                    } catch (Exception e) {
                        return false;
                    }
                },
                "Retry Athena Notification Bulk",
                2,
                Duration.ofSeconds(4)
        );
        if (success) {
            log.info("Successfully pushed the order to SCM");
        }
    }
}

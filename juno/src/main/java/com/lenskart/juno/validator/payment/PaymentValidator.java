package com.lenskart.juno.validator.Payment;

import com.lenskart.commons.base.IValidator;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.juno.database.PaymentDbUtils;
import com.lenskart.juno.helpers.GetOrderDetailsHelper;
import io.restassured.response.Response;
import lombok.Builder;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.testng.Assert;
import org.testng.asserts.SoftAssert;

import java.util.List;

@Slf4j
@Builder
public class PaymentValidator implements IValidator {

    static SoftAssert softAssert = new SoftAssert();
    OrderContext orderContext;

    @Override
    public void validateNode() {
        validateDBEntities();
    }

    @Override
    public void validateDBEntities() {
        String orderId = String.valueOf(orderContext.getOrderId());

        // Get payment details from database
        Document paymentDetails = PaymentDbUtils.getPaymentDetails(orderId);

        // Fail fast if paymentDetails is null or empty
        Assert.assertNotNull(paymentDetails, "Payment details should not be null for order ID: " + orderId);

        if (paymentDetails.isEmpty()) {
            log.error("Payment details are empty for order ID: {}", orderId);
            Assert.fail("Payment details should not be empty for order ID: " + orderId);
        }

        // Get order details from API
        GetOrderDetailsHelper orderDetailsHelper = (GetOrderDetailsHelper)
                GetOrderDetailsHelper.builder()
                        .orderContext(orderContext)
                        .build()
                        .test();

        // Extract API response values
        List<String> apiPaymentMethods = orderDetailsHelper.getResponse().jsonPath().getList("result.payments.paymentList.method");
        log.info("API Payment Response - Payment Methods: {}", apiPaymentMethods);

        // Validate payment list length
        softAssert.assertEquals(apiPaymentMethods.size(), 1,
                String.format("Expected exactly one payment method, but found %d methods in API response", apiPaymentMethods.size()));

        // Extract database values
        Document paymentGatewayInfo = (Document) paymentDetails.get("paymentGatewayInfo");
        Assert.assertNotNull(paymentGatewayInfo, "paymentGatewayInfo should not be null in payment details for order ID: " + orderId);

        String dbPaymentMethod = paymentGatewayInfo.getString("paymentMethod").toLowerCase();
        log.info("DB Payment Details - Payment Method: {}", dbPaymentMethod);

        // Validate if the DB payment method exists in API response
        softAssert.assertTrue(
                apiPaymentMethods.stream()
                        .map(String::toLowerCase)
                        .anyMatch(apiMethod -> apiMethod.equals(dbPaymentMethod)),
                String.format("Payment method '%s' from DB not found in API payment methods: %s",
                        dbPaymentMethod, apiPaymentMethods)
        );

        softAssert.assertAll();
        log.info("Payment validation completed successfully for order ID: {}", orderId);
    }
} 
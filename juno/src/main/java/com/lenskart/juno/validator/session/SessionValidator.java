package com.lenskart.juno.validator.Session;

import com.lenskart.commons.base.IValidator;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.juno.database.JunoSessionDbUtils;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.testng.Assert;
import org.testng.asserts.SoftAssert;

@Slf4j
@Builder
public class SessionValidator implements IValidator {

    static SoftAssert softAssert = new SoftAssert();
    OrderContext orderContext;

    @Override
    public void validateNode() {
        validateDBEntities();
    }

    @Override
    public void validateDBEntities() {
        String sessionToken = orderContext.getHeaders().getSessionToken();

        // Get session details from database
        Document sessionDetails = JunoSessionDbUtils.getSessionDetails(sessionToken);

        // Fail fast if sessionDetails is null or empty
        Assert.assertNotNull(sessionDetails, "Session details should not be null for session token: " + sessionToken);

        if (sessionDetails.isEmpty()) {
            log.error("Session details are empty for session token: {}", sessionToken);
            Assert.fail("Session details should not be empty for session token: " + sessionToken);
        }

        // Extract database values
        String dbSessionId = sessionDetails.get("_id").toString();
        String expectedSessionToken = sessionToken;

        log.info("Session Details - DB Session ID: {}, Expected Session Token: {}", 
                dbSessionId, expectedSessionToken);

        // Validate session ID
        softAssert.assertEquals(
                dbSessionId,
                expectedSessionToken,
                "Session ID in DB does not match session token from request"
        );

        softAssert.assertAll();
        log.info("Session validation completed successfully for session token: {}", sessionToken);
    }
} 
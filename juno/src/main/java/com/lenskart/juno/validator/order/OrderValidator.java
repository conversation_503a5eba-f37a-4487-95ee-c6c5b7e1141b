package com.lenskart.juno.validator.order;

import com.lenskart.commons.base.IValidator;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.AwaitUtils;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.juno.database.JunoCommonDbUtils;
import com.lenskart.juno.database.JunoOrderDBUtils;
import com.lenskart.juno.database.PaymentDbUtils;
import com.lenskart.juno.helpers.AthenaNotificationBulkHelper;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.json.JSONObject;
import org.testng.Assert;


import java.util.Objects;

@Builder
@Slf4j
public class OrderValidator implements IValidator {

    OrderContext orderContext;

    @Override
    public void validateNode() {
        validateDBEntities();
    }

    @Override
    public void validateDBEntities() {
        JSONObject orderJson = fetchOrderDetails(orderContext.getOrderId());
        validateOrderId(orderJson);

        Document paymentDetails = fetchPaymentDetails(String.valueOf(orderContext.getOrderId()));
        validatePaymentDetails(paymentDetails);
    }

    private JSONObject fetchOrderDetails(long orderId) {
        Document orderDocument = JunoOrderDBUtils.getOrderDetails(orderContext.getOrderId());
        if (orderDocument == null || orderDocument.isEmpty()) {
            throw new IllegalStateException("Order document is null or empty for orderId: " + orderId);
        }
        JSONObject orderJson = JsonUtils.convertStringToJson(orderDocument.toJson());
        return orderJson;
    }

    private void validateOrderId(JSONObject orderJson) {
        String expectedOrderId = String.valueOf(orderContext.getOrderId());
        String dbOrderId = orderJson.optString("_id");
        Assert.assertEquals(expectedOrderId, dbOrderId, "OrderId is not matching");
    }

    private Document fetchPaymentDetails(String orderId) {
        Document paymentDetails = PaymentDbUtils.getPaymentDetails(orderId);
        if (paymentDetails == null || paymentDetails.isEmpty()) {
            throw new IllegalStateException("Payment details document is null or empty for orderId: " + orderId);
        }
        log.info("Payment Details Document: {}", paymentDetails);
        return paymentDetails;
    }

    private void validatePaymentDetails(Document paymentDetails) {
        Document paymentGatewayInfo = (Document) paymentDetails.get("paymentGatewayInfo");
        Assert.assertNotNull(paymentGatewayInfo, "paymentGatewayInfo is missing in payment details");

        String paymentMethodExpected = paymentGatewayInfo.getString("paymentMethod");
        String paymentMethodActual = orderContext.getPaymentMethod().getDisplayName();

        Assert.assertEquals(paymentMethodExpected, paymentMethodActual, "Payment method Mismatch");
    }

    private void validateCartId(JSONObject orderJson) {
        Object dbCartIdObj = orderJson.opt("cartId");
        Assert.assertNotNull(dbCartIdObj, "cartId is missing in DB");
        String dbCartId = String.valueOf(dbCartIdObj);
        String expectedCartId = String.valueOf(orderContext.getCartId());
        Assert.assertEquals(expectedCartId, dbCartId, "CartId is not matching");
    }
}
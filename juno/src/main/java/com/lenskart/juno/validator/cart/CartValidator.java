package com.lenskart.juno.validator.cart;

import com.lenskart.commons.base.IValidator;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.juno.database.JunoCartDbUtils;
import com.lenskart.juno.helpers.GetCartDetailsHelper;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.testng.Assert;
import org.testng.asserts.SoftAssert;

import java.util.List;

@Slf4j
@Builder
public class CartValidator implements IValidator {

    static SoftAssert softAssert = new SoftAssert();
    OrderContext orderContext;

    @Override
    public void validateNode() {
        validateDBEntities();
    }

    @Override
    public void validateDBEntities() {
        int cartId = orderContext.getCartId();

        // Get cart details from database
        Document cartDetails = JunoCartDbUtils.getCartDbUtils(cartId);

        // Fail fast if cartDetails is null or empty
        Assert.assertNotNull(cartDetails, "Cart details should not be null for cart ID: " + cartId);

        if (cartDetails.isEmpty()) {
            log.error("Cart details are empty for cart ID: {}", cartId);
            Assert.fail("Cart details should not be empty for cart ID: " + cartId);
        }

        // Get cart details from API
        GetCartDetailsHelper cartDetailsHelper = (GetCartDetailsHelper)
                GetCartDetailsHelper.builder()
                        .orderContext(orderContext)
                        .build()
                        .test();

        // Extract API response values as strings to avoid type casting issues
        String apiCartId = String.valueOf(RestUtils.getValueFromResponse(cartDetailsHelper.getResponse(), "result.cartId"));
        String apiCustomerId = String.valueOf(RestUtils.getValueFromResponse(cartDetailsHelper.getResponse(), "result.customerId"));
        String apiItemsCount = String.valueOf(RestUtils.getValueFromResponse(cartDetailsHelper.getResponse(), "result.itemsCount"));
        String apiCurrencyCode = (String) RestUtils.getValueFromResponse(cartDetailsHelper.getResponse(), "result.currencyCode");

        log.info("API Cart Response - cartId: {}, customerId: {}, itemsCount: {}, currencyCode: {}",
                apiCartId, apiCustomerId, apiItemsCount, apiCurrencyCode);

        // Extract database values
        String dbCartId = String.valueOf(cartDetails.get("_id"));
        String dbCustomerId = String.valueOf(cartDetails.get("customerId"));
        String dbItemsCount = String.valueOf(cartDetails.get("itemsCount"));
        String dbCurrencyCode = String.valueOf(cartDetails.get("currencyCode"));

        log.info("DB Cart Details - cartId: {}, customerId: {}, itemsCount: {}, currencyCode: {}",
                dbCartId, dbCustomerId, dbItemsCount, dbCurrencyCode);

        // Validate basic cart details
        softAssert.assertEquals(
                apiCartId,
                dbCartId,
                "Cart ID from API does not match cart ID from database"
        );

        softAssert.assertEquals(
                apiCustomerId,
                dbCustomerId,
                "Customer ID from API does not match customer ID from database"
        );

        softAssert.assertEquals(
                apiItemsCount,
                dbItemsCount,
                "Items count from API does not match items count from database"
        );

        softAssert.assertEquals(
                apiCurrencyCode,
                dbCurrencyCode,
                "Currency code from API does not match currency code from database"
        );

        // Extract and validate voucher details
        List<String> apiApplicableGvCodes = (List<String>) RestUtils.getValueFromResponse(cartDetailsHelper.getResponse(), "result.applicableGvs.code");
        List<String> apiExecutedRules = (List<String>) RestUtils.getValueFromResponse(cartDetailsHelper.getResponse(), "result.executedRules");
        
        log.info("API Voucher Details - applicableGvs: {}, executedRules: {}",
                apiApplicableGvCodes, apiExecutedRules);

        List<String> dbApplicableGvCodes = cartDetails.getList("applicableGvs", Document.class)
                .stream()
                .map(doc -> doc.getString("code"))
                .toList();
        List<String> dbExecutedRules = cartDetails.getList("executedRules", String.class);


        log.info("DB Voucher Details - applicableGvs: {}, executedRules: {},",
                dbApplicableGvCodes, dbExecutedRules);

        // Validate voucher details
        softAssert.assertEquals(
            apiApplicableGvCodes,
            dbApplicableGvCodes,
            "Applicable GV codes from API do not match DB"
        );

        softAssert.assertEquals(
            apiExecutedRules,
            dbExecutedRules,
            "Executed rules from API do not match DB"
        );


        softAssert.assertAll();
        log.info("Cart validation completed successfully for cart ID: {}", cartId);
    }
}

package com.lenskart.juno.util;

import com.lenskart.commons.model.GiftVoucher;
import com.lenskart.commons.utils.RestUtils;
import io.restassured.response.Response;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONArray;
import org.json.JSONObject;
import org.testng.Assert;

import java.util.Map;

@Slf4j
public class GiftVoucherUtil {

    /**
     * Gets the first applicable gift voucher code from the cart response
     *
     * @param cartJson The cart response JSON
     * @return The first gift voucher code if found, null otherwise
     */
    public static String getFirstApplicableVoucher(JSONObject cartJson) {
        try {
            if (!cartJson.has("result") || !cartJson.getJSONObject("result").has("cartId")) {
                log.debug("Cart response missing required fields: result.cartId");
                return null;
            }

            JSONObject cart = cartJson.getJSONObject("result").getJSONObject("cart");
            if (!cart.has("giftVouchers") || cart.getJSONArray("giftVouchers").isEmpty()) {
                log.debug("No gift vouchers found in cart");
                return null;
             }

            JSONArray giftVouchers = cart.getJSONArray("giftVouchers");
            JSONObject firstVoucher = giftVouchers.getJSONObject(0);
            
            if (!firstVoucher.has("code")) {
                log.warn("Gift voucher missing code field");
                return null;
            }

            String voucherCode = firstVoucher.getString("code");
            log.debug("Found gift voucher code: {}", voucherCode);
            return voucherCode;

        } catch (Exception e) {
            log.warn("Error parsing cart response for gift vouchers: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Checks if a specific gift voucher code is applied to the cart
     * 
     * @param cartJson The cart response JSON
     * @param voucherCode The voucher code to check for
     * @return true if the voucher is applied, false otherwise
     */
    public static boolean isGiftVoucherApplied(JSONObject cartJson, String voucherCode) {
        try {
            if (!cartJson.has("result") || !cartJson.getJSONObject("result").has("cart")) {
                log.debug("Cart response missing required fields: result.cart");
                return false;
            }

            JSONObject cart = cartJson.getJSONObject("result").getJSONObject("cart");
            if (!cart.has("giftVouchers")) {
                log.debug("No gift vouchers array in cart");
                return false;
            }

            JSONArray giftVouchers = cart.getJSONArray("giftVouchers");
            for (int i = 0; i < giftVouchers.length(); i++) {
                JSONObject voucher = giftVouchers.getJSONObject(i);
                if (voucher.has("code") && voucherCode.equals(voucher.getString("code"))) {
                    log.debug("Found matching gift voucher: {}", voucherCode);
                    return true;
                }
            }

            log.debug("Gift voucher {} not found in cart", voucherCode);
            return false;

        } catch (Exception e) {
            log.warn("Error checking for gift voucher {}: {}", voucherCode, e.getMessage());
            return false;
        }
    }

    /**
     * Gets the total number of gift vouchers applied to the cart
     * 
     * @param cartJson The cart response JSON
     * @return The number of gift vouchers, 0 if none found
     */
    public static int getGiftVoucherCount(JSONObject cartJson) {
        try {
            if (!cartJson.has("result") || !cartJson.getJSONObject("result").has("cart")) {
                return 0;
            }

            JSONObject cart = cartJson.getJSONObject("result").getJSONObject("cart");
            if (!cart.has("giftVouchers")) {
                return 0;
            }

            JSONArray giftVouchers = cart.getJSONArray("giftVouchers");
            int count = giftVouchers.length();
            log.debug("Found {} gift vouchers in cart", count);
            return count;

        } catch (Exception e) {
            log.warn("Error counting gift vouchers: {}", e.getMessage());
            return 0;
        }
    }

    /**
     * Attempts to apply a gift voucher to the cart
     * 
     * @param voucherCode The voucher code to apply
     * @param headers Request headers
     * @param endpoint The complete endpoint URL for voucher operations
     * @return Response object if successful, null if failed
     */
    public static Response tryApplyVoucher(String voucherCode, Map<String, String> headers, String endpoint) {
        try {
            Response response = RestUtils.post(endpoint, headers, "{}", 200);
            log.info("Successfully applied voucher {}", voucherCode);
            return response;
        } catch (Exception e) {
            String errorMessage = e.getMessage().toLowerCase();
            // Handle specific error cases
            if (errorMessage.contains("422")) {
                if (errorMessage.contains("voucher is used")) {
                    log.error("Voucher {} is already used", voucherCode);
                    Assert.fail("Gift voucher " + voucherCode + " is already used and cannot be applied again");
                }
                if (errorMessage.contains("voucher is already applied")) {
                    log.info("Voucher {} is already applied to cart", voucherCode);
                    return null;
                }
            }
            log.error("Failed to apply voucher {}: {}", voucherCode, e.getMessage());
            throw e;
        }
    }

    /**
     * Attempts to remove a gift voucher from the cart
     * 
     * @param voucherCode The voucher code to remove
     * @param headers Request headers
     * @param endpoint The complete endpoint URL for voucher operations
     * @return Response object if successful, null if failed
     */
    public static Response removeVoucher(String voucherCode, Map<String, String> headers, String endpoint) {
        try {
            Response response = RestUtils.delete(endpoint, headers, 200);
            log.info("Successfully removed voucher {}", voucherCode);
            return response;
        } catch (Exception e) {
            log.error("Failed to remove voucher {}: {}", voucherCode, e.getMessage());
            return null;
        }
    }

    /**
     * Verifies the final state of voucher application
     * 
     * @param cartJson The cart response JSON
     * @param expectedVoucherCode The expected voucher code
     * @return true if verification passes, false otherwise
     */
    public static boolean verifyVoucherState(JSONObject cartJson, String expectedVoucherCode) {
        String actualVoucherCode = getFirstApplicableVoucher(cartJson);
        if (!expectedVoucherCode.equals(actualVoucherCode)) {
            log.error("Voucher state mismatch. Expected: {}, Found: {}", expectedVoucherCode, actualVoucherCode);
            return false;
        }
        return true;
    }
}
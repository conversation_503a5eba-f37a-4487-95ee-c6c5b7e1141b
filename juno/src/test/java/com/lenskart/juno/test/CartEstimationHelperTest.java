package com.lenskart.juno.test;

import com.lenskart.commons.model.*;
import com.lenskart.juno.helpers.*;
import org.testng.annotations.Test;
import java.util.List;

public class CartEstimationHelperTest {
    @Test
    public void testCartEstimation() {

        OrderContext orderContext = OrderContext.builder()
                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN)
                        .build())
                .headers(OrderContext.Headers.builder()
                        .client(Client.ANDROID)
                        .build())

                .productLists(List.of(
                        OrderContext.ProductList.builder()
                                .productId(ProductId.IN_EYEGLASSES.getProductId())
                                .powerType(PowerTypes.SINGLE_VISION)
                                .build())).build();


        AuthenticationHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();


    /* Search for product and get package id */
        GetProductAndPackageHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

    /* Clear Cart */
        ClearCartHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

    /* Create Cart */
        CreateCartHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();



//        CartEstimationHelpers.builder()
//                .orderContext(orderContext)
//                .build()
//                .test();




    }
}
package com.lenskart.juno.test;

import com.lenskart.commons.model.*;
import com.lenskart.juno.helpers.CartEstimationHelpers;
import org.testng.annotations.Test;
import java.util.List;

public class CartEstimationHelperTest {
    @Test
    public void testCartEstimation() {

        OrderContext orderContext = OrderContext.builder()
                .phoneNumber(Countries.IN.getDefaultPhoneNumber())
                .countryCodeMapper(OrderContext.CountryCodeMapper.builder()
                        .country(Countries.IN)
                        .build())
                .headers(OrderContext.Headers.builder()
                        .client(Client.ANDROID)
                        .build())

                .productLists(List.of(
                        OrderContext.ProductList.builder()
                                .productId(ProductId.IN_EYEGLASSES.getProductId())
                                .powerType(PowerTypes.SINGLE_VISION)
                                .build())).build();



        CartEstimationHelpers.builder()
                .orderContext(orderContext)
                .build()
                .test();


    }
}
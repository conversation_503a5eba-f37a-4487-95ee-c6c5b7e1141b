# Commons Module

## Overview

The **Commons Module** is the foundational module of the Lenskart Backend Automation Framework. It provides shared utilities, configurations, database connections, and common functionality that is used across all other modules in the framework.

## 🏗️ **Architecture**

### **Core Components**

- **Configuration Management**: Unified configuration loading and management
- **Database Connectivity**: Multi-cluster support for MySQL, MongoDB, Redis, Elasticsearch
- **Endpoint Management**: Base endpoint interfaces and management utilities
- **Utilities**: Common utilities for REST calls, JSON processing, logging, etc.
- **Reporting**: Extent Reports integration and management
- **Listeners**: TestNG listeners for test execution and reporting
- **Models**: Common data models and enums used across modules

## 📁 **Package Structure**

```
com.lenskart.commons/
├── annotations/          # Custom annotations for test categorization
├── base/                # Base classes and interfaces
├── config/              # Configuration classes and providers
├── constants/           # Application constants and header mappings
├── database/            # Database connectivity and utilities
│   ├── elasticsearch/   # Elasticsearch connection and operations
│   ├── mongodb/         # MongoDB multi-cluster support
│   ├── mysql/           # MySQL multi-cluster support
│   └── redis/           # Redis connection and operations
├── endpoints/           # Endpoint management interfaces
├── examples/            # Usage examples for various utilities
├── listeners/           # TestNG listeners for reporting and categorization
├── loader/              # Configuration loaders and registries
├── model/               # Common data models and enums
├── reporting/           # Extent Reports management
└── utils/               # Utility classes for common operations
```

## 🔧 **Key Features**

### **1. Multi-Cluster Database Support**

#### **MySQL Multi-Cluster**
```java
// Dynamic MySQL connection to any cluster and database
Connection connection = DynamicMySQLConnectionManager.getConnection("user_cluster", "userdb");

// Execute queries dynamically
List<Map<String, Object>> results = DynamicQueryExecutor.executeQuery(
    "user_cluster", "userdb", 
    "SELECT * FROM users WHERE status = ?", 
    "active"
);
```

#### **MongoDB Multi-Cluster**
```java
// Dynamic MongoDB operations across clusters
List<Document> users = DynamicMongoDBQueryExecutor.find(
    "user_cluster", "userdb", "users", 
    new Document("status", "active")
);

// Insert documents
DynamicMongoDBQueryExecutor.insertOne(
    "orders_cluster", "orders", "orders", 
    new Document("orderId", "ORD123").append("status", "pending")
);
```

### **2. Configuration Management**

#### **Unified Configuration Registry**
```java
// Access any configuration
ConfigRegistry registry = ConfigRegistry.getInstance();

// MySQL multi-cluster configuration
MySQLMultiClusterConfig mysqlConfig = registry.getMySQLMultiClusterConfig();

// MongoDB multi-cluster configuration
MongoDBMultiClusterConfig mongoConfig = registry.getMongoDBMultiClusterConfig();

// Redis configuration
RedisConfig redisConfig = registry.getRedisConfig("cache");
```

### **3. REST API Utilities**

#### **RestUtils for API Calls**
```java
// GET request
Response response = RestUtils.get("https://api.example.com/users");

// POST request with JSON body
Map<String, Object> requestBody = Map.of("name", "John", "email", "<EMAIL>");
Response response = RestUtils.post("https://api.example.com/users", requestBody);

// Multipart file upload
Response response = RestUtils.postMultipart(
    "https://api.example.com/upload", 
    Map.of("file", new File("document.pdf"))
);
```

### **4. Endpoint Management**

#### **BaseEndpoint Interface**
```java
public enum MyEndpoints implements BaseEndpoint {
    GET_USER("/api/v1/users/{userId}", "userService", "GET", "Get user details"),
    CREATE_ORDER("/api/v1/orders", "orderService", "POST", "Create new order");
    
    @Override
    public String getUrl() {
        return MyEndpointManager.getEndpointUrl(this);
    }
    
    @Override
    public String getUrl(Map<String, String> pathParams) {
        return MyEndpointManager.getEndpointUrl(this, pathParams);
    }
}
```

### **5. Test Categorization**

#### **Test Categories**
```java
@Test
@TestCategory(TestCategory.Category.SANITY)
public void testUserLogin() {
    // Test implementation
}

@Test
@TestCategory(TestCategory.Category.REGRESSION)
public void testOrderCreation() {
    // Test implementation
}
```

### **6. Extent Reports Integration**

#### **Automatic Report Generation**
- Reports generated in `test-output/extent-reports/test-report.html`
- Rich HTML reports with test statistics, logs, and screenshots
- Automatic test categorization and environment information

## 🛠️ **Utilities**

### **JsonUtils**
```java
// Parse JSON to object
User user = JsonUtils.parseObject(jsonString, User.class);

// Convert object to JSON
String json = JsonUtils.toJson(user);

// Parse JSON response
Map<String, Object> data = JsonUtils.parseResponse(response);
```

### **AwaitUtils**
```java
// Wait with condition
AwaitUtils.waitUntil(() -> orderStatus.equals("COMPLETED"), Duration.ofMinutes(5));

// Simple wait
AwaitUtils.waitFor(Duration.ofSeconds(10));

// Wait with polling
AwaitUtils.pollUntil(() -> getOrderStatus(orderId), "COMPLETED", Duration.ofMinutes(2));
```

### **GenericUtils**
```java
// Generate random data
String randomEmail = GenericUtils.generateRandomEmail();
String randomPhone = GenericUtils.generateRandomPhoneNumber();
String randomString = GenericUtils.generateRandomString(10);

// Date utilities
String currentDate = GenericUtils.getCurrentDate();
String futureDate = GenericUtils.getFutureDate(30); // 30 days from now
```

## 🗄️ **Database Configuration**

### **config.yml Structure**
```yaml
# MySQL Multi-Cluster Configuration
mysqlClusters:
  user_cluster:
    host: mysql-user.preprod.internal
    port: 3306
    username: user_service
    password: UserPass123!
    
# MongoDB Multi-Cluster Configuration
mongodbClusters:
  user_cluster:
    host: mongo-user.preprod.internal
    port: 27017
    username: user_service
    password: UserServicePass123!
    
# Redis Configuration
redis:
  cache:
    host: redis-cache.preprod.internal
    port: 6379
    password: RedisPass123!
    
# SSH Configuration
sshConfig:
  enabled: true
  hostname: bastion.preprod.internal
  port: 22
  username: automation
  privateKeyPath: ~/.ssh/automation_key
```

## 🧪 **Testing**

### **Running Commons Tests**
```bash
# Run all commons tests
mvn test -pl commons

# Run specific test categories
mvn test -pl commons -DtestCategory=SANITY

# Run with specific environment
mvn test -pl commons -Denvironment=preprod
```

### **Test Categories Available**
- **SANITY**: Basic functionality tests
- **REGRESSION**: Comprehensive regression tests
- **E2E**: End-to-end integration tests

## 📊 **Models and Enums**

### **Countries Enum**
```java
Countries.INDIA.getCountryCode();        // "IN"
Countries.INDIA.getDefaultPhoneNumber(); // "+91-9876543210"
Countries.SINGAPORE.getCurrency();       // "SGD"
```

### **ItemType Enum**
```java
ItemType.OTC                    // Over-the-counter items
ItemType.LOCAL_FITTING          // Local fitting items
ItemType.LENS_ONLY             // Lens only items
ItemType.DTC                   // Direct-to-consumer items
```

### **NexsOrderState Enum**
```java
NexsOrderState.CREATED         // Order created
NexsOrderState.PROCESSING      // Order in processing
NexsOrderState.IN_PICKING      // Order in picking
NexsOrderState.PICKED          // Order picked
```

## 🔗 **Dependencies**

### **Key Dependencies**
- **TestNG**: Test execution framework
- **RestAssured**: REST API testing
- **ExtentReports**: Test reporting
- **Jackson**: JSON processing
- **MySQL Connector**: MySQL database connectivity
- **MongoDB Driver**: MongoDB connectivity
- **Jedis**: Redis connectivity
- **Elasticsearch Client**: Elasticsearch connectivity
- **JSch**: SSH connectivity
- **SnakeYAML**: YAML configuration parsing
- **Awaitility**: Asynchronous testing utilities

## 🚀 **Getting Started**

### **1. Add Commons Dependency**
```xml
<dependency>
    <groupId>com.lenskart</groupId>
    <artifactId>commons</artifactId>
    <version>1.0.0</version>
</dependency>
```

### **2. Configure Database Connections**
Update `config.yml` with your database cluster configurations.

### **3. Use Utilities in Tests**
```java
public class MyTest {
    @Test
    @TestCategory(TestCategory.Category.SANITY)
    public void testApiCall() {
        Response response = RestUtils.get("https://api.example.com/health");
        assert response.getStatusCode() == 200;
    }
}
```

## 📚 **Examples**

The `examples` package contains comprehensive usage examples:
- **DynamicMySQLUsageExamples**: MySQL multi-cluster operations
- **DynamicMongoDBUsageExamples**: MongoDB multi-cluster operations
- **RestUtilsMultipartExample**: File upload examples
- **ElasticsearchExample**: Elasticsearch operations
- **RedisExample**: Redis operations
- **AwaitUtilsExamples**: Asynchronous waiting examples

## 🔧 **Configuration**

### **Environment-Specific Configuration**
The commons module supports environment-specific configurations through the `environment` system property:

```bash
mvn test -Denvironment=preprod
mvn test -Denvironment=prod
```

### **SSH Tunneling**
SSH tunneling is automatically handled when enabled in configuration:
- Tunnels are created per database cluster
- Automatic port allocation for local tunnels
- Connection reuse for performance

## 📈 **Performance Features**

- **Connection Pooling**: Efficient database connection management
- **Configuration Caching**: Cached configuration loading
- **SSH Tunnel Reuse**: Optimized SSH tunnel management
- **Async Operations**: Support for asynchronous operations with AwaitUtils

The Commons module provides the foundation for all automation testing activities across the Lenskart ecosystem, ensuring consistency, reliability, and maintainability across all test modules.

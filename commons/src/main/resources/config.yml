# Unified configuration file for the commons module

# QA environment configuration
preprod:
  # MySQL Multi-Cluster Configuration (New Dynamic Approach)
  mysqlClusters:
    # Nexs Cluster -
    nexs_cluster:
      host: nexs-mysql.preprod.internal
      port: 3306
      username: user_wms
      password: W1ld!7Vgh:9zXyXMp^n4<-m8
      driver: com.mysql.cj.jdbc.Driver
      maxPoolSize: 10
      minIdle: 2
      connectionTimeout: 30000
      idleTimeout: 600000
      maxLifetime: 1800000

      # Pos Cluster
    pos_cluster:
      host: mysql-pos-webservice.preprod.internal
      port: 3306
      username: user_test
      password: 123123jnj
      driver: com.mysql.cj.jdbc.Driver
      maxPoolSize: 20
      minIdle: 5
      connectionTimeout: 30000
      idleTimeout: 600000
      maxLifetime: 1800000


    # SCM Cluster
    scm_cluster:
      host: mysql-pos-webservice.preprod.internal
      port: 3306
      username: amit_aggarwal
      password: BKR8fev9B!8Yoia2hQRYzMyxRqC98NM
      driver: com.mysql.cj.jdbc.Driver
      maxPoolSize: 12
      minIdle: 3
      connectionTimeout: 30000
      idleTimeout: 600000
      maxLifetime: 1800000



    # QMS Cluster
    qms_cluster:
      host: stores-db.preprod.internal
      port: 3306
      username: warehouse_user
      password: WarehousePass123!
      driver: com.mysql.cj.jdbc.Driver
      maxPoolSize: 15
      minIdle: 3
      connectionTimeout: 30000
      idleTimeout: 600000
      maxLifetime: 1800000

    # NPS Cluster
    nps_cluster:
      host: nps.preprod.internal
      port: 3306
      username: user_service
      password: UserPass123!
      driver: com.mysql.cj.jdbc.Driver
      maxPoolSize: 25
      minIdle: 5
      connectionTimeout: 30000
      idleTimeout: 600000
      maxLifetime: 1800000

    # Juno Cluster
    juno_cluster:
      host: mysql-web-master-rw.preprod.internal
      port: 3306
      username: legacy_user
      password: LegacyPass123!
      driver: com.mysql.cj.jdbc.Driver
      maxPoolSize: 8
      minIdle: 2
      connectionTimeout: 30000
      idleTimeout: 600000
      maxLifetime: 1800000

  # MongoDB Multi-Cluster Configuration (New Dynamic Approach)
  mongodbClusters:
    # SCM Cluster
    scm_cluster:
      host: mongo-scm.preprod.internal
      port: 27017
      username: user_service
      password: UserServicePass123!
      authSource: admin
      authMechanism: SCRAM-SHA-256
      maxPoolSize: 50
      minPoolSize: 5
      connectTimeout: 30000
      socketTimeout: 30000
      maxIdleTimeMS: 600000
      maxLifeTimeMS: 1800000

    # Bridge Cluster
    bridge_cluster:
      host: bridge-mongo1.preprod.internal
      port: 27017
      username: inventory_service
      password: InventoryPass123!
      authSource: admin
      authMechanism: SCRAM-SHA-256
      maxPoolSize: 75
      minPoolSize: 10
      connectTimeout: 30000
      socketTimeout: 30000
      maxIdleTimeMS: 600000
      maxLifeTimeMS: 1800000

    # Juno Cluster
    juno_cluster:
      host: mongos-juno.preprod.internal
      port: 27017
      username: analytics_service
      password: AnalyticsPass123!
      authSource: admin
      authMechanism: SCRAM-SHA-256
      maxPoolSize: 100
      minPoolSize: 15
      connectTimeout: 30000
      socketTimeout: 30000
      maxIdleTimeMS: 600000
      maxLifeTimeMS: 1800000

    # POS Cluster
    pos_cluster:
      host: pos-mongo.preprod.internal
      port: 27017
      username: orders_service
      password: OrdersPass123!
      authSource: admin
      authMechanism: SCRAM-SHA-256
      maxPoolSize: 80
      minPoolSize: 12
      connectTimeout: 30000
      socketTimeout: 30000
      maxIdleTimeMS: 600000
      maxLifeTimeMS: 1800000

    # POS II Cluster
    pos_redis_cluster:
      host: redismongo-pos.preprod.internal
      port: 27017
      username: catalog_service
      password: CatalogPass123!
      authSource: admin
      authMechanism: SCRAM-SHA-256
      maxPoolSize: 60
      minPoolSize: 8
      connectTimeout: 30000
      socketTimeout: 30000
      maxIdleTimeMS: 600000
      maxLifeTimeMS: 1800000

  # Redis configurations
  redis:
    # Default Redis configuration
    default:
      host: redis.example.com
      port: 6379
      password: redis_password
      database: 0
      ssl: false
      timeout: 2000
      maxTotal: 128
      maxIdle: 128
      minIdle: 16

    # Cache Redis configuration
    cache:
      host: redis-cache.example.com
      port: 6379
      password: redis_cache_password
      database: 0
      ssl: false
      timeout: 2000
      maxTotal: 128
      maxIdle: 128
      minIdle: 16

  # Elasticsearch configurations
  elasticsearch:
    # Default Elasticsearch configuration
    default:
      hosts:
        - http://es.example.com:9200
      username: elastic
      password: elastic_password
      connectTimeout: 5000
      readTimeout: 5000
      maxTotalConnections: 20
      defaultMaxTotalConnectionsPerRoute: 10
      discoveryEnabled: false
      multiThreaded: true

    # Search Elasticsearch configuration
    search:
      hosts:
        - http://es-search-1.example.com:9200
        - http://es-search-2.example.com:9200
      username: elastic
      password: elastic_password
      connectTimeout: 5000
      readTimeout: 5000
      maxTotalConnections: 40
      defaultMaxTotalConnectionsPerRoute: 20
      discoveryEnabled: true
      multiThreaded: true

  # SSH Tunneling configuration
  sshConfig:
    hostname: ************
    username: harishaj
    password: Deshler300905
    enabled: true

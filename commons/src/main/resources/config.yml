# Unified configuration file for the commons module

# QA environment configuration
preprod:
  # Database configurations
  databases:
    # Default database configuration
    default:
      url: ******************************************************************************
      username: qa_user
      password: qa_password
      driver: com.mysql.cj.jdbc.Driver
      maxPoolSize: 10
      minIdle: 5
      connectionTimeout: 30000
      idleTimeout: 600000
      maxLifetime: 1800000

    # User service database configuration
    tracking_middleware:
      url: **********************************************************************************
      username: user_middleware
      password: test_123
      driver: com.mysql.cj.jdbc.Driver
      maxPoolSize: 10
      minIdle: 5
      connectionTimeout: 30000
      idleTimeout: 600000
      maxLifetime: 1800000

  # MongoDB configurations
  mongodb:
    # User database configuration
    userdb:
      uri: mongodb://mongo-db.example.com:27017/userdb
      database: userdb
      username: mongo_user
      password: mongo_password
      authSource: admin
      authMechanism: SCRAM-SHA-256
      maxPoolSize: 100
      minPoolSize: 10
      connectTimeout: 30000
      socketTimeout: 30000
      maxIdleTimeMS: 600000
      maxLifeTimeMS: 1800000

    # Inventory database configuration
    inventorydb:
      uri: mongodb://mongo-db.example.com:27017/inventorydb
      database: inventorydb
      username: mongo_inventory
      password: mongo_inventory_password
      authSource: admin
      authMechanism: SCRAM-SHA-256
      maxPoolSize: 100
      minPoolSize: 10
      connectTimeout: 30000
      socketTimeout: 30000
      maxIdleTimeMS: 600000
      maxLifeTimeMS: 1800000

  # Redis configurations
  redis:
    # Default Redis configuration
    default:
      host: redis.example.com
      port: 6379
      password: redis_password
      database: 0
      ssl: false
      timeout: 2000
      maxTotal: 128
      maxIdle: 128
      minIdle: 16

    # Cache Redis configuration
    cache:
      host: redis-cache.example.com
      port: 6379
      password: redis_cache_password
      database: 0
      ssl: false
      timeout: 2000
      maxTotal: 128
      maxIdle: 128
      minIdle: 16

  # Elasticsearch configurations
  elasticsearch:
    # Default Elasticsearch configuration
    default:
      hosts:
        - http://es.example.com:9200
      username: elastic
      password: elastic_password
      connectTimeout: 5000
      readTimeout: 5000
      maxTotalConnections: 20
      defaultMaxTotalConnectionsPerRoute: 10
      discoveryEnabled: false
      multiThreaded: true

    # Search Elasticsearch configuration
    search:
      hosts:
        - http://es-search-1.example.com:9200
        - http://es-search-2.example.com:9200
      username: elastic
      password: elastic_password
      connectTimeout: 5000
      readTimeout: 5000
      maxTotalConnections: 40
      defaultMaxTotalConnectionsPerRoute: 20
      discoveryEnabled: true
      multiThreaded: true

  # SSH Tunneling configuration
  sshConfig:
    hostname: ************
    username: amit
    password: 123
    enabled: true

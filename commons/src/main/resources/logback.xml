<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- Define properties for log directories and file names -->
    <property name="LOG_DIR" value="logs" />
    <property name="LOG_FILE_NAME" value="automation" />
    <property name="HTTP_LOG_FILE_NAME" value="http-requests" />
    <property name="MODULE_LOG_DIR" value="${LOG_DIR}/modules" />

    <!-- Console Appender -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Main File Appender -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_DIR}/${LOG_FILE_NAME}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_DIR}/${LOG_FILE_NAME}-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- HTML Appender for test reports -->
    <appender name="HTML" class="ch.qos.logback.core.FileAppender">
        <file>${LOG_DIR}/test-report.html</file>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="ch.qos.logback.classic.html.HTMLLayout">
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS}%thread%level%logger%msg</pattern>
            </layout>
        </encoder>
    </appender>

    <!-- HTTP Log File Appender -->
    <appender name="HTTP_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_DIR}/${HTTP_LOG_FILE_NAME}.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_DIR}/${HTTP_LOG_FILE_NAME}-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Module-specific appenders -->
    <!-- Commons Module Appender -->
    <appender name="COMMONS_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${MODULE_LOG_DIR}/commons.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${MODULE_LOG_DIR}/commons-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
            <totalSizeCap>500MB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Juno Module Appender -->
    <appender name="JUNO_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${MODULE_LOG_DIR}/juno.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${MODULE_LOG_DIR}/juno-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
            <totalSizeCap>500MB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- SCM Module Appender -->
    <appender name="SCM_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${MODULE_LOG_DIR}/scm.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${MODULE_LOG_DIR}/scm-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
            <totalSizeCap>500MB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- NEXS Module Appender -->
    <appender name="NEXS_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${MODULE_LOG_DIR}/nexs.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${MODULE_LOG_DIR}/nexs-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
            <totalSizeCap>500MB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- CSE Module Appender -->
    <appender name="CSE_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${MODULE_LOG_DIR}/cse.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${MODULE_LOG_DIR}/cse-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
            <totalSizeCap>500MB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- POS Module Appender -->
    <appender name="POS_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${MODULE_LOG_DIR}/pos.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${MODULE_LOG_DIR}/pos-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
            <totalSizeCap>500MB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- E2E Module Appender -->
    <appender name="E2E_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${MODULE_LOG_DIR}/e2e.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${MODULE_LOG_DIR}/e2e-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
            <totalSizeCap>500MB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Example Module Appender -->
    <appender name="EXAMPLE_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${MODULE_LOG_DIR}/example.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${MODULE_LOG_DIR}/example-%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
            <totalSizeCap>500MB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Logger for API requests and responses -->
    <logger name="com.lenskart.commons.utils.RestUtils" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
        <appender-ref ref="HTML" />
        <appender-ref ref="HTTP_FILE" />
    </logger>

    <!-- Logger for Rest Assured -->
    <logger name="io.restassured" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="HTTP_FILE" />
    </logger>

    <!-- Logger for HTTP wire traffic -->
    <logger name="org.apache.http" level="DEBUG" additivity="false">
        <appender-ref ref="HTTP_FILE" />
    </logger>

    <!-- Logger for database operations -->
    <logger name="com.lenskart.commons.database" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
        <appender-ref ref="COMMONS_FILE" />
        <appender-ref ref="HTML" />
    </logger>

    <!-- Module-specific loggers -->
    <!-- Commons Module Logger -->
    <logger name="com.lenskart.commons" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
        <appender-ref ref="COMMONS_FILE" />
        <appender-ref ref="HTML" />
    </logger>

    <!-- Juno Module Logger -->
    <logger name="com.lenskart.juno" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
        <appender-ref ref="JUNO_FILE" />
        <appender-ref ref="HTML" />
    </logger>

    <!-- SCM Module Logger -->
    <logger name="com.lenskart.scm" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
        <appender-ref ref="SCM_FILE" />
        <appender-ref ref="HTML" />
    </logger>

    <!-- NEXS Module Logger -->
    <logger name="com.lenskart.nexs" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
        <appender-ref ref="NEXS_FILE" />
        <appender-ref ref="HTML" />
    </logger>

    <!-- CSE Module Logger -->
    <logger name="com.lenskart.cse" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
        <appender-ref ref="CSE_FILE" />
        <appender-ref ref="HTML" />
    </logger>

    <!-- POS Module Logger -->
    <logger name="com.lenskart.pos" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
        <appender-ref ref="POS_FILE" />
        <appender-ref ref="HTML" />
    </logger>

    <!-- E2E Module Logger -->
    <logger name="com.lenskart.e2e" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
        <appender-ref ref="E2E_FILE" />
        <appender-ref ref="HTML" />
    </logger>

    <!-- Example Module Logger -->
    <logger name="com.lenskart.example" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
        <appender-ref ref="EXAMPLE_FILE" />
        <appender-ref ref="HTML" />
    </logger>

    <!-- TestNG Logger -->
    <logger name="org.testng" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </logger>

    <!-- Root logger -->
    <root level="INFO">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </root>
</configuration>
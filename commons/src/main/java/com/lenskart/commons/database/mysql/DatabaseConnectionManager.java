package com.lenskart.commons.database.mysql;

import com.jcraft.jsch.JSchException;
import com.lenskart.commons.loader.ConfigRegistry;
import com.lenskart.commons.config.DatabaseConfig;
import com.lenskart.commons.config.MySQLMultiClusterConfig;
import com.lenskart.commons.loader.DatabaseConfigLoader;
import com.lenskart.commons.config.SSHConfig;
import com.lenskart.commons.loader.SSHConfigLoader;
import com.lenskart.commons.utils.SSHSessionUtil;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;

import javax.sql.DataSource;
import java.net.URI;
import java.net.URISyntaxException;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.lenskart.commons.constants.Constants.DEFAULT_MYSQL_LOCAL_PORT;

/**
 * Manager class for database connections using connection pooling
 */
@Slf4j
public class DatabaseConnectionManager {

    private static final Map<String, HikariDataSource> dataSources = new ConcurrentHashMap<>();
    private static final Map<String, SSHConfig> sshConfigs = new ConcurrentHashMap<>();

    /**
     * Gets a database connection for the specified database name
     *
     * @param dbName Name of the database to connect to
     * @return SQL Connection object
     * @throws SQLException if a database access error occurs
     */
    public static Connection getConnection(String dbName) throws SQLException {
        return getDataSource(dbName).getConnection();
    }

    /**
     * Gets a DataSource for the specified database name
     *
     * @param dbName Name of the database to get a DataSource for
     * @return DataSource object for the specified database
     */
    public static DataSource getDataSource(String dbName) {
        if (!dataSources.containsKey(dbName)) {
            synchronized (DatabaseConnectionManager.class) {
                if (!dataSources.containsKey(dbName)) {
                    DatabaseConfig config = getDatabaseConfiguration(dbName);
                    dataSources.put(dbName, createDataSource(config));
                }
            }
        }
        return dataSources.get(dbName);
    }

    /**
     * Gets database configuration with MySQL cluster support
     *
     * @param dbName Name of the database
     * @return DatabaseConfig for the specified database
     */
    private static DatabaseConfig getDatabaseConfiguration(String dbName) {
        ConfigRegistry registry = ConfigRegistry.getInstance();

        // First, try to get from MySQL cluster configuration
        if (registry.hasMySQLClusterConfig()) {
            MySQLClusterConfig clusterConfig = registry.getMySQLClusterConfig();
            if (clusterConfig.isDatabaseConfigured(dbName)) {
                log.info("Using MySQL cluster configuration for database: {}", dbName);
                return clusterConfig.createDatabaseConfig(dbName);
            }
        }

        // Fall back to individual database configuration
        DatabaseConfig config = registry.getDatabaseConfig(dbName);
        if (config != null) {
            log.info("Using individual database configuration for database: {}", dbName);
            return config;
        }

        // Final fallback to DatabaseConfigLoader
        log.warn("Database configuration not found in registry for: {}, using loader fallback", dbName);
        return DatabaseConfigLoader.loadConfig(dbName);
    }

    /**
     * Creates a HikariCP DataSource from a DatabaseConfig
     *
     * @param config Database configuration
     * @return HikariDataSource configured with the provided settings
     */
    private static HikariDataSource createDataSource(DatabaseConfig config) {
        HikariConfig hikariConfig = new HikariConfig();

        String jdbcUrl = config.getUrl();

        // Check if we have an SSH config or need to load one
        SSHConfig sshConfig = sshConfigs.computeIfAbsent(config.getName(), k -> {
            // Load SSH configuration from registry
            SSHConfig loadedConfig = SSHConfigLoader.loadConfig();
            if (loadedConfig != null) {
                log.info("Loaded SSH configuration for database: {}", config.getName());
            }
            return loadedConfig;
        });

        // If SSH config exists and is enabled, create tunnel and modify JDBC URL
        if (sshConfig != null && sshConfig.isEnabled()) {
            try {
                // Create SSH tunnel
                jdbcUrl = createTunnelAndGetJdbcUrl(config.getUrl(), sshConfig);
                log.info("Using tunneled JDBC URL: {}", jdbcUrl);
            } catch (Exception e) {
                log.error("Failed to create SSH tunnel: {}", e.getMessage(), e);
                // Fall back to direct connection
                log.warn("Falling back to direct connection: {}", config.getUrl());
                jdbcUrl = config.getUrl();
            }
        }

        hikariConfig.setJdbcUrl(jdbcUrl);
        hikariConfig.setUsername(config.getUsername());
        hikariConfig.setPassword(config.getPassword());
        hikariConfig.setDriverClassName(config.getDriverClassName());

        // Connection pool settings
        hikariConfig.setMaximumPoolSize(config.getMaxPoolSize());
        hikariConfig.setMinimumIdle(config.getMinIdle());
        hikariConfig.setConnectionTimeout(config.getConnectionTimeout());
        hikariConfig.setIdleTimeout(config.getIdleTimeout());
        hikariConfig.setMaxLifetime(config.getMaxLifetime());

        // Pool name for easier identification in logs
        hikariConfig.setPoolName(config.getName() + "-pool");

        return new HikariDataSource(hikariConfig);
    }

    /**
     * Creates an SSH tunnel and returns a modified JDBC URL that uses the tunnel
     *
     * @param originalJdbcUrl Original JDBC URL
     * @param sshConfig SSH configuration
     * @return Modified JDBC URL that uses the local tunnel port
     * @throws URISyntaxException if the JDBC URL is invalid
     * @throws JSchException if there's an error establishing the SSH tunnel
     */
    private static String createTunnelAndGetJdbcUrl(String originalJdbcUrl, SSHConfig sshConfig)
            throws URISyntaxException, JSchException {
        // Parse the JDBC URL to extract host, port, and database name
        // Format: **************************************
        String urlWithoutJdbc = originalJdbcUrl.substring(5); // Remove "jdbc:"
        URI uri = new URI(urlWithoutJdbc);

        String host = uri.getHost();
        int port = uri.getPort();
        String path = uri.getPath(); // Contains "/database"
        String query = uri.getQuery(); // Contains URL parameters

        // Update SSH config with remote host and port
        sshConfig = SSHConfig.builder()
                .enabled(sshConfig.isEnabled())
                .hostname(sshConfig.getHostname())
                .port(sshConfig.getPort())
                .username(sshConfig.getUsername())
                .password(sshConfig.getPassword())
                .privateKeyPath(sshConfig.getPrivateKeyPath())
                .localPort(DEFAULT_MYSQL_LOCAL_PORT)
                .remoteHost(host)
                .remotePort(port)
                .build();

        // Create the SSH tunnel
        int localPort = SSHSessionUtil.createTunnel(sshConfig);

        // Create a new JDBC URL that uses localhost and the local port
        String newJdbcUrl = String.format("***************************", localPort, path);
        if (query != null && !query.isEmpty()) {
            newJdbcUrl += "?" + query;
        }

        return newJdbcUrl;
    }

    /**
     * Closes all data sources managed by this connection manager
     */
    public static void closeAllDataSources() {
        for (HikariDataSource dataSource : dataSources.values()) {
            if (!dataSource.isClosed()) {
                dataSource.close();
            }
        }
        dataSources.clear();

        // Close all SSH tunnels
        if (!sshConfigs.isEmpty()) {
            SSHSessionUtil.closeAllTunnels();
            sshConfigs.clear();
        }
    }

    /**
     * Closes a specific data source
     *
     * @param dbName Name of the database whose data source should be closed
     */
    public static void closeDataSource(String dbName) {
        HikariDataSource dataSource = dataSources.get(dbName);
        if (dataSource != null && !dataSource.isClosed()) {
            dataSource.close();
            dataSources.remove(dbName);

            // Close associated SSH tunnel if it exists
            SSHConfig config = sshConfigs.remove(dbName);
            if (config != null) {
                try {
                    SSHSessionUtil.closeTunnel(config);
                    log.info("Closed SSH tunnel for database: {}", dbName);
                } catch (Exception e) {
                    log.warn("Error closing SSH tunnel for database {}: {}", dbName, e.getMessage());
                }
            }
        }
    }

    /**
     * Registers a database configuration directly
     *
     * @param dbName Name of the database
     * @param config Database configuration
     */
    public static void registerConfig(String dbName, DatabaseConfig config) {
        // If there's already a data source for this database, close it
        closeDataSource(dbName);

        // Register the configuration with the DatabaseConfigLoader
        DatabaseConfigLoader.registerConfig(dbName, config);

        // Create a new data source with the provided configuration
        dataSources.put(dbName, createDataSource(config));

        log.info("Registered database configuration for name: {}", dbName);
    }

    /**
     * Checks if SSH tunneling is configured and enabled for a specific database
     *
     * @param dbName Name of the database
     * @return true if SSH tunneling is configured and enabled, false otherwise
     */
    public static boolean isSSHTunnelingConfigured(String dbName) {
        SSHConfig config = sshConfigs.get(dbName);
        return config != null && config.isEnabled();
    }

    /**
     * Refreshes the SSH configuration for all databases
     * This will close all existing connections and tunnels, and recreate them with the latest configuration
     */
    public static void refreshSSHConfiguration() {
        // Close all existing connections and tunnels
        closeAllDataSources();

        // The next time a connection is requested, the SSH configuration will be reloaded
        log.info("SSH configuration refreshed. New connections will use updated configuration.");
    }
}
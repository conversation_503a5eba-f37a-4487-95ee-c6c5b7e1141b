package com.lenskart.commons.database.mysql;

import lombok.extern.slf4j.Slf4j;

import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Utility class for executing database queries
 */
@Slf4j
public class QueryExecutor {

    /**
     * Executes a SELECT query and maps the results to a list of objects
     *
     * @param dbName Name of the database to query
     * @param query  SQL query to execute
     * @param mapper Mapper to convert result set rows to objects
     * @param params Query parameters (optional)
     * @param <T>    Type of objects to return
     * @return List of objects mapped from the query results
     */
    public static <T> List<T> executeQuery(String dbName, String query, ResultSetMapper<T> mapper, Object... params) {
        List<T> results = new ArrayList<>();

        try (Connection conn = DatabaseConnectionManager.getConnection(dbName);
             PreparedStatement stmt = prepareStatement(conn, query, params);
             ResultSet rs = stmt.executeQuery()) {

            while (rs.next()) {
                results.add(mapper.map(rs));
            }

        } catch (SQLException e) {
            log.error("Error executing query: {}", query, e);
            throw new RuntimeException("Error executing query: " + e.getMessage(), e);
        }

        return results;
    }

    /**
     * Executes a SELECT query and returns the results as a list of maps
     *
     * @param dbName Name of the database to query
     * @param query  SQL query to execute
     * @param params Query parameters (optional)
     * @return List of maps containing column name/value pairs
     */
    public static List<Map<String, Object>> executeQueryAsMap(String dbName, String query, Object... params) {
        List<Map<String, Object>> results = new ArrayList<>();

        try (Connection conn = DatabaseConnectionManager.getConnection(dbName);
             PreparedStatement stmt = prepareStatement(conn, query, params);
             ResultSet rs = stmt.executeQuery()) {

            ResultSetMetaData metaData = rs.getMetaData();
            int columnCount = metaData.getColumnCount();

            while (rs.next()) {
                Map<String, Object> row = new HashMap<>();

                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnLabel(i);
                    Object value = rs.getObject(i);
                    row.put(columnName, value);
                }

                results.add(row);
            }

        } catch (SQLException e) {
            log.error("Error executing query: {}", query, e);
            throw new RuntimeException("Error executing query: " + e.getMessage(), e);
        }

        return results;
    }

    /**
     * Executes an UPDATE, INSERT, or DELETE query
     *
     * @param dbName Name of the database to update
     * @param query  SQL query to execute
     * @param params Query parameters (optional)
     * @return Number of rows affected
     */
    public static int executeUpdate(String dbName, String query, Object... params) {
        try (Connection conn = DatabaseConnectionManager.getConnection(dbName);
             PreparedStatement stmt = prepareStatement(conn, query, params)) {

            return stmt.executeUpdate();

        } catch (SQLException e) {
            log.error("Error executing update: {}", query, e);
            throw new RuntimeException("Error executing update: " + e.getMessage(), e);
        }
    }

    /**
     * Executes an INSERT query and returns the generated keys
     *
     * @param dbName Name of the database to update
     * @param query  SQL query to execute
     * @param params Query parameters (optional)
     * @return List of generated keys
     */
    public static List<Object> executeInsertWithGeneratedKeys(String dbName, String query, Object... params) {
        List<Object> generatedKeys = new ArrayList<>();

        try (Connection conn = DatabaseConnectionManager.getConnection(dbName);
             PreparedStatement stmt = conn.prepareStatement(query, Statement.RETURN_GENERATED_KEYS)) {

            setParameters(stmt, params);
            stmt.executeUpdate();

            try (ResultSet rs = stmt.getGeneratedKeys()) {
                while (rs.next()) {
                    generatedKeys.add(rs.getObject(1));
                }
            }

        } catch (SQLException e) {
            log.error("Error executing insert: {}", query, e);
            throw new RuntimeException("Error executing insert: " + e.getMessage(), e);
        }

        return generatedKeys;
    }

    /**
     * Executes a batch update
     *
     * @param dbName      Name of the database to update
     * @param query       SQL query to execute
     * @param batchParams List of parameter arrays for batch execution
     * @return Array of update counts
     */
    public static int[] executeBatch(String dbName, String query, List<Object[]> batchParams) {
        try (Connection conn = DatabaseConnectionManager.getConnection(dbName);
             PreparedStatement stmt = conn.prepareStatement(query)) {

            for (Object[] params : batchParams) {
                setParameters(stmt, params);
                stmt.addBatch();
            }

            return stmt.executeBatch();

        } catch (SQLException e) {
            log.error("Error executing batch update: {}", query, e);
            throw new RuntimeException("Error executing batch update: " + e.getMessage(), e);
        }
    }

    /**
     * Prepares a statement with the given parameters
     *
     * @param conn   Database connection
     * @param query  SQL query
     * @param params Query parameters
     * @return Prepared statement
     * @throws SQLException if a database access error occurs
     */
    private static PreparedStatement prepareStatement(Connection conn, String query, Object... params) throws SQLException {
        PreparedStatement stmt = conn.prepareStatement(query);
        setParameters(stmt, params);
        return stmt;
    }

    /**
     * Sets parameters on a prepared statement
     *
     * @param stmt   Prepared statement
     * @param params Parameters to set
     * @throws SQLException if a database access error occurs
     */
    private static void setParameters(PreparedStatement stmt, Object... params) throws SQLException {
        for (int i = 0; i < params.length; i++) {
            stmt.setObject(i + 1, params[i]);
        }
    }
}
package com.lenskart.commons.database.mongodb;

import com.jcraft.jsch.JSchException;
import com.lenskart.commons.loader.ConfigRegistry;
import com.lenskart.commons.config.MongoDBConfig;
import com.lenskart.commons.loader.MongoDBConfigLoader;
import com.lenskart.commons.config.SSHConfig;
import com.lenskart.commons.loader.SSHConfigLoader;
import com.lenskart.commons.utils.SSHSessionUtil;
import com.mongodb.ConnectionString;
import com.mongodb.MongoClientSettings;
import com.mongodb.MongoCredential;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import com.mongodb.client.MongoDatabase;
import lombok.extern.slf4j.Slf4j;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import com.lenskart.commons.constants.Constants;

/**
 * Manager class for MongoDB connections using connection pooling
 */
@Slf4j
public class MongoDBConnectionManager {

    private static final Map<String, MongoClient> mongoClients = new ConcurrentHashMap<>();
    private static final Map<String, SSHConfig> sshConfigs = new ConcurrentHashMap<>();

    /**
     * Gets a MongoDB client for the specified database name
     *
     * @param dbName Name of the database to connect to
     * @return MongoClient object
     */
    public static MongoClient getMongoClient(String dbName) {
        if (!mongoClients.containsKey(dbName)) {
            synchronized (MongoDBConnectionManager.class) {
                if (!mongoClients.containsKey(dbName)) {
                    // Use ConfigRegistry to get the MongoDB configuration
                    MongoDBConfig config = ConfigRegistry.getInstance().getMongoDBConfig(dbName);
                    if (config == null) {
                        // Fall back to MongoDBConfigLoader if not found in registry
                        config = MongoDBConfigLoader.loadConfig(dbName);
                    }
                    mongoClients.put(dbName, createMongoClient(config));
                }
            }
        }
        return mongoClients.get(dbName);
    }

    /**
     * Gets a MongoDB database for the specified database name
     *
     * @param dbName Name of the database to connect to
     * @return MongoDatabase object
     */
    public static MongoDatabase getMongoDatabase(String dbName) {
        MongoClient client = getMongoClient(dbName);
        // Use ConfigRegistry to get the MongoDB configuration
        MongoDBConfig config = ConfigRegistry.getInstance().getMongoDBConfig(dbName);
        if (config == null) {
            // Fall back to MongoDBConfigLoader if not found in registry
            config = MongoDBConfigLoader.loadConfig(dbName);
        }
        return client.getDatabase(config.getDatabase());
    }

    /**
     * Creates a MongoClient from a MongoDBConfig
     *
     * @param config MongoDB configuration
     * @return MongoClient configured with the provided settings
     */
    private static MongoClient createMongoClient(MongoDBConfig config) {
        String mongoUri = config.getUri();

        // Check if we have an SSH config or need to load one
        SSHConfig sshConfig = sshConfigs.computeIfAbsent(config.getName(), k -> {
            // Load SSH configuration from registry
            SSHConfig loadedConfig = SSHConfigLoader.loadConfig();
            if (loadedConfig != null) {
                log.info("Loaded SSH configuration for MongoDB database: {}", config.getName());
            }
            return loadedConfig;
        });

        // If SSH config exists and is enabled, create tunnel and modify MongoDB URI
        if (sshConfig != null && sshConfig.isEnabled()) {
            try {
                // Create SSH tunnel
                mongoUri = createTunnelAndGetMongoUri(config.getUri(), sshConfig);
                log.info("Using tunneled MongoDB URI: {}", mongoUri);
            } catch (Exception e) {
                log.error("Failed to create SSH tunnel: {}", e.getMessage(), e);
                // Fall back to direct connection
                log.warn("Falling back to direct connection: {}", config.getUri());
                mongoUri = config.getUri();
            }
        }

        // Create MongoDB client settings
        MongoClientSettings.Builder settingsBuilder = MongoClientSettings.builder()
                .applyConnectionString(new ConnectionString(mongoUri))
                .applyToConnectionPoolSettings(builder ->
                    builder.maxSize(config.getMaxPoolSize())
                           .minSize(config.getMinPoolSize())
                           .maxConnectionIdleTime(config.getMaxIdleTimeMS(), TimeUnit.MILLISECONDS)
                           .maxConnectionLifeTime(config.getMaxLifeTimeMS(), TimeUnit.MILLISECONDS)
                )
                .applyToSocketSettings(builder ->
                    builder.connectTimeout(config.getConnectTimeout(), TimeUnit.MILLISECONDS)
                           .readTimeout(config.getSocketTimeout(), TimeUnit.MILLISECONDS)
                );

        // Add credentials if username and password are provided
        if (config.getUsername() != null && !config.getUsername().isEmpty() &&
            config.getPassword() != null && !config.getPassword().isEmpty()) {

            MongoCredential credential = MongoCredential.createCredential(
                config.getUsername(),
                config.getAuthSource(),
                config.getPassword().toCharArray()
            );
            settingsBuilder.credential(credential);
        }

        // Create and return the MongoDB client
        return MongoClients.create(settingsBuilder.build());
    }

    /**
     * Creates an SSH tunnel and returns a modified MongoDB URI that uses the tunnel
     *
     * @param originalUri Original MongoDB URI
     * @param sshConfig SSH configuration
     * @return Modified MongoDB URI that uses the local tunnel port
     * @throws URISyntaxException if the MongoDB URI is invalid
     * @throws JSchException if there's an error establishing the SSH tunnel
     */
    private static String createTunnelAndGetMongoUri(String originalUri, SSHConfig sshConfig)
            throws URISyntaxException, JSchException {
        // Parse the MongoDB URI to extract host, port, and database name
        // Format: mongodb://host:port/database
        URI uri = new URI(originalUri.replace("mongodb://", ""));

        String host = uri.getHost();
        int port = uri.getPort() > 0 ? uri.getPort() : 27017;
        String path = uri.getPath(); // Contains "/database"
        String query = uri.getQuery(); // Contains URI parameters

        // Update SSH config with remote host and port
        sshConfig = SSHConfig.builder()
                .enabled(sshConfig.isEnabled())
                .hostname(sshConfig.getHostname())
                .port(sshConfig.getPort())
                .username(sshConfig.getUsername())
                .password(sshConfig.getPassword())
                .privateKeyPath(sshConfig.getPrivateKeyPath())
                .localPort(Constants.DEFAULT_MONGODB_LOCAL_PORT)
                .remoteHost(host)
                .remotePort(port)
                .build();

        // Create the SSH tunnel
        int localPort = SSHSessionUtil.createTunnel(sshConfig);

        // Create a new MongoDB URI that uses localhost and the local port
        String newMongoUri = String.format("mongodb://localhost:%d%s", localPort, path);
        if (query != null && !query.isEmpty()) {
            newMongoUri += "?" + query;
        }

        return newMongoUri;
    }

    /**
     * Closes all MongoDB clients managed by this connection manager
     */
    public static void closeAllMongoClients() {
        for (MongoClient client : mongoClients.values()) {
            try {
                client.close();
            } catch (Exception e) {
                log.error("Error closing MongoDB client: {}", e.getMessage(), e);
            }
        }
        mongoClients.clear();

        // Close all SSH tunnels
        if (!sshConfigs.isEmpty()) {
            SSHSessionUtil.closeAllTunnels();
            sshConfigs.clear();
        }
    }

    /**
     * Closes a specific MongoDB client
     *
     * @param dbName Name of the database whose client should be closed
     */
    public static void closeMongoClient(String dbName) {
        MongoClient client = mongoClients.get(dbName);
        if (client != null) {
            try {
                client.close();
                mongoClients.remove(dbName);

                // Close associated SSH tunnel if it exists
                SSHConfig config = sshConfigs.remove(dbName);
                if (config != null) {
                    try {
                        SSHSessionUtil.closeTunnel(config);
                        log.info("Closed SSH tunnel for MongoDB database: {}", dbName);
                    } catch (Exception e) {
                        log.warn("Error closing SSH tunnel for MongoDB database {}: {}", dbName, e.getMessage());
                    }
                }
            } catch (Exception e) {
                log.error("Error closing MongoDB client: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * Registers a MongoDB configuration directly
     *
     * @param dbName Name of the database
     * @param config MongoDB configuration
     */
    public static void registerConfig(String dbName, MongoDBConfig config) {
        // If there's already a client for this database, close it
        closeMongoClient(dbName);

        // Register the configuration with the MongoDBConfigLoader
        MongoDBConfigLoader.registerConfig(dbName, config);

        // Create a new client with the provided configuration
        mongoClients.put(dbName, createMongoClient(config));

        log.info("Registered MongoDB configuration for name: {}", dbName);
    }

    /**
     * Checks if SSH tunneling is configured and enabled for a specific database
     *
     * @param dbName Name of the database
     * @return true if SSH tunneling is configured and enabled, false otherwise
     */
    public static boolean isSSHTunnelingConfigured(String dbName) {
        SSHConfig config = sshConfigs.get(dbName);
        return config != null && config.isEnabled();
    }

    /**
     * Refreshes the SSH configuration for all databases
     * This will close all existing connections and tunnels, and recreate them with the latest configuration
     */
    public static void refreshSSHConfiguration() {
        // Close all existing connections and tunnels
        closeAllMongoClients();

        // The next time a connection is requested, the SSH configuration will be reloaded
        log.info("SSH configuration refreshed. New MongoDB connections will use updated configuration.");
    }
}

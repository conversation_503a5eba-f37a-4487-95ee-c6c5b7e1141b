package com.lenskart.commons.database.mysql;

import lombok.extern.slf4j.Slf4j;

import java.sql.*;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Dynamic query executor that can execute queries on any cluster and database
 */
@Slf4j
public class MySQLQueryExecutor {

    /**
     * Executes a SELECT query and returns results as a list of maps
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @param query SQL query to execute
     * @return List of result rows as maps
     * @throws SQLException if query execution fails
     */
    public static List<Map<String, Object>> executeQuery(String clusterName, String databaseName, String query) 
            throws SQLException {
        log.info("Executing query on cluster: {}, database: {}", clusterName, databaseName);
        log.debug("Query: {}", query);
        
        List<Map<String, Object>> results = new ArrayList<>();
        
        try (Connection connection = MySQLConnectionManager.getConnection(clusterName, databaseName);
             Statement statement = connection.createStatement();
             ResultSet resultSet = statement.executeQuery(query)) {
            
            ResultSetMetaData metaData = resultSet.getMetaData();
            int columnCount = metaData.getColumnCount();
            
            while (resultSet.next()) {
                Map<String, Object> row = new HashMap<>();
                for (int i = 1; i <= columnCount; i++) {
                    String columnName = metaData.getColumnName(i);
                    Object value = resultSet.getObject(i);
                    row.put(columnName, value);
                }
                results.add(row);
            }
            
            log.info("Query executed successfully. Returned {} rows", results.size());
            log.info("Result: {}", results);
            return results;
            
        } catch (SQLException e) {
            log.error("Error executing query on cluster: {}, database: {} - {}", 
                    clusterName, databaseName, e.getMessage());
            throw new RuntimeException("Error executing query: " + e.getMessage(), e);
        }
    }

    /**
     * Executes a SELECT query with parameters and returns results as a list of maps
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @param query SQL query with placeholders (?)
     * @param parameters Parameters to bind to the query
     * @return List of result rows as maps
     */
    public static List<Map<String, Object>> executeQuery(String clusterName, String databaseName, 
                                                         String query, Object... parameters) {
        log.info("Executing parameterized query on cluster: {}, database: {}", clusterName, databaseName);
        log.debug("Query: {}, Parameters: {}", query, java.util.Arrays.toString(parameters));
        
        List<Map<String, Object>> results = new ArrayList<>();
        
        try (Connection connection = MySQLConnectionManager.getConnection(clusterName, databaseName);
             PreparedStatement statement = connection.prepareStatement(query)) {
            
            // Set parameters
            for (int i = 0; i < parameters.length; i++) {
                statement.setObject(i + 1, parameters[i]);
            }
            
            try (ResultSet resultSet = statement.executeQuery()) {
                ResultSetMetaData metaData = resultSet.getMetaData();
                int columnCount = metaData.getColumnCount();
                
                while (resultSet.next()) {
                    Map<String, Object> row = new HashMap<>();
                    for (int i = 1; i <= columnCount; i++) {
                        String columnName = metaData.getColumnName(i);
                        Object value = resultSet.getObject(i);
                        row.put(columnName, value);
                    }
                    results.add(row);
                }
            }
            
            log.info("Parameterized query executed successfully. Returned {} rows", results.size());
            log.info("Result: {}", results);
            return results;
            
        } catch (SQLException e) {
            log.error("Error executing parameterized query on cluster: {}, database: {} - {}", 
                    clusterName, databaseName, e.getMessage());
            throw new RuntimeException("Error executing parameterized query: " + e.getMessage(), e);
        }
    }



    /**
     * Executes a SELECT query and maps the results to a list of objects
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @param query  SQL query to execute
     * @param mapper Mapper to convert result set rows to objects
     * @param params Query parameters (optional)
     * @param <T>    Type of objects to return
     * @return List of objects mapped from the query results
     */
    public static <T> List<T> executeQuery(String clusterName, String databaseName,
                                           String query, ResultSetMapper<T> mapper, Object... params) {
        List<T> results = new ArrayList<>();


        try (Connection connection = MySQLConnectionManager.getConnection(clusterName, databaseName);
             PreparedStatement statement = connection.prepareStatement(query)) {

            // Set parameters
            for (int i = 0; i < params.length; i++) {
                statement.setObject(i + 1, params[i]);
            }


            try (ResultSet resultSet = statement.executeQuery()) {
                while (resultSet.next()) {
                    results.add(mapper.map(resultSet));
                }
            }

        } catch (SQLException e) {
            log.error("Error executing parameterized query on cluster: {}, database: {} - {}",
                    clusterName, databaseName, e.getMessage());
            throw new RuntimeException("Error executing parameterized query: " + e.getMessage(), e);
        }
        return results;
    }

    /**
     * Executes an UPDATE, INSERT, or DELETE query
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @param query SQL query to execute
     * @return Number of affected rows
     */
    public static int executeUpdate(String clusterName, String databaseName, String query){
        log.info("Executing update on cluster: {}, database: {}", clusterName, databaseName);
        log.debug("Query: {}", query);
        
        try (Connection connection = MySQLConnectionManager.getConnection(clusterName, databaseName);
             Statement statement = connection.createStatement()) {
            
            int affectedRows = statement.executeUpdate(query);
            log.info("Update executed successfully. Affected {} rows", affectedRows);
            return affectedRows;
            
        } catch (SQLException e) {
            log.error("Error executing update on cluster: {}, database: {} - {}", 
                    clusterName, databaseName, e.getMessage());
            throw new RuntimeException("Error executing update: " + e.getMessage(), e);
        }
    }

    /**
     * Executes an UPDATE, INSERT, or DELETE query with parameters
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @param query SQL query with placeholders (?)
     * @param parameters Parameters to bind to the query
     * @return Number of affected rows
     * @throws SQLException if query execution fails
     */
    public static int executeUpdate(String clusterName, String databaseName, String query, Object... parameters) 
            throws SQLException {
        log.info("Executing parameterized update on cluster: {}, database: {}", clusterName, databaseName);
        log.debug("Query: {}, Parameters: {}", query, java.util.Arrays.toString(parameters));
        
        try (Connection connection = MySQLConnectionManager.getConnection(clusterName, databaseName);
             PreparedStatement statement = connection.prepareStatement(query)) {
            
            // Set parameters
            for (int i = 0; i < parameters.length; i++) {
                statement.setObject(i + 1, parameters[i]);
            }
            
            int affectedRows = statement.executeUpdate();
            log.info("Parameterized update executed successfully. Affected {} rows", affectedRows);
            return affectedRows;
            
        } catch (SQLException e) {
            log.error("Error executing parameterized update on cluster: {}, database: {} - {}", 
                    clusterName, databaseName, e.getMessage());
            throw new RuntimeException("Error executing parameterized update: " + e.getMessage(), e);
        }
    }

    /**
     * Executes a query and returns a single result
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @param query SQL query to execute
     * @return Single result as a map, or null if no results
     * @throws SQLException if query execution fails
     */
    public static Map<String, Object> executeQuerySingle(String clusterName, String databaseName, String query) 
            throws SQLException {
        List<Map<String, Object>> results = executeQuery(clusterName, databaseName, query);
        return results.isEmpty() ? null : results.get(0);
    }

    /**
     * Executes a query and returns a single result with parameters
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @param query SQL query with placeholders (?)
     * @param parameters Parameters to bind to the query
     * @return Single result as a map, or null if no results
     */
    public static Map<String, Object> executeQuerySingle(String clusterName, String databaseName, 
                                                         String query, Object... parameters){
        List<Map<String, Object>> results = executeQuery(clusterName, databaseName, query, parameters);
        return results.isEmpty() ? null : results.get(0);
    }

    /**
     * Executes a query and returns a single scalar value
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @param query SQL query to execute
     * @return Single scalar value, or null if no results
     * @throws SQLException if query execution fails
     */
    public static Object executeScalar(String clusterName, String databaseName, String query) throws SQLException {
        Map<String, Object> result = executeQuerySingle(clusterName, databaseName, query);
        if (result == null || result.isEmpty()) {
            return null;
        }
        return result.values().iterator().next();
    }

    /**
     * Executes a query and returns a single scalar value with parameters
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @param query SQL query with placeholders (?)
     * @param parameters Parameters to bind to the query
     * @return Single scalar value, or null if no results
     * @throws SQLException if query execution fails
     */
    public static Object executeScalar(String clusterName, String databaseName, String query, Object... parameters) 
            throws SQLException {
        Map<String, Object> result = executeQuerySingle(clusterName, databaseName, query, parameters);
        if (result == null || result.isEmpty()) {
            return null;
        }
        return result.values().iterator().next();
    }

    /**
     * Executes multiple queries in a transaction
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @param queries List of SQL queries to execute
     * @return Array of affected row counts for each query
     * @throws SQLException if any query execution fails
     */
    public static int[] executeTransaction(String clusterName, String databaseName, List<String> queries) 
            throws SQLException {
        log.info("Executing transaction with {} queries on cluster: {}, database: {}", 
                queries.size(), clusterName, databaseName);
        
        Connection connection = null;
        try {
            connection = MySQLConnectionManager.getConnection(clusterName, databaseName);
            connection.setAutoCommit(false);
            
            int[] results = new int[queries.size()];
            
            for (int i = 0; i < queries.size(); i++) {
                try (Statement statement = connection.createStatement()) {
                    results[i] = statement.executeUpdate(queries.get(i));
                }
            }
            
            connection.commit();
            log.info("Transaction executed successfully");
            return results;
            
        } catch (SQLException e) {
            if (connection != null) {
                try {
                    connection.rollback();
                    log.warn("Transaction rolled back due to error: {}", e.getMessage());
                } catch (SQLException rollbackEx) {
                    log.error("Error during rollback: {}", rollbackEx.getMessage());
                }
            }
            throw e;
        } finally {
            if (connection != null) {
                try {
                    connection.setAutoCommit(true);
                    connection.close();
                } catch (SQLException e) {
                    log.warn("Error closing connection: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * Tests connectivity to a cluster and database
     *
     * @param clusterName Name of the cluster
     * @param databaseName Name of the database
     * @return true if connection is successful, false otherwise
     */
    public static boolean testConnection(String clusterName, String databaseName) {
        try {
            Object result = executeScalar(clusterName, databaseName, "SELECT 1");
            boolean success = "1".equals(String.valueOf(result));
            log.info("Connection test for cluster: {}, database: {} - {}", 
                    clusterName, databaseName, success ? "SUCCESS" : "FAILED");
            return success;
        } catch (Exception e) {
            log.error("Connection test failed for cluster: {}, database: {} - {}", 
                    clusterName, databaseName, e.getMessage());
            return false;
        }
    }
}

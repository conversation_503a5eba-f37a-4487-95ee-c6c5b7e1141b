package com.lenskart.commons.database.mongodb;

import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.MongoDatabase;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.UpdateOptions;
import com.mongodb.client.model.Updates;
import com.mongodb.client.result.DeleteResult;
import com.mongodb.client.result.InsertManyResult;
import com.mongodb.client.result.InsertOneResult;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;

/**
 * Utility class for executing MongoDB queries
 */
@Slf4j
public class MongoDBQueryExecutor {

    /**
     * Gets a MongoDB collection
     *
     * @param dbName         Name of the MongoDB database
     * @param collectionName Name of the collection
     * @return MongoCollection object
     */
    public static MongoCollection<Document> getCollection(String dbName, String collectionName) {
        MongoDatabase database = MongoDBConnectionManager.getMongoDatabase(dbName);
        return database.getCollection(collectionName);
    }

    /**
     * Finds documents in a collection
     *
     * @param dbName         Name of the MongoDB database
     * @param collectionName Name of the collection
     * @param filter         Filter to apply (can be null for all documents)
     * @return List of documents
     */
    public static List<Document> find(String dbName, String collectionName, Bson filter) {
        MongoCollection<Document> collection = getCollection(dbName, collectionName);
        FindIterable<Document> findIterable = filter != null ? collection.find(filter) : collection.find();
        
        List<Document> results = new ArrayList<>();
        try (MongoCursor<Document> cursor = findIterable.iterator()) {
            while (cursor.hasNext()) {
                results.add(cursor.next());
            }
        } catch (Exception e) {
            log.error("Error executing find query on collection {}: {}", collectionName, e.getMessage(), e);
            throw new RuntimeException("Error executing find query: " + e.getMessage(), e);
        }
        
        return results;
    }

    /**
     * Finds all documents in a collection
     *
     * @param dbName         Name of the MongoDB database
     * @param collectionName Name of the collection
     * @return List of documents
     */
    public static List<Document> findAll(String dbName, String collectionName) {
        return find(dbName, collectionName, null);
    }

    /**
     * Finds a document by its ID
     *
     * @param dbName         Name of the MongoDB database
     * @param collectionName Name of the collection
     * @param id             Document ID
     * @return Document if found, null otherwise
     */
    public static Document findById(String dbName, String collectionName, String id) {
        MongoCollection<Document> collection = getCollection(dbName, collectionName);
        try {
            return collection.find(Filters.eq("_id", new ObjectId(id))).first();
        } catch (Exception e) {
            log.error("Error executing findById query on collection {}: {}", collectionName, e.getMessage(), e);
            throw new RuntimeException("Error executing findById query: " + e.getMessage(), e);
        }
    }

    /**
     * Inserts a document into a collection
     *
     * @param dbName         Name of the MongoDB database
     * @param collectionName Name of the collection
     * @param document       Document to insert
     * @return ID of the inserted document
     */
    public static String insertOne(String dbName, String collectionName, Document document) {
        MongoCollection<Document> collection = getCollection(dbName, collectionName);
        try {
            InsertOneResult result = collection.insertOne(document);
            return result.getInsertedId().asObjectId().getValue().toString();
        } catch (Exception e) {
            log.error("Error executing insertOne on collection {}: {}", collectionName, e.getMessage(), e);
            throw new RuntimeException("Error executing insertOne: " + e.getMessage(), e);
        }
    }

    /**
     * Inserts multiple documents into a collection
     *
     * @param dbName         Name of the MongoDB database
     * @param collectionName Name of the collection
     * @param documents      List of documents to insert
     * @return Number of documents inserted
     */
    public static int insertMany(String dbName, String collectionName, List<Document> documents) {
        MongoCollection<Document> collection = getCollection(dbName, collectionName);
        try {
            InsertManyResult result = collection.insertMany(documents);
            return result.getInsertedIds().size();
        } catch (Exception e) {
            log.error("Error executing insertMany on collection {}: {}", collectionName, e.getMessage(), e);
            throw new RuntimeException("Error executing insertMany: " + e.getMessage(), e);
        }
    }

    /**
     * Updates a document in a collection
     *
     * @param dbName         Name of the MongoDB database
     * @param collectionName Name of the collection
     * @param filter         Filter to identify documents to update
     * @param updates        Updates to apply
     * @param upsert         Whether to insert if no document matches the filter
     * @return Number of documents updated
     */
    public static long updateOne(String dbName, String collectionName, Bson filter, Map<String, Object> updates, boolean upsert) {
        MongoCollection<Document> collection = getCollection(dbName, collectionName);
        
        List<Bson> updateOperations = new ArrayList<>();
        for (Map.Entry<String, Object> entry : updates.entrySet()) {
            updateOperations.add(Updates.set(entry.getKey(), entry.getValue()));
        }
        
        Bson combinedUpdate = Updates.combine(updateOperations);
        
        try {
            UpdateResult result = collection.updateOne(filter, combinedUpdate, new UpdateOptions().upsert(upsert));
            return result.getModifiedCount();
        } catch (Exception e) {
            log.error("Error executing updateOne on collection {}: {}", collectionName, e.getMessage(), e);
            throw new RuntimeException("Error executing updateOne: " + e.getMessage(), e);
        }
    }

    /**
     * Updates multiple documents in a collection
     *
     * @param dbName         Name of the MongoDB database
     * @param collectionName Name of the collection
     * @param filter         Filter to identify documents to update
     * @param updates        Updates to apply
     * @return Number of documents updated
     */
    public static long updateMany(String dbName, String collectionName, Bson filter, Map<String, Object> updates) {
        MongoCollection<Document> collection = getCollection(dbName, collectionName);
        
        List<Bson> updateOperations = new ArrayList<>();
        for (Map.Entry<String, Object> entry : updates.entrySet()) {
            updateOperations.add(Updates.set(entry.getKey(), entry.getValue()));
        }
        
        Bson combinedUpdate = Updates.combine(updateOperations);
        
        try {
            UpdateResult result = collection.updateMany(filter, combinedUpdate);
            return result.getModifiedCount();
        } catch (Exception e) {
            log.error("Error executing updateMany on collection {}: {}", collectionName, e.getMessage(), e);
            throw new RuntimeException("Error executing updateMany: " + e.getMessage(), e);
        }
    }

    /**
     * Deletes a document from a collection
     *
     * @param dbName         Name of the MongoDB database
     * @param collectionName Name of the collection
     * @param filter         Filter to identify document to delete
     * @return Number of documents deleted
     */
    public static long deleteOne(String dbName, String collectionName, Bson filter) {
        MongoCollection<Document> collection = getCollection(dbName, collectionName);
        try {
            DeleteResult result = collection.deleteOne(filter);
            return result.getDeletedCount();
        } catch (Exception e) {
            log.error("Error executing deleteOne on collection {}: {}", collectionName, e.getMessage(), e);
            throw new RuntimeException("Error executing deleteOne: " + e.getMessage(), e);
        }
    }

    /**
     * Deletes multiple documents from a collection
     *
     * @param dbName         Name of the MongoDB database
     * @param collectionName Name of the collection
     * @param filter         Filter to identify documents to delete
     * @return Number of documents deleted
     */
    public static long deleteMany(String dbName, String collectionName, Bson filter) {
        MongoCollection<Document> collection = getCollection(dbName, collectionName);
        try {
            DeleteResult result = collection.deleteMany(filter);
            return result.getDeletedCount();
        } catch (Exception e) {
            log.error("Error executing deleteMany on collection {}: {}", collectionName, e.getMessage(), e);
            throw new RuntimeException("Error executing deleteMany: " + e.getMessage(), e);
        }
    }

    /**
     * Counts documents in a collection
     *
     * @param dbName         Name of the MongoDB database
     * @param collectionName Name of the collection
     * @param filter         Filter to apply (can be null for all documents)
     * @return Number of documents
     */
    public static long count(String dbName, String collectionName, Bson filter) {
        MongoCollection<Document> collection = getCollection(dbName, collectionName);
        try {
            return filter != null ? collection.countDocuments(filter) : collection.countDocuments();
        } catch (Exception e) {
            log.error("Error executing count on collection {}: {}", collectionName, e.getMessage(), e);
            throw new RuntimeException("Error executing count: " + e.getMessage(), e);
        }
    }

    /**
     * Executes an aggregation pipeline
     *
     * @param dbName         Name of the MongoDB database
     * @param collectionName Name of the collection
     * @param pipeline       Aggregation pipeline
     * @return List of documents
     */
    public static List<Document> aggregate(String dbName, String collectionName, List<Bson> pipeline) {
        MongoCollection<Document> collection = getCollection(dbName, collectionName);
        List<Document> results = new ArrayList<>();
        
        try {
            collection.aggregate(pipeline).forEach((Consumer<Document>) results::add);
            return results;
        } catch (Exception e) {
            log.error("Error executing aggregate on collection {}: {}", collectionName, e.getMessage(), e);
            throw new RuntimeException("Error executing aggregate: " + e.getMessage(), e);
        }
    }

    /**
     * Creates an index on a collection
     *
     * @param dbName         Name of the MongoDB database
     * @param collectionName Name of the collection
     * @param keys           Index keys
     * @return Name of the created index
     */
    public static String createIndex(String dbName, String collectionName, Bson keys) {
        MongoCollection<Document> collection = getCollection(dbName, collectionName);
        try {
            return collection.createIndex(keys);
        } catch (Exception e) {
            log.error("Error creating index on collection {}: {}", collectionName, e.getMessage(), e);
            throw new RuntimeException("Error creating index: " + e.getMessage(), e);
        }
    }

    /**
     * Drops an index from a collection
     *
     * @param dbName         Name of the MongoDB database
     * @param collectionName Name of the collection
     * @param indexName      Name of the index to drop
     */
    public static void dropIndex(String dbName, String collectionName, String indexName) {
        MongoCollection<Document> collection = getCollection(dbName, collectionName);
        try {
            collection.dropIndex(indexName);
        } catch (Exception e) {
            log.error("Error dropping index on collection {}: {}", collectionName, e.getMessage(), e);
            throw new RuntimeException("Error dropping index: " + e.getMessage(), e);
        }
    }
}

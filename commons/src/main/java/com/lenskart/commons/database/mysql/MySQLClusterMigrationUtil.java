package com.lenskart.commons.database.mysql;

import com.lenskart.commons.config.DatabaseConfig;
import com.lenskart.commons.config.MySQLClusterConfig;
import com.lenskart.commons.loader.ConfigRegistry;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * Utility class to help migrate from individual database configurations
 * to MySQL cluster configuration
 */
@Slf4j
public class MySQLClusterMigrationUtil {

    /**
     * Analyzes existing database configurations and suggests cluster migration
     */
    public static void analyzeForClusterMigration() {
        log.info("=== MySQL Cluster Migration Analysis ===");
        
        ConfigRegistry registry = ConfigRegistry.getInstance();
        Map<String, DatabaseConfig> allDatabases = registry.getAllDatabaseConfigs();
        
        if (allDatabases.isEmpty()) {
            log.info("No database configurations found");
            return;
        }
        
        // Group databases by host
        Map<String, Set<String>> hostGroups = new HashMap<>();
        Map<String, DatabaseConfig> hostConfigs = new HashMap<>();
        
        for (Map.Entry<String, DatabaseConfig> entry : allDatabases.entrySet()) {
            String dbName = entry.getKey();
            DatabaseConfig config = entry.getValue();
            
            String host = extractHostFromUrl(config.getUrl());
            if (host != null) {
                hostGroups.computeIfAbsent(host, k -> new java.util.HashSet<>()).add(dbName);
                hostConfigs.putIfAbsent(host, config);
            }
        }
        
        log.info("Found {} unique database hosts:", hostGroups.size());
        
        for (Map.Entry<String, Set<String>> entry : hostGroups.entrySet()) {
            String host = entry.getKey();
            Set<String> databases = entry.getValue();
            
            log.info("Host: {} - Databases: {}", host, databases);
            
            if (databases.size() > 1) {
                log.info("  ✅ CLUSTER CANDIDATE: {} databases on same host", databases.size());
                suggestClusterConfiguration(host, databases, allDatabases);
            } else {
                log.info("  ⚠️  Single database on host - consider individual config");
            }
        }
    }
    
    /**
     * Suggests cluster configuration for databases on the same host
     */
    private static void suggestClusterConfiguration(String host, Set<String> databases, 
                                                   Map<String, DatabaseConfig> allConfigs) {
        log.info("\n--- Suggested Cluster Configuration for {} ---", host);
        
        // Find common settings
        DatabaseConfig firstConfig = null;
        for (String dbName : databases) {
            firstConfig = allConfigs.get(dbName);
            break;
        }
        
        if (firstConfig == null) return;
        
        String[] hostPort = host.split(":");
        String hostname = hostPort[0];
        int port = hostPort.length > 1 ? Integer.parseInt(hostPort[1]) : 3306;
        
        log.info("mysqlCluster:");
        log.info("  host: {}", hostname);
        log.info("  port: {}", port);
        log.info("  username: {}", firstConfig.getUsername());
        log.info("  password: {}", firstConfig.getPassword());
        log.info("  driver: {}", firstConfig.getDriverClassName());
        log.info("  maxPoolSize: {}", firstConfig.getMaxPoolSize());
        log.info("  minIdle: {}", firstConfig.getMinIdle());
        log.info("  connectionTimeout: {}", firstConfig.getConnectionTimeout());
        log.info("  idleTimeout: {}", firstConfig.getIdleTimeout());
        log.info("  maxLifetime: {}", firstConfig.getMaxLifetime());
        log.info("  databases:");
        
        for (String dbName : databases) {
            DatabaseConfig config = allConfigs.get(dbName);
            String actualDbName = extractDatabaseFromUrl(config.getUrl());
            
            log.info("    {}:", dbName);
            log.info("      databaseName: {}", actualDbName);
            
            // Check for differences from common settings
            if (!firstConfig.getUsername().equals(config.getUsername())) {
                log.info("      username: {}", config.getUsername());
            }
            if (!firstConfig.getPassword().equals(config.getPassword())) {
                log.info("      password: {}", config.getPassword());
            }
            if (firstConfig.getMaxPoolSize() != config.getMaxPoolSize()) {
                log.info("      maxPoolSize: {}", config.getMaxPoolSize());
            }
            if (firstConfig.getMinIdle() != config.getMinIdle()) {
                log.info("      minIdle: {}", config.getMinIdle());
            }
        }
        
        log.info("--- End Suggestion ---\n");
    }
    
    /**
     * Validates that cluster configuration covers all required databases
     */
    public static boolean validateClusterCoverage() {
        log.info("=== Validating MySQL Cluster Coverage ===");
        
        ConfigRegistry registry = ConfigRegistry.getInstance();
        
        if (!registry.hasMySQLClusterConfig()) {
            log.warn("No MySQL cluster configuration found");
            return false;
        }
        
        MySQLClusterConfig clusterConfig = registry.getMySQLClusterConfig();
        Map<String, DatabaseConfig> allDatabases = registry.getAllDatabaseConfigs();
        
        Set<String> configuredDatabases = clusterConfig.getConfiguredDatabases();
        
        log.info("Cluster covers {} databases: {}", configuredDatabases.size(), configuredDatabases);
        log.info("Total databases in system: {}", allDatabases.size());
        
        boolean allCovered = true;
        
        for (String dbName : allDatabases.keySet()) {
            if (configuredDatabases.contains(dbName)) {
                log.info("  ✅ {} - Covered by cluster", dbName);
            } else {
                log.warn("  ❌ {} - NOT covered by cluster (using individual config)", dbName);
                allCovered = false;
            }
        }
        
        if (allCovered) {
            log.info("✅ All databases are covered by cluster configuration");
        } else {
            log.warn("⚠️  Some databases are not covered by cluster configuration");
        }
        
        return allCovered;
    }
    
    /**
     * Compares cluster configuration with individual configurations
     */
    public static void compareConfigurations() {
        log.info("=== Comparing Cluster vs Individual Configurations ===");
        
        ConfigRegistry registry = ConfigRegistry.getInstance();
        
        if (!registry.hasMySQLClusterConfig()) {
            log.warn("No MySQL cluster configuration to compare");
            return;
        }
        
        MySQLClusterConfig clusterConfig = registry.getMySQLClusterConfig();
        
        for (String dbName : clusterConfig.getConfiguredDatabases()) {
            log.info("\n--- Comparing configuration for {} ---", dbName);
            
            // Get cluster-generated config
            DatabaseConfig clusterDbConfig = clusterConfig.createDatabaseConfig(dbName);
            
            // Get individual config (if exists)
            DatabaseConfig individualConfig = registry.getDatabaseConfig(dbName);
            
            if (individualConfig == null) {
                log.info("No individual configuration found for {}", dbName);
                continue;
            }
            
            // Compare configurations
            compareField("URL", clusterDbConfig.getUrl(), individualConfig.getUrl());
            compareField("Username", clusterDbConfig.getUsername(), individualConfig.getUsername());
            compareField("Password", clusterDbConfig.getPassword(), individualConfig.getPassword());
            compareField("Driver", clusterDbConfig.getDriverClassName(), individualConfig.getDriverClassName());
            compareField("Max Pool Size", clusterDbConfig.getMaxPoolSize(), individualConfig.getMaxPoolSize());
            compareField("Min Idle", clusterDbConfig.getMinIdle(), individualConfig.getMinIdle());
            compareField("Connection Timeout", clusterDbConfig.getConnectionTimeout(), individualConfig.getConnectionTimeout());
            compareField("Idle Timeout", clusterDbConfig.getIdleTimeout(), individualConfig.getIdleTimeout());
            compareField("Max Lifetime", clusterDbConfig.getMaxLifetime(), individualConfig.getMaxLifetime());
        }
    }
    
    /**
     * Extracts host:port from JDBC URL
     */
    private static String extractHostFromUrl(String url) {
        try {
            // Extract from *******************************
            String[] parts = url.split("//")[1].split("/");
            return parts[0]; // host:port
        } catch (Exception e) {
            log.warn("Could not extract host from URL: {}", url);
            return null;
        }
    }
    
    /**
     * Extracts database name from JDBC URL
     */
    private static String extractDatabaseFromUrl(String url) {
        try {
            // Extract from **************************************
            String[] parts = url.split("//")[1].split("/");
            if (parts.length > 1) {
                String dbPart = parts[1];
                // Remove query parameters
                return dbPart.split("\\?")[0];
            }
        } catch (Exception e) {
            log.warn("Could not extract database from URL: {}", url);
        }
        return "unknown";
    }
    
    /**
     * Compares two configuration fields
     */
    private static void compareField(String fieldName, Object clusterValue, Object individualValue) {
        if (java.util.Objects.equals(clusterValue, individualValue)) {
            log.info("  ✅ {}: {} (matches)", fieldName, clusterValue);
        } else {
            log.warn("  ❌ {}: cluster={}, individual={} (DIFFERENT)", fieldName, clusterValue, individualValue);
        }
    }
    
    /**
     * Generates sample cluster configuration based on existing individual configs
     */
    public static void generateSampleClusterConfig() {
        log.info("=== Generating Sample Cluster Configuration ===");
        
        ConfigRegistry registry = ConfigRegistry.getInstance();
        Map<String, DatabaseConfig> allDatabases = registry.getAllDatabaseConfigs();
        
        if (allDatabases.isEmpty()) {
            log.info("No database configurations found to generate sample from");
            return;
        }
        
        // Use first database as template
        DatabaseConfig template = allDatabases.values().iterator().next();
        String templateHost = extractHostFromUrl(template.getUrl());
        
        log.info("# Sample MySQL Cluster Configuration");
        log.info("mysqlCluster:");
        log.info("  host: {}", templateHost != null ? templateHost.split(":")[0] : "mysql-cluster.example.com");
        log.info("  port: 3306");
        log.info("  username: {}", template.getUsername());
        log.info("  password: {}", template.getPassword());
        log.info("  driver: {}", template.getDriverClassName());
        log.info("  maxPoolSize: {}", template.getMaxPoolSize());
        log.info("  minIdle: {}", template.getMinIdle());
        log.info("  connectionTimeout: {}", template.getConnectionTimeout());
        log.info("  idleTimeout: {}", template.getIdleTimeout());
        log.info("  maxLifetime: {}", template.getMaxLifetime());
        log.info("  databases:");
        
        for (Map.Entry<String, DatabaseConfig> entry : allDatabases.entrySet()) {
            String dbName = entry.getKey();
            DatabaseConfig config = entry.getValue();
            String actualDbName = extractDatabaseFromUrl(config.getUrl());
            
            log.info("    {}:", dbName);
            log.info("      databaseName: {}", actualDbName);
        }
    }
}

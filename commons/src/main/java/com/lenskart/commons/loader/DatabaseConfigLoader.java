package com.lenskart.commons.loader;

import com.lenskart.commons.config.DatabaseConfig;
import lombok.extern.slf4j.Slf4j;

/**
 * Facade for loading database configurations
 */
@Slf4j
public class DatabaseConfigLoader {

    /**
     * Load database configuration
     *
     * @param dbName Database name
     * @return Database configuration
     */
    public static DatabaseConfig loadConfig(String dbName) {
        DatabaseConfig config = ConfigRegistry.getInstance().getDatabaseConfig(dbName);

        if (config == null) {
            log.warn("Database configuration not found for name: {}", dbName);
            return createDefaultConfig(dbName);
        }

        return config;
    }

    /**
     * Create default database configuration
     *
     * @param dbName Database name
     * @return Default database configuration
     */
    private static DatabaseConfig createDefaultConfig(String dbName) {
        return DatabaseConfig.builder()
                .name(dbName)
                .url("***************************/" + dbName)
                .username("root")
                .password("password")
                .driverClassName("com.mysql.cj.jdbc.Driver")
                .maxPoolSize(10)
                .minIdle(5)
                .connectionTimeout(30000)
                .idleTimeout(600000)
                .maxLifetime(1800000)
                .build();
    }

    /**
     * Register a database configuration
     *
     * @param dbName Database name
     * @param config Database configuration
     */
    public static void registerConfig(String dbName, DatabaseConfig config) {
        // Refresh the ConfigRegistry to ensure it picks up the new configuration
        // In a real implementation, we would directly update the ConfigRegistry
        // but for now we'll just refresh it
        ConfigRegistry.getInstance().refresh();
    }

    /**
     * Clear the configuration cache
     */
    public static void clearCache() {
        ConfigRegistry.getInstance().refresh();
    }
}

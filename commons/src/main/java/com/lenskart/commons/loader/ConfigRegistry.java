package com.lenskart.commons.loader;

import com.lenskart.commons.config.DatabaseConfig;
import com.lenskart.commons.config.MongoDBConfig;
import com.lenskart.commons.config.MySQLClusterConfig;
import com.lenskart.commons.config.MySQLMultiClusterConfig;
import com.lenskart.commons.config.SSHConfig;
import com.lenskart.commons.config.RedisConfig;
import com.lenskart.commons.config.ElasticsearchConfig;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Central registry for all configurations
 */
@Slf4j
public class ConfigRegistry {
    // Singleton instance
    private static ConfigRegistry instance;

    // Configuration caches
    private final Map<String, DatabaseConfig> databaseConfigs = new ConcurrentHashMap<>();
    private final Map<String, MongoDBConfig> mongoDBConfigs = new ConcurrentHashMap<>();
    private final Map<String, RedisConfig> redisConfigs = new ConcurrentHashMap<>();
    private final Map<String, ElasticsearchConfig> elasticsearchConfigs = new ConcurrentHashMap<>();
    private MySQLClusterConfig mysqlClusterConfig;
    private MySQLMultiClusterConfig mysqlMultiClusterConfig;
    private SSHConfig sshConfig;
    private final Map<String, String> baseUrls = new ConcurrentHashMap<>();

    // Constants
    private static final String DATABASES_SECTION = "databases";
    private static final String MYSQL_CLUSTER_SECTION = "mysqlCluster";
    private static final String MYSQL_MULTI_CLUSTER_SECTION = "mysqlClusters";
    private static final String MONGODB_SECTION = "mongodb";
    private static final String REDIS_SECTION = "redis";
    private static final String ELASTICSEARCH_SECTION = "elasticsearch";
    private static final String SSH_CONFIG_SECTION = "sshConfig";
    private static final String BASE_URLS_SECTION = "baseUrls";

    // Private constructor for singleton
    private ConfigRegistry() {
        // Initialize configurations
        loadConfigurations();
    }

    /**
     * Get singleton instance
     *
     * @return ConfigRegistry instance
     */
    public static synchronized ConfigRegistry getInstance() {
        if (instance == null) {
            instance = new ConfigRegistry();
        }
        return instance;
    }

    /**
     * Load all configurations
     */
    private void loadConfigurations() {
        loadDatabaseConfigs();
        loadMySQLClusterConfig();
        loadMySQLMultiClusterConfig();
        loadMongoDBConfigs();
        loadRedisConfigs();
        loadElasticsearchConfigs();
        loadSSHConfig();
        loadBaseUrls();
    }

    /**
     * Load database configurations
     */
    @SuppressWarnings("unchecked")
    private void loadDatabaseConfigs() {
        ConfigLoader loader = ConfigLoader.getInstance();
        Map<String, Object> dbSection = loader.getConfigSection(DATABASES_SECTION);

        for (Map.Entry<String, Object> entry : dbSection.entrySet()) {
            String dbName = entry.getKey();
            Map<String, Object> dbConfig = (Map<String, Object>) entry.getValue();

            DatabaseConfig config = DatabaseConfig.builder()
                    .name(dbName)
                    .url((String) dbConfig.get("url"))
                    .username((String) dbConfig.get("username"))
                    .password((String) dbConfig.get("password"))
                    .driverClassName((String) dbConfig.getOrDefault("driver", "com.mysql.cj.jdbc.Driver"))
                    .maxPoolSize(((Number) dbConfig.getOrDefault("maxPoolSize", 10)).intValue())
                    .minIdle(((Number) dbConfig.getOrDefault("minIdle", 5)).intValue())
                    .connectionTimeout(((Number) dbConfig.getOrDefault("connectionTimeout", 30000)).intValue())
                    .idleTimeout(((Number) dbConfig.getOrDefault("idleTimeout", 600000)).intValue())
                    .maxLifetime(((Number) dbConfig.getOrDefault("maxLifetime", 1800000)).intValue())
                    .build();

            databaseConfigs.put(dbName, config);
        }

        log.info("Loaded {} database configurations", databaseConfigs.size());
    }

    /**
     * Load MySQL cluster configuration
     */
    @SuppressWarnings("unchecked")
    private void loadMySQLClusterConfig() {
        ConfigLoader loader = ConfigLoader.getInstance();
        Map<String, Object> clusterSection = loader.getConfigSection(MYSQL_CLUSTER_SECTION);

        if (clusterSection.isEmpty()) {
            log.info("MySQL cluster configuration not found, using individual database configurations");
            return;
        }

        // Extract cluster-level settings
        String clusterHost = (String) clusterSection.get("host");
        int clusterPort = ((Number) clusterSection.getOrDefault("port", 3306)).intValue();
        String defaultUsername = (String) clusterSection.get("username");
        String defaultPassword = (String) clusterSection.get("password");
        String driverClassName = (String) clusterSection.getOrDefault("driver", "com.mysql.cj.jdbc.Driver");

        // Extract cluster-level connection pool settings
        int maxPoolSize = ((Number) clusterSection.getOrDefault("maxPoolSize", 10)).intValue();
        int minIdle = ((Number) clusterSection.getOrDefault("minIdle", 5)).intValue();
        long connectionTimeout = ((Number) clusterSection.getOrDefault("connectionTimeout", 30000)).longValue();
        long idleTimeout = ((Number) clusterSection.getOrDefault("idleTimeout", 600000)).longValue();
        long maxLifetime = ((Number) clusterSection.getOrDefault("maxLifetime", 1800000)).longValue();

        // Extract database-specific configurations
        Map<String, Object> databasesSection = (Map<String, Object>) clusterSection.get("databases");
        Map<String, MySQLClusterConfig.DatabaseSpecificConfig> databases = new HashMap<>();

        if (databasesSection != null) {
            for (Map.Entry<String, Object> entry : databasesSection.entrySet()) {
                String dbName = entry.getKey();
                Map<String, Object> dbConfig = (Map<String, Object>) entry.getValue();

                MySQLClusterConfig.DatabaseSpecificConfig.DatabaseSpecificConfigBuilder configBuilder =
                    MySQLClusterConfig.DatabaseSpecificConfig.builder()
                        .databaseName((String) dbConfig.getOrDefault("databaseName", dbName));

                // Optional overrides
                if (dbConfig.containsKey("username")) {
                    configBuilder.username((String) dbConfig.get("username"));
                }
                if (dbConfig.containsKey("password")) {
                    configBuilder.password((String) dbConfig.get("password"));
                }
                if (dbConfig.containsKey("maxPoolSize")) {
                    configBuilder.maxPoolSize(((Number) dbConfig.get("maxPoolSize")).intValue());
                }
                if (dbConfig.containsKey("minIdle")) {
                    configBuilder.minIdle(((Number) dbConfig.get("minIdle")).intValue());
                }
                if (dbConfig.containsKey("connectionTimeout")) {
                    configBuilder.connectionTimeout(((Number) dbConfig.get("connectionTimeout")).longValue());
                }
                if (dbConfig.containsKey("idleTimeout")) {
                    configBuilder.idleTimeout(((Number) dbConfig.get("idleTimeout")).longValue());
                }
                if (dbConfig.containsKey("maxLifetime")) {
                    configBuilder.maxLifetime(((Number) dbConfig.get("maxLifetime")).longValue());
                }

                // Additional JDBC parameters
                if (dbConfig.containsKey("additionalParams")) {
                    configBuilder.additionalParams((Map<String, String>) dbConfig.get("additionalParams"));
                }

                databases.put(dbName, configBuilder.build());
            }
        }

        // Build the cluster configuration
        mysqlClusterConfig = MySQLClusterConfig.builder()
                .clusterHost(clusterHost)
                .clusterPort(clusterPort)
                .defaultUsername(defaultUsername)
                .defaultPassword(defaultPassword)
                .driverClassName(driverClassName)
                .maxPoolSize(maxPoolSize)
                .minIdle(minIdle)
                .connectionTimeout(connectionTimeout)
                .idleTimeout(idleTimeout)
                .maxLifetime(maxLifetime)
                .databases(databases)
                .build();

        // Also populate individual database configs for backward compatibility
        for (String dbName : databases.keySet()) {
            DatabaseConfig dbConfig = mysqlClusterConfig.createDatabaseConfig(dbName);
            databaseConfigs.put(dbName, dbConfig);
        }

        log.info("Loaded MySQL cluster configuration with {} databases", databases.size());
    }

    /**
     * Load MySQL multi-cluster configuration
     */
    @SuppressWarnings("unchecked")
    private void loadMySQLMultiClusterConfig() {
        ConfigLoader loader = ConfigLoader.getInstance();
        Map<String, Object> multiClusterSection = loader.getConfigSection(MYSQL_MULTI_CLUSTER_SECTION);

        if (multiClusterSection.isEmpty()) {
            log.info("MySQL multi-cluster configuration not found");
            return;
        }

        Map<String, MySQLMultiClusterConfig.ClusterConfig> clusters = new HashMap<>();

        for (Map.Entry<String, Object> entry : multiClusterSection.entrySet()) {
            String clusterName = entry.getKey();
            Map<String, Object> clusterSection = (Map<String, Object>) entry.getValue();

            // Extract cluster settings
            String host = (String) clusterSection.get("host");
            int port = ((Number) clusterSection.getOrDefault("port", 3306)).intValue();
            String username = (String) clusterSection.get("username");
            String password = (String) clusterSection.get("password");
            String driverClassName = (String) clusterSection.getOrDefault("driver", "com.mysql.cj.jdbc.Driver");

            // Extract connection pool settings
            int maxPoolSize = ((Number) clusterSection.getOrDefault("maxPoolSize", 10)).intValue();
            int minIdle = ((Number) clusterSection.getOrDefault("minIdle", 5)).intValue();
            long connectionTimeout = ((Number) clusterSection.getOrDefault("connectionTimeout", 30000)).longValue();
            long idleTimeout = ((Number) clusterSection.getOrDefault("idleTimeout", 600000)).longValue();
            long maxLifetime = ((Number) clusterSection.getOrDefault("maxLifetime", 1800000)).longValue();

            // Extract additional parameters (optional)
            Map<String, String> additionalParams = null;
            if (clusterSection.containsKey("additionalParams")) {
                additionalParams = (Map<String, String>) clusterSection.get("additionalParams");
            }

            // Build cluster configuration
            MySQLMultiClusterConfig.ClusterConfig clusterConfig = MySQLMultiClusterConfig.ClusterConfig.builder()
                    .clusterName(clusterName)
                    .host(host)
                    .port(port)
                    .username(username)
                    .password(password)
                    .driverClassName(driverClassName)
                    .maxPoolSize(maxPoolSize)
                    .minIdle(minIdle)
                    .connectionTimeout(connectionTimeout)
                    .idleTimeout(idleTimeout)
                    .maxLifetime(maxLifetime)
                    .additionalParams(additionalParams)
                    .build();

            clusters.put(clusterName, clusterConfig);
        }

        // Build the multi-cluster configuration
        mysqlMultiClusterConfig = MySQLMultiClusterConfig.builder()
                .clusters(clusters)
                .build();

        // Validate configuration
        mysqlMultiClusterConfig.validateClusters();

        log.info("Loaded MySQL multi-cluster configuration with {} clusters: {}",
                clusters.size(), clusters.keySet());
    }

    /**
     * Load MongoDB configurations
     */
    @SuppressWarnings("unchecked")
    private void loadMongoDBConfigs() {
        ConfigLoader loader = ConfigLoader.getInstance();
        Map<String, Object> mongoSection = loader.getConfigSection(MONGODB_SECTION);

        for (Map.Entry<String, Object> entry : mongoSection.entrySet()) {
            String dbName = entry.getKey();
            Map<String, Object> dbConfig = (Map<String, Object>) entry.getValue();

            MongoDBConfig config = MongoDBConfig.builder()
                    .name(dbName)
                    .uri((String) dbConfig.get("uri"))
                    .database((String) dbConfig.get("database"))
                    .username((String) dbConfig.get("username"))
                    .password((String) dbConfig.get("password"))
                    .authSource((String) dbConfig.getOrDefault("authSource", "admin"))
                    .authMechanism((String) dbConfig.getOrDefault("authMechanism", "SCRAM-SHA-256"))
                    .connectTimeout(((Number) dbConfig.getOrDefault("connectTimeout", 30000)).intValue())
                    .socketTimeout(((Number) dbConfig.getOrDefault("socketTimeout", 30000)).intValue())
                    .maxPoolSize(((Number) dbConfig.getOrDefault("maxPoolSize", 100)).intValue())
                    .minPoolSize(((Number) dbConfig.getOrDefault("minPoolSize", 10)).intValue())
                    .maxIdleTimeMS(((Number) dbConfig.getOrDefault("maxIdleTimeMS", 600000)).intValue())
                    .maxLifeTimeMS(((Number) dbConfig.getOrDefault("maxLifeTimeMS", 1800000)).intValue())
                    .build();

            mongoDBConfigs.put(dbName, config);
        }

        log.info("Loaded {} MongoDB configurations", mongoDBConfigs.size());
    }

    /**
     * Load Redis configurations
     */
    @SuppressWarnings("unchecked")
    private void loadRedisConfigs() {
        ConfigLoader loader = ConfigLoader.getInstance();
        Map<String, Object> redisSection = loader.getConfigSection(REDIS_SECTION);

        for (Map.Entry<String, Object> entry : redisSection.entrySet()) {
            String redisName = entry.getKey();
            Map<String, Object> redisConfig = (Map<String, Object>) entry.getValue();

            RedisConfig config = RedisConfig.builder()
                    .name(redisName)
                    .host((String) redisConfig.get("host"))
                    .port(((Number) redisConfig.getOrDefault("port", 6379)).intValue())
                    .password((String) redisConfig.get("password"))
                    .database(((Number) redisConfig.getOrDefault("database", 0)).intValue())
                    .ssl(Boolean.parseBoolean(redisConfig.getOrDefault("ssl", "false").toString()))
                    .timeout(((Number) redisConfig.getOrDefault("timeout", 2000)).intValue())
                    .maxTotal(((Number) redisConfig.getOrDefault("maxTotal", 128)).intValue())
                    .maxIdle(((Number) redisConfig.getOrDefault("maxIdle", 128)).intValue())
                    .minIdle(((Number) redisConfig.getOrDefault("minIdle", 16)).intValue())
                    .build();

            redisConfigs.put(redisName, config);
        }

        log.info("Loaded {} Redis configurations", redisConfigs.size());
    }

    /**
     * Load Elasticsearch configurations
     */
    @SuppressWarnings("unchecked")
    private void loadElasticsearchConfigs() {
        ConfigLoader loader = ConfigLoader.getInstance();
        Map<String, Object> esSection = loader.getConfigSection(ELASTICSEARCH_SECTION);

        for (Map.Entry<String, Object> entry : esSection.entrySet()) {
            String esName = entry.getKey();
            Map<String, Object> esConfig = (Map<String, Object>) entry.getValue();

            // Get hosts list
            List<String> hosts = (List<String>) esConfig.get("hosts");

            ElasticsearchConfig config = ElasticsearchConfig.builder()
                    .name(esName)
                    .hosts(hosts)
                    .username((String) esConfig.get("username"))
                    .password((String) esConfig.get("password"))
                    .connectTimeout(((Number) esConfig.getOrDefault("connectTimeout", 5000)).intValue())
                    .readTimeout(((Number) esConfig.getOrDefault("readTimeout", 5000)).intValue())
                    .maxTotalConnections(((Number) esConfig.getOrDefault("maxTotalConnections", 20)).intValue())
                    .defaultMaxTotalConnectionsPerRoute(((Number) esConfig.getOrDefault("defaultMaxTotalConnectionsPerRoute", 10)).intValue())
                    .discoveryEnabled(Boolean.parseBoolean(esConfig.getOrDefault("discoveryEnabled", "false").toString()))
                    .multiThreaded(Boolean.parseBoolean(esConfig.getOrDefault("multiThreaded", "true").toString()))
                    .build();

            elasticsearchConfigs.put(esName, config);
        }

        log.info("Loaded {} Elasticsearch configurations", elasticsearchConfigs.size());
    }

    /**
     * Load SSH configuration
     */
    @SuppressWarnings("unchecked")
    private void loadSSHConfig() {
        ConfigLoader loader = ConfigLoader.getInstance();
        Map<String, Object> sshSection = loader.getConfigSection(SSH_CONFIG_SECTION);

        if (sshSection.isEmpty()) {
            log.warn("SSH configuration not found");
            return;
        }

        boolean enabled = Boolean.parseBoolean(sshSection.getOrDefault("enabled", "false").toString());

        if (!enabled) {
            log.info("SSH tunneling is disabled in configuration");
            return;
        }

        // Extract SSH configuration values with null safety
        Object hostnameObj = sshSection.get("hostname");
        Object usernameObj = sshSection.get("username");
        Object passwordObj = sshSection.get("password");

        if (hostnameObj == null || usernameObj == null) {
            log.error("SSH configuration is missing required fields (hostname or username)");
            return;
        }

        sshConfig = SSHConfig.builder()
                .enabled(enabled)
                .hostname(hostnameObj.toString())
                .port(((Number) sshSection.getOrDefault("port", 22)).intValue())
                .username(usernameObj.toString())
                .password(passwordObj != null ? passwordObj.toString() : null)
                .build();

        log.info("Loaded SSH configuration");
    }

    /**
     * Load base URLs for services
     */
    @SuppressWarnings("unchecked")
    private void loadBaseUrls() {
        ConfigLoader loader = ConfigLoader.getInstance();
        Map<String, Object> urlsSection = loader.getConfigSection(BASE_URLS_SECTION);

        for (Map.Entry<String, Object> entry : urlsSection.entrySet()) {
            String serviceName = entry.getKey();
            String url = entry.getValue().toString();

            baseUrls.put(serviceName, url);
        }

        log.info("Loaded {} base URLs", baseUrls.size());
    }

    /**
     * Get database configuration
     *
     * @param dbName Database name
     * @return Database configuration
     */
    public DatabaseConfig getDatabaseConfig(String dbName) {
        return databaseConfigs.get(dbName);
    }

    /**
     * Get all database configurations
     *
     * @return Map of database configurations
     */
    public Map<String, DatabaseConfig> getAllDatabaseConfigs() {
        return new HashMap<>(databaseConfigs);
    }

    /**
     * Get MySQL cluster configuration
     *
     * @return MySQL cluster configuration
     */
    public MySQLClusterConfig getMySQLClusterConfig() {
        return mysqlClusterConfig;
    }

    /**
     * Check if MySQL cluster configuration is available
     *
     * @return true if MySQL cluster configuration is available, false otherwise
     */
    public boolean hasMySQLClusterConfig() {
        return mysqlClusterConfig != null;
    }

    /**
     * Get MySQL multi-cluster configuration
     *
     * @return MySQL multi-cluster configuration
     */
    public MySQLMultiClusterConfig getMySQLMultiClusterConfig() {
        return mysqlMultiClusterConfig;
    }

    /**
     * Check if MySQL multi-cluster configuration is available
     *
     * @return true if MySQL multi-cluster configuration is available, false otherwise
     */
    public boolean hasMySQLMultiClusterConfig() {
        return mysqlMultiClusterConfig != null;
    }

    /**
     * Get MongoDB configuration
     *
     * @param dbName Database name
     * @return MongoDB configuration
     */
    public MongoDBConfig getMongoDBConfig(String dbName) {
        return mongoDBConfigs.get(dbName);
    }

    /**
     * Get all MongoDB configurations
     *
     * @return Map of MongoDB configurations
     */
    public Map<String, MongoDBConfig> getAllMongoDBConfigs() {
        return new HashMap<>(mongoDBConfigs);
    }

    /**
     * Get Redis configuration
     *
     * @param redisName Redis instance name
     * @return Redis configuration
     */
    public RedisConfig getRedisConfig(String redisName) {
        return redisConfigs.get(redisName);
    }

    /**
     * Get all Redis configurations
     *
     * @return Map of Redis configurations
     */
    public Map<String, RedisConfig> getAllRedisConfigs() {
        return new HashMap<>(redisConfigs);
    }

    /**
     * Get Elasticsearch configuration
     *
     * @param esName Elasticsearch instance name
     * @return Elasticsearch configuration
     */
    public ElasticsearchConfig getElasticsearchConfig(String esName) {
        return elasticsearchConfigs.get(esName);
    }

    /**
     * Get all Elasticsearch configurations
     *
     * @return Map of Elasticsearch configurations
     */
    public Map<String, ElasticsearchConfig> getAllElasticsearchConfigs() {
        return new HashMap<>(elasticsearchConfigs);
    }

    /**
     * Get SSH configuration
     *
     * @return SSH configuration
     */
    public SSHConfig getSSHConfig() {
        return sshConfig;
    }

    /**
     * Check if SSH tunneling is enabled
     *
     * @return true if SSH tunneling is enabled, false otherwise
     */
    public boolean isSSHTunnelingEnabled() {
        return sshConfig != null && sshConfig.isEnabled();
    }

    /**
     * Get base URL for a service
     *
     * @param serviceName Service name
     * @return Base URL
     */
    public String getBaseUrl(String serviceName) {
        return baseUrls.get(serviceName);
    }

    /**
     * Get all base URLs
     *
     * @return Map of base URLs
     */
    public Map<String, String> getAllBaseUrls() {
        return new HashMap<>(baseUrls);
    }

    /**
     * Refresh all configurations
     */
    public synchronized void refresh() {
        // Clear caches
        databaseConfigs.clear();
        mongoDBConfigs.clear();
        redisConfigs.clear();
        elasticsearchConfigs.clear();
        mysqlClusterConfig = null;
        mysqlMultiClusterConfig = null;
        sshConfig = null;
        baseUrls.clear();

        // Clear the config loader cache
        ConfigLoader.getInstance().clearCache();

        // Reload configurations
        loadConfigurations();

        log.info("All configurations refreshed");
    }
}

package com.lenskart.commons.loader;

import com.lenskart.commons.config.DatabaseConfig;
import com.lenskart.commons.config.MongoDBConfig;
import com.lenskart.commons.config.SSHConfig;
import com.lenskart.commons.config.RedisConfig;
import com.lenskart.commons.config.ElasticsearchConfig;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Central registry for all configurations
 */
@Slf4j
public class ConfigRegistry {
    // Singleton instance
    private static ConfigRegistry instance;

    // Configuration caches
    private final Map<String, DatabaseConfig> databaseConfigs = new ConcurrentHashMap<>();
    private final Map<String, MongoDBConfig> mongoDBConfigs = new ConcurrentHashMap<>();
    private final Map<String, RedisConfig> redisConfigs = new ConcurrentHashMap<>();
    private final Map<String, ElasticsearchConfig> elasticsearchConfigs = new ConcurrentHashMap<>();
    private SSHConfig sshConfig;
    private final Map<String, String> baseUrls = new ConcurrentHashMap<>();

    // Constants
    private static final String DATABASES_SECTION = "databases";
    private static final String MONGODB_SECTION = "mongodb";
    private static final String REDIS_SECTION = "redis";
    private static final String ELASTICSEARCH_SECTION = "elasticsearch";
    private static final String SSH_CONFIG_SECTION = "sshConfig";
    private static final String BASE_URLS_SECTION = "baseUrls";

    // Private constructor for singleton
    private ConfigRegistry() {
        // Initialize configurations
        loadConfigurations();
    }

    /**
     * Get singleton instance
     *
     * @return ConfigRegistry instance
     */
    public static synchronized ConfigRegistry getInstance() {
        if (instance == null) {
            instance = new ConfigRegistry();
        }
        return instance;
    }

    /**
     * Load all configurations
     */
    private void loadConfigurations() {
        loadDatabaseConfigs();
        loadMongoDBConfigs();
        loadRedisConfigs();
        loadElasticsearchConfigs();
        loadSSHConfig();
        loadBaseUrls();
    }

    /**
     * Load database configurations
     */
    @SuppressWarnings("unchecked")
    private void loadDatabaseConfigs() {
        ConfigLoader loader = ConfigLoader.getInstance();
        Map<String, Object> dbSection = loader.getConfigSection(DATABASES_SECTION);

        for (Map.Entry<String, Object> entry : dbSection.entrySet()) {
            String dbName = entry.getKey();
            Map<String, Object> dbConfig = (Map<String, Object>) entry.getValue();

            DatabaseConfig config = DatabaseConfig.builder()
                    .name(dbName)
                    .url((String) dbConfig.get("url"))
                    .username((String) dbConfig.get("username"))
                    .password((String) dbConfig.get("password"))
                    .driverClassName((String) dbConfig.getOrDefault("driver", "com.mysql.cj.jdbc.Driver"))
                    .maxPoolSize(((Number) dbConfig.getOrDefault("maxPoolSize", 10)).intValue())
                    .minIdle(((Number) dbConfig.getOrDefault("minIdle", 5)).intValue())
                    .connectionTimeout(((Number) dbConfig.getOrDefault("connectionTimeout", 30000)).intValue())
                    .idleTimeout(((Number) dbConfig.getOrDefault("idleTimeout", 600000)).intValue())
                    .maxLifetime(((Number) dbConfig.getOrDefault("maxLifetime", 1800000)).intValue())
                    .build();

            databaseConfigs.put(dbName, config);
        }

        log.info("Loaded {} database configurations", databaseConfigs.size());
    }

    /**
     * Load MongoDB configurations
     */
    @SuppressWarnings("unchecked")
    private void loadMongoDBConfigs() {
        ConfigLoader loader = ConfigLoader.getInstance();
        Map<String, Object> mongoSection = loader.getConfigSection(MONGODB_SECTION);

        for (Map.Entry<String, Object> entry : mongoSection.entrySet()) {
            String dbName = entry.getKey();
            Map<String, Object> dbConfig = (Map<String, Object>) entry.getValue();

            MongoDBConfig config = MongoDBConfig.builder()
                    .name(dbName)
                    .uri((String) dbConfig.get("uri"))
                    .database((String) dbConfig.get("database"))
                    .username((String) dbConfig.get("username"))
                    .password((String) dbConfig.get("password"))
                    .authSource((String) dbConfig.getOrDefault("authSource", "admin"))
                    .authMechanism((String) dbConfig.getOrDefault("authMechanism", "SCRAM-SHA-256"))
                    .connectTimeout(((Number) dbConfig.getOrDefault("connectTimeout", 30000)).intValue())
                    .socketTimeout(((Number) dbConfig.getOrDefault("socketTimeout", 30000)).intValue())
                    .maxPoolSize(((Number) dbConfig.getOrDefault("maxPoolSize", 100)).intValue())
                    .minPoolSize(((Number) dbConfig.getOrDefault("minPoolSize", 10)).intValue())
                    .maxIdleTimeMS(((Number) dbConfig.getOrDefault("maxIdleTimeMS", 600000)).intValue())
                    .maxLifeTimeMS(((Number) dbConfig.getOrDefault("maxLifeTimeMS", 1800000)).intValue())
                    .build();

            mongoDBConfigs.put(dbName, config);
        }

        log.info("Loaded {} MongoDB configurations", mongoDBConfigs.size());
    }

    /**
     * Load Redis configurations
     */
    @SuppressWarnings("unchecked")
    private void loadRedisConfigs() {
        ConfigLoader loader = ConfigLoader.getInstance();
        Map<String, Object> redisSection = loader.getConfigSection(REDIS_SECTION);

        for (Map.Entry<String, Object> entry : redisSection.entrySet()) {
            String redisName = entry.getKey();
            Map<String, Object> redisConfig = (Map<String, Object>) entry.getValue();

            RedisConfig config = RedisConfig.builder()
                    .name(redisName)
                    .host((String) redisConfig.get("host"))
                    .port(((Number) redisConfig.getOrDefault("port", 6379)).intValue())
                    .password((String) redisConfig.get("password"))
                    .database(((Number) redisConfig.getOrDefault("database", 0)).intValue())
                    .ssl(Boolean.parseBoolean(redisConfig.getOrDefault("ssl", "false").toString()))
                    .timeout(((Number) redisConfig.getOrDefault("timeout", 2000)).intValue())
                    .maxTotal(((Number) redisConfig.getOrDefault("maxTotal", 128)).intValue())
                    .maxIdle(((Number) redisConfig.getOrDefault("maxIdle", 128)).intValue())
                    .minIdle(((Number) redisConfig.getOrDefault("minIdle", 16)).intValue())
                    .build();

            redisConfigs.put(redisName, config);
        }

        log.info("Loaded {} Redis configurations", redisConfigs.size());
    }

    /**
     * Load Elasticsearch configurations
     */
    @SuppressWarnings("unchecked")
    private void loadElasticsearchConfigs() {
        ConfigLoader loader = ConfigLoader.getInstance();
        Map<String, Object> esSection = loader.getConfigSection(ELASTICSEARCH_SECTION);

        for (Map.Entry<String, Object> entry : esSection.entrySet()) {
            String esName = entry.getKey();
            Map<String, Object> esConfig = (Map<String, Object>) entry.getValue();

            // Get hosts list
            List<String> hosts = (List<String>) esConfig.get("hosts");

            ElasticsearchConfig config = ElasticsearchConfig.builder()
                    .name(esName)
                    .hosts(hosts)
                    .username((String) esConfig.get("username"))
                    .password((String) esConfig.get("password"))
                    .connectTimeout(((Number) esConfig.getOrDefault("connectTimeout", 5000)).intValue())
                    .readTimeout(((Number) esConfig.getOrDefault("readTimeout", 5000)).intValue())
                    .maxTotalConnections(((Number) esConfig.getOrDefault("maxTotalConnections", 20)).intValue())
                    .defaultMaxTotalConnectionsPerRoute(((Number) esConfig.getOrDefault("defaultMaxTotalConnectionsPerRoute", 10)).intValue())
                    .discoveryEnabled(Boolean.parseBoolean(esConfig.getOrDefault("discoveryEnabled", "false").toString()))
                    .multiThreaded(Boolean.parseBoolean(esConfig.getOrDefault("multiThreaded", "true").toString()))
                    .build();

            elasticsearchConfigs.put(esName, config);
        }

        log.info("Loaded {} Elasticsearch configurations", elasticsearchConfigs.size());
    }

    /**
     * Load SSH configuration
     */
    @SuppressWarnings("unchecked")
    private void loadSSHConfig() {
        ConfigLoader loader = ConfigLoader.getInstance();
        Map<String, Object> sshSection = loader.getConfigSection(SSH_CONFIG_SECTION);

        if (sshSection.isEmpty()) {
            log.warn("SSH configuration not found");
            return;
        }

        boolean enabled = Boolean.parseBoolean(sshSection.getOrDefault("enabled", "false").toString());

        if (!enabled) {
            log.info("SSH tunneling is disabled in configuration");
            return;
        }

        sshConfig = SSHConfig.builder()
                .enabled(enabled)
                .hostname((String) sshSection.get("hostname"))
                .port(((Number) sshSection.getOrDefault("port", 22)).intValue())
                .username((String) sshSection.get("username"))
                .password((String) sshSection.get("password"))
                .build();

        log.info("Loaded SSH configuration");
    }

    /**
     * Load base URLs for services
     */
    @SuppressWarnings("unchecked")
    private void loadBaseUrls() {
        ConfigLoader loader = ConfigLoader.getInstance();
        Map<String, Object> urlsSection = loader.getConfigSection(BASE_URLS_SECTION);

        for (Map.Entry<String, Object> entry : urlsSection.entrySet()) {
            String serviceName = entry.getKey();
            String url = entry.getValue().toString();

            baseUrls.put(serviceName, url);
        }

        log.info("Loaded {} base URLs", baseUrls.size());
    }

    /**
     * Get database configuration
     *
     * @param dbName Database name
     * @return Database configuration
     */
    public DatabaseConfig getDatabaseConfig(String dbName) {
        return databaseConfigs.get(dbName);
    }

    /**
     * Get all database configurations
     *
     * @return Map of database configurations
     */
    public Map<String, DatabaseConfig> getAllDatabaseConfigs() {
        return new HashMap<>(databaseConfigs);
    }

    /**
     * Get MongoDB configuration
     *
     * @param dbName Database name
     * @return MongoDB configuration
     */
    public MongoDBConfig getMongoDBConfig(String dbName) {
        return mongoDBConfigs.get(dbName);
    }

    /**
     * Get all MongoDB configurations
     *
     * @return Map of MongoDB configurations
     */
    public Map<String, MongoDBConfig> getAllMongoDBConfigs() {
        return new HashMap<>(mongoDBConfigs);
    }

    /**
     * Get Redis configuration
     *
     * @param redisName Redis instance name
     * @return Redis configuration
     */
    public RedisConfig getRedisConfig(String redisName) {
        return redisConfigs.get(redisName);
    }

    /**
     * Get all Redis configurations
     *
     * @return Map of Redis configurations
     */
    public Map<String, RedisConfig> getAllRedisConfigs() {
        return new HashMap<>(redisConfigs);
    }

    /**
     * Get Elasticsearch configuration
     *
     * @param esName Elasticsearch instance name
     * @return Elasticsearch configuration
     */
    public ElasticsearchConfig getElasticsearchConfig(String esName) {
        return elasticsearchConfigs.get(esName);
    }

    /**
     * Get all Elasticsearch configurations
     *
     * @return Map of Elasticsearch configurations
     */
    public Map<String, ElasticsearchConfig> getAllElasticsearchConfigs() {
        return new HashMap<>(elasticsearchConfigs);
    }

    /**
     * Get SSH configuration
     *
     * @return SSH configuration
     */
    public SSHConfig getSSHConfig() {
        return sshConfig;
    }

    /**
     * Check if SSH tunneling is enabled
     *
     * @return true if SSH tunneling is enabled, false otherwise
     */
    public boolean isSSHTunnelingEnabled() {
        return sshConfig != null && sshConfig.isEnabled();
    }

    /**
     * Get base URL for a service
     *
     * @param serviceName Service name
     * @return Base URL
     */
    public String getBaseUrl(String serviceName) {
        return baseUrls.get(serviceName);
    }

    /**
     * Get all base URLs
     *
     * @return Map of base URLs
     */
    public Map<String, String> getAllBaseUrls() {
        return new HashMap<>(baseUrls);
    }

    /**
     * Refresh all configurations
     */
    public synchronized void refresh() {
        // Clear caches
        databaseConfigs.clear();
        mongoDBConfigs.clear();
        redisConfigs.clear();
        elasticsearchConfigs.clear();
        sshConfig = null;
        baseUrls.clear();

        // Clear the config loader cache
        ConfigLoader.getInstance().clearCache();

        // Reload configurations
        loadConfigurations();

        log.info("All configurations refreshed");
    }
}

package com.lenskart.commons.endpoints;

import com.lenskart.commons.config.ConfigProvider;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Abstract base class for managing endpoints across different modules.
 * Provides common URL formation, caching, and path parameter replacement logic.
 *
 * @param <T> The endpoint enum type that implements BaseEndpoint
 */
@Slf4j
public abstract class EndpointManager<T extends Enum<T> & BaseEndpoint> {
    
    // Static maps to store base URLs and full URLs per endpoint type
    private final Map<String, String> serviceBaseUrls = new ConcurrentHashMap<>();
    private final Map<T, String> fullUrls = new ConcurrentHashMap<>();
    
    // Flag to track if initialization has been done
    private volatile boolean initialized = false;
    
    // Configuration provider for this manager
    private final ConfigProvider configProvider;
    
    // Class of the endpoint enum for reflection
    private final Class<T> endpointClass;
    
    // Pattern for matching path parameters in different formats
    private static final Pattern PATH_PARAM_PATTERN = Pattern.compile("\\{\\$([^}]+)\\}|\\{([^}]+)\\}|:([a-zA-Z_][a-zA-Z0-9_]*)");
    
    /**
     * Constructor for EndpointManager
     *
     * @param configProvider The configuration provider to use for base URLs
     * @param endpointClass The class of the endpoint enum
     */
    protected EndpointManager(ConfigProvider configProvider, Class<T> endpointClass) {
        this.configProvider = configProvider;
        this.endpointClass = endpointClass;
    }
    
    /**
     * Gets the complete URL for the specified endpoint
     *
     * @param endpoint The endpoint to get the URL for
     * @return Complete URL for the endpoint
     */
    public String getUrl(T endpoint) {
        // Ensure URLs are initialized
        if (!initialized) {
            initializeUrls();
        }
        
        // Return the pre-computed URL
        String url = fullUrls.get(endpoint);
        if (url == null) {
            throw new IllegalStateException("URL not found for endpoint: " + endpoint.name());
        }
        
        return url;
    }
    
    /**
     * Gets the complete URL for the specified endpoint with path parameters replaced
     *
     * @param endpoint The endpoint to get the URL for
     * @param pathParams Map of path parameters to replace in the URL
     * @return Complete URL with path parameters replaced
     */
    public String getUrl(T endpoint, Map<String, String> pathParams) {
        String url = getUrl(endpoint);
        
        if (pathParams != null && !pathParams.isEmpty()) {
            url = replacePathParameters(url, pathParams);
        }
        
        return url;
    }
    
    /**
     * Initializes all URLs by loading the configuration once
     */
    private synchronized void initializeUrls() {
        if (initialized) {
            return;
        }
        
        log.info("Initializing URLs for endpoint manager: {}", this.getClass().getSimpleName());
        
        try {
            // Get all endpoint values using reflection
            T[] endpoints = endpointClass.getEnumConstants();
            
            if (endpoints == null || endpoints.length == 0) {
                log.warn("No endpoints found for class: {}", endpointClass.getSimpleName());
                initialized = true;
                return;
            }
            
            // Pre-load all service base URLs
            for (T endpoint : endpoints) {
                String serviceName = endpoint.getServiceName();
                if (!serviceBaseUrls.containsKey(serviceName)) {
                    String baseUrl = configProvider.getBaseUrl(serviceName);
                    
                    if (baseUrl == null) {
                        log.error("Base URL not found for service: {} in endpoint: {}", serviceName, endpoint.name());
                        throw new IllegalStateException("Base URL not found for service: " + serviceName);
                    }
                    
                    // Remove trailing slash from base URL if present
                    if (baseUrl.endsWith("/")) {
                        baseUrl = baseUrl.substring(0, baseUrl.length() - 1);
                    }
                    
                    serviceBaseUrls.put(serviceName, baseUrl);
                    log.debug("Loaded base URL for service {}: {}", serviceName, baseUrl);
                }
            }
            
            // Pre-compute all full URLs
            for (T endpoint : endpoints) {
                String baseUrl = serviceBaseUrls.get(endpoint.getServiceName());
                String path = endpoint.getEndpoint();
                
                // Ensure endpoint starts with a slash
                if (!path.startsWith("/")) {
                    path = "/" + path;
                }
                
                // Store the full URL
                String fullUrl = baseUrl + path;
                fullUrls.put(endpoint, fullUrl);
                log.debug("Computed full URL for {}: {}", endpoint.name(), fullUrl);
            }
            
            initialized = true;
            log.info("Successfully initialized {} URLs for {}", fullUrls.size(), this.getClass().getSimpleName());
            
        } catch (Exception e) {
            log.error("Failed to initialize URLs for {}: {}", this.getClass().getSimpleName(), e.getMessage(), e);
            throw new RuntimeException("Failed to initialize endpoint URLs", e);
        }
    }
    
    /**
     * Replaces path parameters in the URL with provided values
     * Supports multiple parameter formats: {$param}, {param}, :param
     *
     * @param url The URL template with parameters
     * @param pathParams Map of parameter names to values
     * @return URL with parameters replaced
     */
    private String replacePathParameters(String url, Map<String, String> pathParams) {
        if (pathParams == null || pathParams.isEmpty()) {
            return url;
        }
        
        Matcher matcher = PATH_PARAM_PATTERN.matcher(url);
        StringBuffer result = new StringBuffer();
        
        while (matcher.find()) {
            // Extract parameter name from different formats
            String paramName = null;
            if (matcher.group(1) != null) {
                paramName = matcher.group(1); // {$param} format
            } else if (matcher.group(2) != null) {
                paramName = matcher.group(2); // {param} format
            } else if (matcher.group(3) != null) {
                paramName = matcher.group(3); // :param format
            }
            
            if (paramName != null && pathParams.containsKey(paramName)) {
                String replacement = pathParams.get(paramName);
                if (replacement != null) {
                    matcher.appendReplacement(result, Matcher.quoteReplacement(replacement));
                } else {
                    log.warn("Null value provided for path parameter: {}", paramName);
                }
            } else {
                log.warn("Path parameter not found in provided parameters: {}", paramName);
                // Keep the original parameter in the URL
            }
        }
        matcher.appendTail(result);
        
        return result.toString();
    }
    
    /**
     * Refreshes the URLs by clearing caches and reloading configuration
     */
    public synchronized void refresh() {
        log.info("Refreshing URLs for endpoint manager: {}", this.getClass().getSimpleName());
        
        // Refresh the configuration provider
        configProvider.refresh();
        
        // Clear our caches
        serviceBaseUrls.clear();
        fullUrls.clear();
        initialized = false;
        
        // Re-initialize
        initializeUrls();
        
        log.info("Successfully refreshed URLs for {}", this.getClass().getSimpleName());
    }
    
    /**
     * Gets all computed URLs for debugging/monitoring purposes
     *
     * @return Map of endpoint names to their full URLs
     */
    public Map<String, String> getAllUrls() {
        if (!initialized) {
            initializeUrls();
        }
        
        Map<String, String> result = new HashMap<>();
        for (Map.Entry<T, String> entry : fullUrls.entrySet()) {
            result.put(entry.getKey().name(), entry.getValue());
        }
        
        return result;
    }
    
    /**
     * Gets all service base URLs
     *
     * @return Map of service names to base URLs
     */
    public Map<String, String> getServiceBaseUrls() {
        if (!initialized) {
            initializeUrls();
        }
        
        return new HashMap<>(serviceBaseUrls);
    }
    
    /**
     * Validates that all endpoints have valid URLs
     *
     * @return true if all endpoints are valid, false otherwise
     */
    public boolean validateEndpoints() {
        try {
            if (!initialized) {
                initializeUrls();
            }
            
            T[] endpoints = endpointClass.getEnumConstants();
            if (endpoints == null) {
                return false;
            }
            
            for (T endpoint : endpoints) {
                String url = fullUrls.get(endpoint);
                if (url == null || url.trim().isEmpty()) {
                    log.error("Invalid URL for endpoint: {}", endpoint.name());
                    return false;
                }
            }
            
            return true;
        } catch (Exception e) {
            log.error("Validation failed for endpoint manager: {}", this.getClass().getSimpleName(), e);
            return false;
        }
    }
    
    /**
     * Gets the configuration provider used by this manager
     *
     * @return The configuration provider
     */
    protected ConfigProvider getConfigProvider() {
        return configProvider;
    }
    
    /**
     * Checks if the manager is initialized
     *
     * @return true if initialized, false otherwise
     */
    public boolean isInitialized() {
        return initialized;
    }
}

package com.lenskart.commons.endpoints;

import com.lenskart.commons.config.ModuleConfigAdapter;
import com.lenskart.commons.config.UnifiedConfigRegistry;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Central registry for all endpoint managers across modules.
 * Provides initialization, registration, and management of endpoint managers.
 */
@Slf4j
public class EndpointRegistry {
    
    // Map of module names to their endpoint managers
    private final Map<String, EndpointManager<?>> endpointManagers = new ConcurrentHashMap<>();
    
    // Singleton instance
    private static volatile EndpointRegistry instance;
    
    // Initialization flag
    private volatile boolean initialized = false;
    
    /**
     * Private constructor for singleton pattern
     */
    private EndpointRegistry() {
        // Private constructor
    }
    
    /**
     * Gets the singleton instance of EndpointRegistry
     *
     * @return The singleton instance
     */
    public static EndpointRegistry getInstance() {
        if (instance == null) {
            synchronized (EndpointRegistry.class) {
                if (instance == null) {
                    instance = new EndpointRegistry();
                }
            }
        }
        return instance;
    }
    
    /**
     * Initializes the endpoint registry by auto-discovering and registering
     * endpoint managers from all available modules
     */
    public synchronized void initialize() {
        if (initialized) {
            log.debug("EndpointRegistry already initialized");
            return;
        }
        
        log.info("Initializing EndpointRegistry...");
        
        try {
            // Initialize the unified config registry first
            UnifiedConfigRegistry unifiedRegistry = UnifiedConfigRegistry.getInstance();
            
            // Register module config adapters
            registerModuleConfigAdapters(unifiedRegistry);
            
            // Register endpoint managers
            registerEndpointManagers();
            
            initialized = true;
            log.info("EndpointRegistry initialized successfully with {} modules", endpointManagers.size());
            
        } catch (Exception e) {
            log.error("Failed to initialize EndpointRegistry: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to initialize EndpointRegistry", e);
        }
    }
    
    /**
     * Registers module configuration adapters with the unified registry
     */
    private void registerModuleConfigAdapters(UnifiedConfigRegistry unifiedRegistry) {
        String[] modules = {"juno", "nexs", "pos", "cs", "scm"};
        
        for (String module : modules) {
            try {
                var adapter = ModuleConfigAdapter.createForModule(module);
                if (adapter != null) {
                    unifiedRegistry.registerModuleProvider(module, adapter);
                    log.debug("Registered config adapter for module: {}", module);
                }
            } catch (Exception e) {
                log.warn("Could not register config adapter for module {}: {}", module, e.getMessage());
            }
        }
    }
    
    /**
     * Registers endpoint managers for all available modules
     */
    private void registerEndpointManagers() {
        // Register Juno endpoint manager
        registerJunoEndpointManager();
        
        // Register POS endpoint manager
        registerPosEndpointManager();
        
        // Register NEXS endpoint manager
        registerNexsEndpointManager();
        
        // Register Cs modules as they become available
         registerCsEndpointManager();

        // Register Scm modules as they become available
         registerScmEndpointManager();
    }
    
    /**
     * Registers Juno endpoint manager
     */
    private void registerJunoEndpointManager() {
        try {
            Class<?> managerClass = Class.forName("com.lenskart.juno.endpoints.JunoEndpointManager");
            Object manager = managerClass.getMethod("getInstance").invoke(null);
            
            if (manager instanceof EndpointManager) {
                endpointManagers.put("juno", (EndpointManager<?>) manager);
                log.debug("Registered Juno endpoint manager");
            }
        } catch (Exception e) {
            log.warn("Could not register Juno endpoint manager: {}", e.getMessage());
        }
    }
    
    /**
     * Registers POS endpoint manager
     */
    private void registerPosEndpointManager() {
        try {
            Class<?> managerClass = Class.forName("com.lenskart.pos.endpoints.PosEndpointManager");
            Object manager = managerClass.getMethod("getInstance").invoke(null);
            
            if (manager instanceof EndpointManager) {
                endpointManagers.put("pos", (EndpointManager<?>) manager);
                log.debug("Registered POS endpoint manager");
            }
        } catch (Exception e) {
            log.warn("Could not register POS endpoint manager: {}", e.getMessage());
        }
    }
    
    /**
     * Registers NEXS endpoint manager
     */
    private void registerNexsEndpointManager() {
        try {
            Class<?> managerClass = Class.forName("com.lenskart.nexs.endpoints.NexsEndpointManager");
            Object manager = managerClass.getMethod("getInstance").invoke(null);
            
            if (manager instanceof EndpointManager) {
                endpointManagers.put("nexs", (EndpointManager<?>) manager);
                log.debug("Registered NEXS endpoint manager");
            }
        } catch (Exception e) {
            log.warn("Could not register NEXS endpoint manager: {}", e.getMessage());
        }
    }


    /**
     * Registers Cs endpoint manager
     */
    private void registerCsEndpointManager() {
        try {
            Class<?> managerClass = Class.forName("com.lenskart.nexs.endpoints.CsEndpointManager");
            Object manager = managerClass.getMethod("getInstance").invoke(null);

            if (manager instanceof EndpointManager) {
                endpointManagers.put("cs", (EndpointManager<?>) manager);
                log.debug("Registered Cs endpoint manager");
            }
        } catch (Exception e) {
            log.warn("Could not register Cs endpoint manager: {}", e.getMessage());
        }
    }

    /**
     * Registers Scm endpoint manager
     */
    private void registerScmEndpointManager() {
        try {
            Class<?> managerClass = Class.forName("com.lenskart.nexs.endpoints.ScmEndpointManager");
            Object manager = managerClass.getMethod("getInstance").invoke(null);

            if (manager instanceof EndpointManager) {
                endpointManagers.put("scm", (EndpointManager<?>) manager);
                log.debug("Registered Scm endpoint manager");
            }
        } catch (Exception e) {
            log.warn("Could not register Scm endpoint manager: {}", e.getMessage());
        }
    }
    
    /**
     * Gets an endpoint manager for a specific module
     *
     * @param moduleName The module name (e.g., "juno", "pos", "nexs")
     * @return The endpoint manager for the module, or null if not found
     */
    public EndpointManager<?> getEndpointManager(String moduleName) {
        if (!initialized) {
            initialize();
        }
        
        return endpointManagers.get(moduleName.toLowerCase());
    }
    
    /**
     * Gets all registered endpoint managers
     *
     * @return Map of module names to endpoint managers
     */
    public Map<String, EndpointManager<?>> getAllEndpointManagers() {
        if (!initialized) {
            initialize();
        }
        
        return new HashMap<>(endpointManagers);
    }
    
    /**
     * Refreshes all registered endpoint managers
     */
    public void refreshAll() {
        if (!initialized) {
            initialize();
        }
        
        log.info("Refreshing all endpoint managers...");
        
        for (Map.Entry<String, EndpointManager<?>> entry : endpointManagers.entrySet()) {
            try {
                entry.getValue().refresh();
                log.debug("Refreshed endpoint manager for module: {}", entry.getKey());
            } catch (Exception e) {
                log.error("Failed to refresh endpoint manager for module {}: {}", 
                    entry.getKey(), e.getMessage(), e);
            }
        }
        
        log.info("Completed refreshing all endpoint managers");
    }
    
    /**
     * Validates all registered endpoint managers
     *
     * @return Map of module names to validation results
     */
    public Map<String, Boolean> validateAll() {
        if (!initialized) {
            initialize();
        }
        
        Map<String, Boolean> results = new HashMap<>();
        
        for (Map.Entry<String, EndpointManager<?>> entry : endpointManagers.entrySet()) {
            try {
                boolean isValid = entry.getValue().validateEndpoints();
                results.put(entry.getKey(), isValid);
                
                if (isValid) {
                    log.debug("Validation passed for module: {}", entry.getKey());
                } else {
                    log.warn("Validation failed for module: {}", entry.getKey());
                }
            } catch (Exception e) {
                log.error("Validation error for module {}: {}", entry.getKey(), e.getMessage(), e);
                results.put(entry.getKey(), false);
            }
        }
        
        return results;
    }
    
    /**
     * Gets statistics about all registered endpoint managers
     *
     * @return Map containing various statistics
     */
    public Map<String, Object> getStatistics() {
        if (!initialized) {
            initialize();
        }
        
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalModules", endpointManagers.size());
        stats.put("registeredModules", endpointManagers.keySet());
        stats.put("initialized", initialized);
        
        // Get endpoint counts per module
        Map<String, Integer> endpointCounts = new HashMap<>();
        for (Map.Entry<String, EndpointManager<?>> entry : endpointManagers.entrySet()) {
            try {
                int count = entry.getValue().getAllUrls().size();
                endpointCounts.put(entry.getKey(), count);
            } catch (Exception e) {
                log.warn("Could not get endpoint count for module {}: {}", entry.getKey(), e.getMessage());
                endpointCounts.put(entry.getKey(), 0);
            }
        }
        stats.put("endpointCounts", endpointCounts);
        
        return stats;
    }
    
    /**
     * Checks if the registry is initialized
     *
     * @return true if initialized, false otherwise
     */
    public boolean isInitialized() {
        return initialized;
    }
    
    /**
     * Gets the number of registered modules
     *
     * @return Number of registered modules
     */
    public int getModuleCount() {
        return endpointManagers.size();
    }
}

package com.lenskart.commons.listeners;

import io.qameta.allure.Attachment;
import org.testng.ITestResult;

public class AllureReportListener extends BaseTestListener {

    @Override
    public void onTestStart(ITestResult result) {
        super.onTestStart(result);
    }

    @Override
    public void onTestSuccess(ITestResult result) {
        super.onTestSuccess(result);
    }

    @Override
    public void onTestFailure(ITestResult result) {
        super.onTestFailure(result);


        // Attach test logs
        saveTextLog(getTestFailureDetails(result));
    }

    @Override
    public void onTestSkipped(ITestResult result) {
        super.onTestSkipped(result);

        // Attach skip reason to Allure report
        if (result.getThrowable() != null) {
            saveTextLog("Test Skipped: " + result.getThrowable().getMessage());
        }
    }


    /**
     * Attaches text logs to the Allure report
     *
     * @param message Log message
     * @return Message text
     */
    @Attachment(value = "Test Logs", type = "text/plain")
    private String saveTextLog(String message) {
        return message;
    }

    /**
     * Gets detailed failure information from a test result
     *
     * @param result Test result
     * @return Formatted failure details
     */
    private String getTestFailureDetails(ITestResult result) {
        StringBuilder sb = new StringBuilder();
        sb.append("Test Failed: ").append(getTestName(result)).append("\n");

        if (result.getThrowable() != null) {
            sb.append("Failure Reason: ").append(result.getThrowable().getMessage()).append("\n");

            StackTraceElement[] stackTrace = result.getThrowable().getStackTrace();
            for (int i = 0; i < Math.min(5, stackTrace.length); i++) {
                sb.append("\tat ").append(stackTrace[i]).append("\n");
            }
        }

        return sb.toString();
    }
}

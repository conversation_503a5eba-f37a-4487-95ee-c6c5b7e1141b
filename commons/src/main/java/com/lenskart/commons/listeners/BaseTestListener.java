package com.lenskart.commons.listeners;

import com.lenskart.commons.utils.LoggerUtils;
import org.slf4j.Logger;
import org.testng.ITestContext;
import org.testng.ITestListener;
import org.testng.ITestResult;

/**
 * Base TestNG listener for logging test execution
 */
public class BaseTestListener implements ITestListener {

    private static final Logger logger = LoggerUtils.getLogger(BaseTestListener.class);

    @Override
    public void onStart(ITestContext context) {
        logger.info("========== Test Suite Started: {} ==========", context.getName());
    }

    @Override
    public void onFinish(ITestContext context) {
        logger.info("========== Test Suite Finished: {} ==========", context.getName());
        logger.info("Passed Tests: {}", context.getPassedTests().size());
        logger.info("Failed Tests: {}", context.getFailedTests().size());
        logger.info("Skipped Tests: {}", context.getSkippedTests().size());
    }

    @Override
    public void onTestStart(ITestResult result) {
        LoggerUtils.logTestStart(logger, getTestName(result));
    }

    @Override
    public void onTestSuccess(ITestResult result) {
        LoggerUtils.logTestEnd(logger, getTestName(result), "PASS");
    }

    @Override
    public void onTestFailure(ITestResult result) {
        logger.error("Test Failed: {}", getTestName(result));
        if (result.getThrowable() != null) {
            logger.error("Failure Reason: ", result.getThrowable());
        }
        LoggerUtils.logTestEnd(logger, getTestName(result), "FAIL");
    }

    @Override
    public void onTestSkipped(ITestResult result) {
        logger.warn("Test Skipped: {}", getTestName(result));
        if (result.getThrowable() != null) {
            logger.warn("Skip Reason: ", result.getThrowable());
        }
        LoggerUtils.logTestEnd(logger, getTestName(result), "SKIP");
    }

    @Override
    public void onTestFailedButWithinSuccessPercentage(ITestResult result) {
        logger.warn("Test Failed Within Success Percentage: {}", getTestName(result));
        LoggerUtils.logTestEnd(logger, getTestName(result), "PARTIAL");
    }

    /**
     * Gets a formatted test name from the test result
     *
     * @param result Test result
     * @return Formatted test name
     */
    protected String getTestName(ITestResult result) {
        return result.getTestClass().getName() + "." + result.getMethod().getMethodName();
    }
}
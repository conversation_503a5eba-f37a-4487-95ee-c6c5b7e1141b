package com.lenskart.commons.listeners;

import com.aventstack.extentreports.ExtentReports;
import com.aventstack.extentreports.ExtentTest;
import com.aventstack.extentreports.Status;
import com.aventstack.extentreports.markuputils.ExtentColor;
import com.aventstack.extentreports.markuputils.MarkupHelper;
import com.lenskart.commons.reporting.ExtentManager;
import org.testng.ITestContext;
import org.testng.ITestResult;

import java.util.HashMap;
import java.util.Map;

/**
 * TestNG listener for ExtentReports integration
 */
public class ExtentReportListener extends BaseTestListener {
    
    private ExtentReports extent;
    private final Map<String, ExtentTest> testMap = new HashMap<>();
    
    @Override
    public void onStart(ITestContext context) {
        super.onStart(context);
        extent = ExtentManager.getInstance();
    }
    
    @Override
    public void onFinish(ITestContext context) {
        super.onFinish(context);
        extent.flush();
    }
    
    @Override
    public void onTestStart(ITestResult result) {
        super.onTestStart(result);
        
        String testName = getTestName(result);
        ExtentTest test = extent.createTest(testName, getTestDescription(result));
        
        // Add test class as category
        test.assignCategory(result.getTestClass().getName());
        
        // Add test groups as categories
        String[] groups = result.getMethod().getGroups();
        if (groups != null && groups.length > 0) {
            for (String group : groups) {
                test.assignCategory(group);
            }
        }
        
        testMap.put(testName, test);
    }
    
    @Override
    public void onTestSuccess(ITestResult result) {
        super.onTestSuccess(result);
        
        String testName = getTestName(result);
        ExtentTest test = testMap.get(testName);
        
        if (test != null) {
            test.log(Status.PASS, MarkupHelper.createLabel("Test Passed", ExtentColor.GREEN));
        }
    }
    
    @Override
    public void onTestFailure(ITestResult result) {
        super.onTestFailure(result);
        
        String testName = getTestName(result);
        ExtentTest test = testMap.get(testName);
        
        if (test != null) {
            // Log failure details
            test.log(Status.FAIL, MarkupHelper.createLabel("Test Failed", ExtentColor.RED));
            
            if (result.getThrowable() != null) {
                test.log(Status.FAIL, result.getThrowable());
            }
        }
    }
    
    @Override
    public void onTestSkipped(ITestResult result) {
        super.onTestSkipped(result);
        
        String testName = getTestName(result);
        ExtentTest test = testMap.get(testName);
        
        if (test != null) {
            test.log(Status.SKIP, MarkupHelper.createLabel("Test Skipped", ExtentColor.YELLOW));
            
            if (result.getThrowable() != null) {
                test.log(Status.SKIP, result.getThrowable());
            }
        }
    }
    
    /**
     * Gets the test description from the test result
     * 
     * @param result    Test result
     * @return          Test description or method name if no description is available
     */
    private String getTestDescription(ITestResult result) {
        String description = result.getMethod().getDescription();
        return description != null && !description.isEmpty() ? description : result.getMethod().getMethodName();
    }
}
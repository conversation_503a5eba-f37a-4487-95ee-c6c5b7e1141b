package com.lenskart.commons.config;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * Configuration class for MySQL cluster with common DNS hosting multiple databases
 */
@Data
@Builder
public class MySQLClusterConfig {
    
    // Common cluster settings
    private String clusterHost;
    private int clusterPort;
    private String defaultUsername;
    private String defaultPassword;
    private String driverClassName;
    
    // Common connection pool settings
    private int maxPoolSize;
    private int minIdle;
    private long connectionTimeout;
    private long idleTimeout;
    private long maxLifetime;
    
    // Database-specific configurations
    private Map<String, DatabaseSpecificConfig> databases;
    
    /**
     * Database-specific configuration that can override cluster defaults
     */
    @Data
    @Builder
    public static class DatabaseSpecificConfig {
        private String databaseName;
        private String username;        // Optional: override cluster username
        private String password;        // Optional: override cluster password
        private Integer maxPoolSize;    // Optional: override cluster pool size
        private Integer minIdle;        // Optional: override cluster min idle
        private Long connectionTimeout; // Optional: override cluster connection timeout
        private Long idleTimeout;       // Optional: override cluster idle timeout
        private Long maxLifetime;       // Optional: override cluster max lifetime
        private Map<String, String> additionalParams; // Additional JDBC parameters
    }
    
    /**
     * Creates a complete JDBC URL for a specific database
     *
     * @param databaseName Name of the database
     * @return Complete JDBC URL
     */
    public String createJdbcUrl(String databaseName) {
        DatabaseSpecificConfig dbConfig = databases.get(databaseName);
        if (dbConfig == null) {
            throw new IllegalArgumentException("Database configuration not found for: " + databaseName);
        }
        
        StringBuilder url = new StringBuilder();
        url.append("jdbc:mysql://")
           .append(clusterHost)
           .append(":")
           .append(clusterPort)
           .append("/")
           .append(dbConfig.getDatabaseName());
        
        // Add default parameters
        url.append("?useSSL=false&serverTimezone=UTC");
        
        // Add additional parameters if specified
        if (dbConfig.getAdditionalParams() != null) {
            for (Map.Entry<String, String> param : dbConfig.getAdditionalParams().entrySet()) {
                url.append("&").append(param.getKey()).append("=").append(param.getValue());
            }
        }
        
        return url.toString();
    }
    
    /**
     * Gets the username for a specific database (with fallback to cluster default)
     *
     * @param databaseName Name of the database
     * @return Username to use for the database
     */
    public String getUsername(String databaseName) {
        DatabaseSpecificConfig dbConfig = databases.get(databaseName);
        if (dbConfig != null && dbConfig.getUsername() != null) {
            return dbConfig.getUsername();
        }
        return defaultUsername;
    }
    
    /**
     * Gets the password for a specific database (with fallback to cluster default)
     *
     * @param databaseName Name of the database
     * @return Password to use for the database
     */
    public String getPassword(String databaseName) {
        DatabaseSpecificConfig dbConfig = databases.get(databaseName);
        if (dbConfig != null && dbConfig.getPassword() != null) {
            return dbConfig.getPassword();
        }
        return defaultPassword;
    }
    
    /**
     * Gets the max pool size for a specific database (with fallback to cluster default)
     *
     * @param databaseName Name of the database
     * @return Max pool size to use for the database
     */
    public int getMaxPoolSize(String databaseName) {
        DatabaseSpecificConfig dbConfig = databases.get(databaseName);
        if (dbConfig != null && dbConfig.getMaxPoolSize() != null) {
            return dbConfig.getMaxPoolSize();
        }
        return maxPoolSize;
    }
    
    /**
     * Gets the min idle for a specific database (with fallback to cluster default)
     *
     * @param databaseName Name of the database
     * @return Min idle to use for the database
     */
    public int getMinIdle(String databaseName) {
        DatabaseSpecificConfig dbConfig = databases.get(databaseName);
        if (dbConfig != null && dbConfig.getMinIdle() != null) {
            return dbConfig.getMinIdle();
        }
        return minIdle;
    }
    
    /**
     * Gets the connection timeout for a specific database (with fallback to cluster default)
     *
     * @param databaseName Name of the database
     * @return Connection timeout to use for the database
     */
    public long getConnectionTimeout(String databaseName) {
        DatabaseSpecificConfig dbConfig = databases.get(databaseName);
        if (dbConfig != null && dbConfig.getConnectionTimeout() != null) {
            return dbConfig.getConnectionTimeout();
        }
        return connectionTimeout;
    }
    
    /**
     * Gets the idle timeout for a specific database (with fallback to cluster default)
     *
     * @param databaseName Name of the database
     * @return Idle timeout to use for the database
     */
    public long getIdleTimeout(String databaseName) {
        DatabaseSpecificConfig dbConfig = databases.get(databaseName);
        if (dbConfig != null && dbConfig.getIdleTimeout() != null) {
            return dbConfig.getIdleTimeout();
        }
        return idleTimeout;
    }
    
    /**
     * Gets the max lifetime for a specific database (with fallback to cluster default)
     *
     * @param databaseName Name of the database
     * @return Max lifetime to use for the database
     */
    public long getMaxLifetime(String databaseName) {
        DatabaseSpecificConfig dbConfig = databases.get(databaseName);
        if (dbConfig != null && dbConfig.getMaxLifetime() != null) {
            return dbConfig.getMaxLifetime();
        }
        return maxLifetime;
    }
    
    /**
     * Creates a DatabaseConfig object for a specific database
     *
     * @param databaseName Name of the database
     * @return DatabaseConfig object for the specified database
     */
    public DatabaseConfig createDatabaseConfig(String databaseName) {
        if (!databases.containsKey(databaseName)) {
            throw new IllegalArgumentException("Database configuration not found for: " + databaseName);
        }
        
        return DatabaseConfig.builder()
                .name(databaseName)
                .url(createJdbcUrl(databaseName))
                .username(getUsername(databaseName))
                .password(getPassword(databaseName))
                .driverClassName(driverClassName)
                .maxPoolSize(getMaxPoolSize(databaseName))
                .minIdle(getMinIdle(databaseName))
                .connectionTimeout(getConnectionTimeout(databaseName))
                .idleTimeout(getIdleTimeout(databaseName))
                .maxLifetime(getMaxLifetime(databaseName))
                .build();
    }
    
    /**
     * Checks if a database is configured in this cluster
     *
     * @param databaseName Name of the database to check
     * @return true if the database is configured, false otherwise
     */
    public boolean isDatabaseConfigured(String databaseName) {
        return databases.containsKey(databaseName);
    }
    
    /**
     * Gets all configured database names
     *
     * @return Set of all configured database names
     */
    public java.util.Set<String> getConfiguredDatabases() {
        return databases.keySet();
    }
}

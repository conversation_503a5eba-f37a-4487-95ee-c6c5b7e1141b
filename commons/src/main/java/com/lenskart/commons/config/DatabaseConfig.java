package com.lenskart.commons.config;

import lombok.Builder;
import lombok.Data;

/**
 * Configuration class for database connection details
 */
@Data
@Builder
public class DatabaseConfig {
    private String name;
    private String url;
    private String username;
    private String password;
    private String driverClassName;
    private int maxPoolSize;
    private int minIdle;
    private long connectionTimeout;
    private long idleTimeout;
    private long maxLifetime;

    /**
     * Creates a JDBC URL for MySQL based on host, port, and database name
     *
     * @param host     Database host
     * @param port     Database port
     * @param database Database name
     * @return Complete JDBC URL
     */

    public static String createMySqlJdbcUrl(String host, int port, String database) {
        return String.format("*****************************************************", host, port, database);
    }
}
package com.lenskart.commons.model;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Builder
@Data
public class OrderContext {

    /* this model holds context for the order

    1. users authentication details, pos store details
    2. juno order metadata
    3. scm states and intermediate metadata
    4. cs metadata
     */

    // User authentication details
    String phoneNumber;
    @Builder.Default
    String otp = "7352";
    @Builder.Default
    boolean isGoldUser = false;
    @Builder.Default
    boolean isPosOrder = false;
    PosStoreMapper posStoreMapper;


    // Product list
    List<ProductList> productLists;
    CountryCodeMapper countryCodeMapper;

    // Order metadata
    int orderId;
    int cartId;
    PaymentMethod paymentMethod;

    // needed for Zero power & Sunglasses es cases
    boolean confirmOrder;
    BigDecimal finalOrderAmount;

    int unicomOrderCode;

    @Builder.Default
    int statusCode = 200;

    // headers for API's
    Headers headers;

    @Builder
    @Data
    public static class Headers {

        private String sessionToken;
        private String posSessionToken;
        private Client client;
        private String csCookie;
    }


    @Builder
    @Data
    public static class ProductList {

        private String productId;
        private PowerTypes powerType;
        private String packageId;
        private FrameTypes frameType;
        private ProductTypes productType;
        @Builder.Default
        private boolean isPrescriptionRequired = false;
        NexsOrderState finalState;
        ItemType itemType;
        @Builder.Default
        int quantity = 1;
        private int itemNumber;
        String shippingPackageId;
        String facilityCode;
        String barCode;
    }

    @Builder
    @Data
    public static class CountryCodeMapper {

        private Countries country;
        private String pinCode;
    }

    @Builder
    @Data
    public static class PosStoreMapper {

        private Countries country;
        private String storeId;
    }


}

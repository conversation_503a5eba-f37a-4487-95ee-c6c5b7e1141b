package com.lenskart.commons.model;

import lombok.Getter;

/**
 * Enum representing all MySQL cluster portals available in the system.
 * These clusters correspond to the mysqlClusters configuration in config.yml.
 * 
 * Each cluster represents a different service domain with its own database infrastructure.
 */
@Getter
public enum MySQLCluster {
    
    /**
     * POS (Point of Sale) Cluster
     * Handles point-of-sale operations and store management
     * Host: mysql-pos-webservice.preprod.internal
     */
    POS_CLUSTER("pos_cluster", "POS Cluster", "Point of Sale operations and store management"),
    
    /**
     * NEXS (Next Generation Execution System) Cluster  
     * Handles order processing, warehouse management, and fulfillment
     * Host: nexs-mysql.preprod.internal
     */
    NEXS_CLUSTER("nexs_cluster", "NEXS Cluster", "Order processing, warehouse management, and fulfillment"),
    
    /**
     * QMS (Quality Management System) Cluster
     * Handles quality control, store operations, and inventory management
     * Host: stores-db.preprod.internal
     */
    QMS_CLUSTER("qms_cluster", "QMS Cluster", "Quality control, store operations, and inventory management"),
    
    /**
     * NPS (Net Promoter Score / Notification Processing System) Cluster
     * Handles user services, notifications, and customer feedback
     * Host: nps.preprod.internal
     */
    NPS_CLUSTER("nps_cluster", "NPS Cluster", "User services, notifications, and customer feedback"),
    
    /**
     * JUNO (Legacy Web System) Cluster
     * Handles legacy web operations and legacy data management
     * Host: mysql-web-master-rw.preprod.internal
     */
    JUNO_CLUSTER("juno_cluster", "JUNO Cluster", "Legacy web operations and legacy data management");

    /**
     * -- GETTER --
     *  Get the cluster name as defined in config.yml
     *
     * @return The cluster name used in configuration
     */
    private final String clusterName;
    /**
     * -- GETTER --
     *  Get the human-readable display name
     *
     * @return Display name for UI/logging purposes
     */
    private final String displayName;
    /**
     * -- GETTER --
     *  Get the description of the cluster's purpose
     *
     * @return Description of what this cluster handles
     */
    private final String description;
    
    /**
     * Constructor for MySQLCluster enum
     *
     * @param clusterName The cluster name as defined in config.yml
     * @param displayName Human-readable display name
     * @param description Description of the cluster's purpose
     */
    MySQLCluster(String clusterName, String displayName, String description) {
        this.clusterName = clusterName;
        this.displayName = displayName;
        this.description = description;
    }

    /**
     * String representation of the cluster
     *
     * @return String representation including cluster name and display name
     */
    @Override
    public String toString() {
        return String.format("%s (%s)", displayName, clusterName);
    }
}

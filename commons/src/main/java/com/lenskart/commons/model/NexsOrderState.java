package com.lenskart.commons.model;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Enum representing different order states in the NEXS system.
 * Orders move through these states in a sequential manner.
 */
@Getter
public enum NexsOrderState {

    /**
     * Initial state when Order is created
     */
    CREATED("Created", "Order has been created and is awaiting processing", 1),

    /**
     * Shipment is in picking stage
     */
    IN_PICKING("In Picking", "Order items are being picked from warehouse", 2),
    
    /**
     * Shipment items have been picked
     */
    PICKED("Picked", "All order items have been picked successfully", 3),

    /**
     * Shipment items are in tray
     */
    IN_TRAY("In Tray", "Order items are in tray", 3),

    /**
     * Shipment is in MEI
     */
    EDGING("In Edging", "Order is in edging stage", 4),

    /**
     * Order is in fitting stage
     */
    PENDING_CUSTOMIZATION("In Fitting", "Order is in fitting stage", 5),

    /**
     * Order customization is complete
     */
    CUSTOMIZATION_COMPLETE("Customization Complete", "Order customization is complete", 6),
    
    /**
     * Order is in quality control
     */
    IN_QC("In QC", "Order is undergoing quality control checks", 4),
    
    /**
     * Quality control is completed
     */
    QC_DONE("QC Done", "Quality control has been completed successfully", 5),
    
    /**
     * Shipment has been invoiced
     */
    INVOICED("Invoiced", "Invoice has been generated for the order", 6),

    /**
     * Courier has been assigned for the Shipment
     */
    AWB_CREATED("Awb Created", "AWB has been created for the order", 7),

    /**
     * Shipment is added to the Manifest
     */
    READY_TO_SHIP("Ready To Ship", "Order is ready to ship", 8),
    
    /**
     * Shipment has been dispatched
     */
    DISPATCHED("Dispatched", "Order has been dispatched for delivery", 9);
    
    private final String displayName;
    private final String description;
    private final int sequence;
    
    /**
     * Constructor for OrderState enum
     */
    NexsOrderState(String displayName, String description, int sequence) {
        this.displayName = displayName;
        this.description = description;
        this.sequence = sequence;
    }
    
    /**
     * Get order state by display name (case-insensitive)
     * 
     * @param displayName The display name of the order state
     * @return The corresponding OrderState enum value
     * @throws IllegalArgumentException if the display name is not found
     */
    public static NexsOrderState getByDisplayName(String displayName) {
        if (displayName == null || displayName.trim().isEmpty()) {
            throw new IllegalArgumentException("Display name cannot be null or empty");
        }
        
        return Arrays.stream(values())
                .filter(state -> state.displayName.equalsIgnoreCase(displayName.trim()))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Order state not found for display name: " + displayName));
    }
    
    /**
     * Get order state by enum name (case-insensitive)
     * 
     * @param name The enum name
     * @return The corresponding OrderState enum value
     * @throws IllegalArgumentException if the name is not found
     */
    public static NexsOrderState getByName(String name) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("Name cannot be null or empty");
        }
        
        return Arrays.stream(values())
                .filter(state -> state.name().equalsIgnoreCase(name.trim()))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Order state not found for name: " + name));
    }
    
    /**
     * Get order state by sequence number
     * 
     * @param sequence The sequence number
     * @return Optional containing the order state if found
     */
    public static Optional<NexsOrderState> getBySequence(int sequence) {
        return Arrays.stream(values())
                .filter(state -> state.sequence == sequence)
                .findFirst();
    }
    
    /**
     * Get the next possible state in the sequence
     * 
     * @return Optional containing the next state, empty if this is the final state
     */
    public Optional<NexsOrderState> getNextState() {
        return getBySequence(this.sequence + 1);
    }
    
    /**
     * Get the previous state in the sequence
     * 
     * @return Optional containing the previous state, empty if this is the initial state
     */
    public Optional<NexsOrderState> getPreviousState() {
        return getBySequence(this.sequence - 1);
    }
    
    /**
     * Check if this state can transition to the target state
     * 
     * @param targetState The target state to transition to
     * @return true if transition is allowed, false otherwise
     */
    public boolean canTransitionTo(NexsOrderState targetState) {
        if (targetState == null) {
            return false;
        }
        
        // Can only move to the next state in sequence
        return targetState.sequence == this.sequence + 1;
    }
    
    /**
     * Check if this is the initial state
     * 
     * @return true if this is the initial state
     */
    public boolean isInitialState() {
        return this == CREATED;
    }
    
    /**
     * Check if this is the final state
     * 
     * @return true if this is the final state
     */
    public boolean isFinalState() {
        return this == DISPATCHED;
    }
    
    /**
     * Get all states that come after this state
     * 
     * @return List of states that come after this state
     */
    public List<NexsOrderState> getSubsequentStates() {
        return Arrays.stream(values())
                .filter(state -> state.sequence > this.sequence)
                .collect(Collectors.toList());
    }
    
    /**
     * Get all states that come before this state
     * 
     * @return List of states that come before this state
     */
    public List<NexsOrderState> getPrecedingStates() {
        return Arrays.stream(values())
                .filter(state -> state.sequence < this.sequence)
                .collect(Collectors.toList());
    }
    
    /**
     * Check if a state name is valid
     * 
     * @param name The state name to validate
     * @return true if valid, false otherwise
     */
    public static boolean isValidState(String name) {
        if (name == null || name.trim().isEmpty()) {
            return false;
        }
        
        return Arrays.stream(values())
                .anyMatch(state -> state.name().equalsIgnoreCase(name.trim()) || 
                                 state.displayName.equalsIgnoreCase(name.trim()));
    }
    
    /**
     * Get all order state names as a list
     * 
     * @return List of all order state names
     */
    public static List<String> getAllStateNames() {
        return Arrays.stream(values())
                .map(Enum::name)
                .collect(Collectors.toList());
    }
    
    /**
     * Get all display names as a list
     * 
     * @return List of all display names
     */
    public static List<String> getAllDisplayNames() {
        return Arrays.stream(values())
                .map(NexsOrderState::getDisplayName)
                .collect(Collectors.toList());
    }
    
    /**
     * Get the state code for API calls
     * 
     * @return State code in lowercase
     */
    public String getStateCode() {
        return this.name().toLowerCase();
    }
    
    @Override
    public String toString() {
        return String.format("%s (%d) - %s", displayName, sequence, description);
    }
}

package com.lenskart.commons.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum ProductId {

    IN_EYEGLASSES("148248", "Eyeglasses"),
    IN_SUNGLASSES("131315", "Sunglasses"),
    IN_CONTACT_LENS("134723", "Contact Lens"),
    IN_CONTACT_LENS_ZERO_POWER("151325", "Contact Lens"),
    IN_LOYALTY("128269", "Loyalty Services"),
    IN_ACCESSORIES("145651", "Accessories"),
    IN_CARRIAGE_BAG("208251", "Carriage Bag"),
    IN_FRAME_ONLY("140551", "Eyeglasses"),
    IN_INSURANCE("152748", "Insurance"),

    SG_EYEGLASSES("131932", "Eyeglasses"),
    SG_SUNGLASSES("131933", "Sunglasses"),
    SG_CONTACT_LENS("131934", "Contact Lens"),
    SG_LOYALTY("131935", "Loyalty Services"),
    SG_ACCESSORIES("131936", "Accessories"),

    AE_EYEGLASSES("131932", "Eyeglasses"),
    AE_SUNGLASSES("131933", "Sunglasses"),
    AE_CONTACT_LENS("131934", "Contact Lens"),
    AE_LOYALTY("131935", "Loyalty Services"),
    AE_ACCESSORIES("131936", "Accessories"),

    SA_EYEGLASSES("131932", "Eyeglasses"),
    SA_SUNGLASSES("131933", "Sunglasses"),
    SA_CONTACT_LENS("131934", "Contact Lens"),
    SA_LOYALTY("131935", "Loyalty Services"),
    SA_ACCESSORIES("131936", "Accessories"),

    TH_EYEGLASSES("131932", "Eyeglasses"),
    TH_SUNGLASSES("131933", "Sunglasses"),
    TH_CONTACT_LENS("131934", "Contact Lens"),
    TH_LOYALTY("131935", "Loyalty Services"),
    TH_ACCESSORIES("131936", "Accessories"),

    ID_EYEGLASSES("131932", "Eyeglasses"),
    ID_SUNGLASSES("131933", "Sunglasses"),
    ID_CONTACT_LENS("131934", "Contact Lens"),
    ID_LOYALTY("131935", "Loyalty Services"),
    ID_ACCESSORIES("131936", "Accessories");

    public static ProductId getByProductName(String productName) {
        if (productName == null) {
            throw new IllegalArgumentException("Product name cannot be null");
        }

        for (ProductId product : values()) {
            if (product.getProductName().equalsIgnoreCase(productName)) {
                return product;
            }
        }

        throw new IllegalArgumentException("Product name not found: " + productName);
    }

    public static ProductId getByProductId(String productId) {
        if (productId == null) {
            throw new IllegalArgumentException("Product ID cannot be null");
        }

        for (ProductId product : values()) {
            if (product.getProductId().equalsIgnoreCase(productId)) {
                return product;
            }
        }

        throw new IllegalArgumentException("Product ID not found: " + productId);
    }

    private final String productId;
    private final String productName;
}

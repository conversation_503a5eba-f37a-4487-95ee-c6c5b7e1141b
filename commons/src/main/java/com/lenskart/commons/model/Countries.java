package com.lenskart.commons.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enum representing supported countries in the system.
 * Each country is identified by its ISO 3166-1 alpha-2 country code.
 */
@AllArgsConstructor
@Getter
public enum Countries {

    /**
     * India
     */
    IN("India", "+91", "1876543210", "121004",PaymentMethod.COD.getDisplayName()),

    /**
     * Singapore
     */
    SG("Singapore", "+65", "62511322", "048886",PaymentMethod.OFFLINE_CASH.getDisplayName()),

    /**
     * United States
     */
    US("United States", "+1", "5551234567","78201",PaymentMethod.OFFLINE_CASH.getDisplayName()),

    /**
     * Indonesia
     */
    ID("Indonesia", "+62", "812345678","10110",PaymentMethod.OFFLINE_CASH.getDisplayName()),

    /**
     * United Arab Emirates
     */
    AE("United Arab Emirates", "+971", "585858585","000000",PaymentMethod.OFFLINE_CASH.getDisplayName()),

    /**
     * Saudi Arabia
     */
    SA("Saudi Arabia", "+966", "500000001","12215",PaymentMethod.OFFLINE_CASH.getDisplayName()),

    /**
     * Thailand
     */
    TH("Thailand", "+66", "21031033","10110",PaymentMethod.OFFLINE_CASH.getDisplayName());

    /**
     * Full country name
     */
    private final String countryName;

    /**
     * International dialing code
     */
    private final String dialingCode;

    /**
     * Default phone number for testing purposes
     */
    private final String defaultPhoneNumber;

    /**
     * Default pin code for testing purposes
     */
    private final String defaultPinCode;

    /**
     * Default payment method for testing purposes
     */
    private final String defaultPaymentMethod;


    /**
     * Get country by country name
     *
     * @param countryName The full country name
     * @return The corresponding Countries enum value
     * @throws IllegalArgumentException if the country name is not found
     */
    public static Countries getByCountryName(String countryName) {
        if (countryName == null) {
            throw new IllegalArgumentException("Country name cannot be null");
        }

        for (Countries country : values()) {
            if (country.getCountryName().equalsIgnoreCase(countryName)) {
                return country;
            }
        }

        throw new IllegalArgumentException("Country not found for name: " + countryName);
    }

    /**
     * Get country by dialing code
     *
     * @param dialingCode The international dialing code (with or without + prefix)
     * @return The corresponding Countries enum value
     * @throws IllegalArgumentException if the dialing code is not found
     */
    public static Countries getByDialingCode(String dialingCode) {
        if (dialingCode == null) {
            throw new IllegalArgumentException("Dialing code cannot be null");
        }

        // Normalize the dialing code by adding + if not present
        String normalizedCode = dialingCode.startsWith("+") ? dialingCode : "+" + dialingCode;

        for (Countries country : values()) {
            if (country.getDialingCode().equals(normalizedCode)) {
                return country;
            }
        }

        throw new IllegalArgumentException("Country not found for dialing code: " + dialingCode);
    }

    /**
     * Get country by default phone number
     *
     * @param phoneNumber The default phone number
     * @return The corresponding Countries enum value
     * @throws IllegalArgumentException if the phone number is not found
     */
    public static Countries getByDefaultPhoneNumber(String phoneNumber) {
        if (phoneNumber == null) {
            throw new IllegalArgumentException("Phone number cannot be null");
        }

        for (Countries country : values()) {
            if (country.getDefaultPhoneNumber().equals(phoneNumber)) {
                return country;
            }
        }

        throw new IllegalArgumentException("Country not found for phone number: " + phoneNumber);
    }

    /**
     * Get country by default pin code
     *
     * @param pinCode The default pin code
     * @return The corresponding Countries enum value
     * @throws IllegalArgumentException if the pin code is not found
     */
    public static Countries getByDefaultPinCode(String pinCode) {
        if (pinCode == null) {
            throw new IllegalArgumentException("Pin code cannot be null");
        }

        for (Countries country : values()) {
            if (country.getDefaultPinCode().equals(pinCode)) {
                return country;
            }
        }

        throw new IllegalArgumentException("Country not found for pin code: " + pinCode);
    }

    /**
     * Get all country names as an array
     *
     * @return Array of all country names
     */
    public static String[] getAllCountryNames() {
        Countries[] countries = values();
        String[] names = new String[countries.length];

        for (int i = 0; i < countries.length; i++) {
            names[i] = countries[i].getCountryName();
        }

        return names;
    }

    /**
     * Get the full phone number with country code
     *
     * @return Full phone number with country code (e.g., "+91 9876543210")
     */
    public String getFullPhoneNumber() {
        return dialingCode + " " + defaultPhoneNumber;
    }

    /**
     * Get the full phone number without spaces
     *
     * @return Full phone number without spaces (e.g., "+919876543210")
     */
    public String getFullPhoneNumberWithoutSpaces() {
        return dialingCode + defaultPhoneNumber;
    }

    /**
     * Get all default phone numbers as an array
     *
     * @return Array of all default phone numbers
     */
    public static String[] getAllDefaultPhoneNumbers() {
        Countries[] countries = values();
        String[] phoneNumbers = new String[countries.length];

        for (int i = 0; i < countries.length; i++) {
            phoneNumbers[i] = countries[i].getDefaultPhoneNumber();
        }

        return phoneNumbers;
    }

    /**
     * Get all default pin codes as an array
     *
     * @return Array of all default pin codes
     */
    public static String[] getAllDefaultPinCodes() {
        Countries[] countries = values();
        String[] pinCodes = new String[countries.length];

        for (int i = 0; i < countries.length; i++) {
            pinCodes[i] = countries[i].getDefaultPinCode();
        }

        return pinCodes;
    }

    @Override
    public String toString() {
        return String.format("%s %s - Default Phone: %s",
                countryName, dialingCode, getFullPhoneNumber(), defaultPinCode, defaultPaymentMethod);
    }
}

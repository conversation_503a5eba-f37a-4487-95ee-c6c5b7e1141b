package com.lenskart.commons.model;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Enum representing different payment methods available in the system.
 * Refactored for better maintainability and type safety.
 */
@Getter
public enum PaymentMethod {

    // Offline Payment Methods
    COD("cod", PaymentType.OFFLINE,
        "Payment made in cash upon delivery of the order", false),

    BANK_TRANSFER("Bank Transfer", PaymentType.OFFLINE,
                  "Direct bank transfer or NEFT/RTGS", true),

    // Card-based Payment Methods
    CREDIT_CARD("Credit Card", PaymentType.ONLINE,
                "Payment using credit card through secure gateway", true),

    DEBIT_CARD("Debit Card", PaymentType.ONLINE,
               "Payment using debit card through secure gateway", true),

    // Digital Payment Methods
    NET_BANKING("Net Banking", PaymentType.ONLINE,
                "Direct bank transfer through internet banking", true),

    UPI("UPI", PaymentType.ONLINE,
        "Payment through UPI apps like GPay, PhonePe, Paytm", true),

    WALLET("Digital Wallet", PaymentType.ONLINE,
           "Payment through digital wallets like Paytm, Amazon Pay", true),

    // Credit-based Payment Methods
    EMI("EMI", PaymentType.ONLINE,
        "Payment in installments through credit card or personal loan", true),

    BNPL("Buy Now Pay Later", PaymentType.ONLINE,
         "Deferred payment through BNPL services like Simpl, LazyPay", true),

    // Prepaid Payment Methods
    GIFT_CARD("Gift Card", PaymentType.ONLINE,
              "Payment using gift cards or vouchers", false),

    STORE_CREDIT("Store Credit", PaymentType.ONLINE,
                 "Payment using store credit or loyalty points", false),

    // Alternative Payment Methods
    CRYPTO("Cryptocurrency", PaymentType.ONLINE,
           "Payment using cryptocurrencies like Bitcoin, Ethereum", true);

    private final String displayName;
    private final PaymentType type;
    private final String description;
    private final boolean requiresAuthentication;

    /**
     * Constructor for PaymentMethod enum
     */
    PaymentMethod(String displayName, PaymentType type, String description, boolean requiresAuthentication) {
        this.displayName = displayName;
        this.type = type;
        this.description = description;
        this.requiresAuthentication = requiresAuthentication;
    }

    /**
     * Enum for payment types
     */
    public enum PaymentType {
        ONLINE("Online Payment"),
        OFFLINE("Offline Payment");

        @Getter
        private final String description;

        PaymentType(String description) {
            this.description = description;
        }
    }



    /**
     * Enum for processing speed
     */
    public enum ProcessingSpeed {
        INSTANT("Instant"),
        FAST("2-3 minutes"),
        MEDIUM("1-2 business days"),
        SLOW("1-3 business days"),
        ON_DELIVERY("On delivery"),
        VARIABLE("10-60 minutes");

        @Getter
        private final String description;

        ProcessingSpeed(String description) {
            this.description = description;
        }
    }

    // ==================== FINDER METHODS ====================

    /**
     * Get payment method by display name (case-insensitive)
     *
     * @param displayName The display name of the payment method
     * @return The corresponding PaymentMethod enum value
     * @throws IllegalArgumentException if the display name is not found
     */
    public static PaymentMethod getByDisplayName(String displayName) {
        if (displayName == null || displayName.trim().isEmpty()) {
            throw new IllegalArgumentException("Display name cannot be null or empty");
        }

        return Arrays.stream(values())
                .filter(method -> method.displayName.equalsIgnoreCase(displayName.trim()))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Payment method not found for display name: " + displayName));
    }

    /**
     * Get payment method by enum name (case-insensitive)
     *
     * @param name The enum name
     * @return The corresponding PaymentMethod enum value
     * @throws IllegalArgumentException if the name is not found
     */
    public static PaymentMethod getByName(String name) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("Name cannot be null or empty");
        }

        return Arrays.stream(values())
                .filter(method -> method.name().equalsIgnoreCase(name.trim()))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Payment method not found for name: " + name));
    }

    // ==================== FILTER METHODS ====================

    /**
     * Get all payment methods by type
     *
     * @param type The payment type
     * @return List of PaymentMethod in the specified type
     */
    public static List<PaymentMethod> getByType(PaymentType type) {
        if (type == null) {
            throw new IllegalArgumentException("Payment type cannot be null");
        }

        return Arrays.stream(values())
                .filter(method -> method.type == type)
                .collect(Collectors.toList());
    }



    /**
     * Get payment methods that require authentication
     *
     * @return List of PaymentMethod that require authentication
     */
    public static List<PaymentMethod> getAuthenticationRequired() {
        return Arrays.stream(values())
                .filter(method -> method.requiresAuthentication)
                .collect(Collectors.toList());
    }



    /**
     * Get online payment methods
     *
     * @return List of online PaymentMethod
     */
    public static List<PaymentMethod> getOnlinePaymentMethods() {
        return getByType(PaymentType.ONLINE);
    }

    /**
     * Get offline payment methods
     *
     * @return List of offline PaymentMethod
     */
    public static List<PaymentMethod> getOfflinePaymentMethods() {
        return getByType(PaymentType.OFFLINE);
    }

    /**
     * Get card-based payment methods
     *
     * @return List of card-based PaymentMethod
     */
    public static List<PaymentMethod> getCardBasedMethods() {
        return Arrays.stream(values())
                .filter(method -> method == CREDIT_CARD || method == DEBIT_CARD)
                .collect(Collectors.toList());
    }

    /**
     * Get digital payment methods (UPI, Wallet, Net Banking)
     *
     * @return List of digital PaymentMethod
     */
    public static List<PaymentMethod> getDigitalMethods() {
        return Arrays.stream(values())
                .filter(method -> method == UPI || method == WALLET || method == NET_BANKING)
                .collect(Collectors.toList());
    }

    // ==================== VALIDATION METHODS ====================

    /**
     * Check if a payment method name is valid
     *
     * @param name The payment method name
     * @return true if the payment method name is valid, false otherwise
     */
    public static boolean isValidPaymentMethod(String name) {
        if (name == null || name.trim().isEmpty()) {
            return false;
        }

        return Arrays.stream(values())
                .anyMatch(method -> method.name().equalsIgnoreCase(name.trim()) ||
                                  method.displayName.equalsIgnoreCase(name.trim()));
    }

    // ==================== UTILITY METHODS ====================

    /**
     * Get all payment method names as a list
     *
     * @return List of all payment method names
     */
    public static List<String> getAllPaymentMethodNames() {
        return Arrays.stream(values())
                .map(Enum::name)
                .collect(Collectors.toList());
    }

    /**
     * Get all display names as a list
     *
     * @return List of all display names
     */
    public static List<String> getAllDisplayNames() {
        return Arrays.stream(values())
                .map(PaymentMethod::getDisplayName)
                .collect(Collectors.toList());
    }

    /**
     * Get the payment method code for API calls
     *
     * @return Payment method code in lowercase
     */
    public String getPaymentMethodCode() {
        return this.name().toLowerCase();
    }

    // ==================== BUSINESS LOGIC METHODS ====================

    /**
     * Check if the payment method is instant
     *
     * @return true if payment is processed instantly, false otherwise
     */
    public boolean isInstantPayment() {
        return getProcessingSpeed() == ProcessingSpeed.INSTANT;
    }

    /**
     * Get the processing speed for this payment method
     *
     * @return ProcessingSpeed enum value
     */
    public ProcessingSpeed getProcessingSpeed() {
        return switch (this) {
            case CREDIT_CARD, DEBIT_CARD, UPI, WALLET, GIFT_CARD, STORE_CREDIT -> ProcessingSpeed.INSTANT;
            case NET_BANKING -> ProcessingSpeed.FAST;
            case COD -> ProcessingSpeed.ON_DELIVERY;
            case EMI, BNPL -> ProcessingSpeed.MEDIUM;
            case BANK_TRANSFER -> ProcessingSpeed.SLOW;
            case CRYPTO -> ProcessingSpeed.VARIABLE;
            default -> ProcessingSpeed.MEDIUM;
        };
    }

    /**
     * Check if the payment method supports refunds
     *
     * @return true if refunds are supported, false otherwise
     */
    public boolean supportsRefunds() {
        return switch (this) {
            case CREDIT_CARD, DEBIT_CARD, NET_BANKING, UPI, WALLET, BANK_TRANSFER -> true;
            case COD, EMI, BNPL, GIFT_CARD, STORE_CREDIT, CRYPTO -> false;
            default -> false;
        };
    }

    /**
     * Check if the payment method is suitable for high-value transactions
     *
     * @return true if suitable for high-value transactions, false otherwise
     */
    public boolean isSuitableForHighValue() {
        // High-value transactions typically require authentication and refund support
        return requiresAuthentication && supportsRefunds();
    }

    /**
     * Check if the payment method requires internet connectivity
     *
     * @return true if internet is required, false otherwise
     */
    public boolean requiresInternet() {
        return type == PaymentType.ONLINE;
    }

    /**
     * Get user-friendly processing time description
     *
     * @return Processing time description
     */
    public String getProcessingTimeDescription() {
        return getProcessingSpeed().getDescription();
    }

    /**
     * Check if the payment method is mobile-friendly
     *
     * @return true if mobile-friendly, false otherwise
     */
    public boolean isMobileFriendly() {
        return switch (this) {
            case UPI, WALLET, CREDIT_CARD, DEBIT_CARD -> true;
            case NET_BANKING, EMI, BNPL, GIFT_CARD, STORE_CREDIT, CRYPTO -> false;
            case COD, BANK_TRANSFER -> false;
            default -> false;
        };
    }

    @Override
    public String toString() {
        return String.format("%s (%s) - Processing: %s, Refunds: %s, Auth Required: %s",
                displayName, type.getDescription(), getProcessingTimeDescription(),
                supportsRefunds() ? "Yes" : "No", requiresAuthentication ? "Yes" : "No");
    }
}

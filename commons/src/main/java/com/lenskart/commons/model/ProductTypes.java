package com.lenskart.commons.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enum representing different product types available in the Lenskart system.
 * Each product type has associated properties like display name, category, and description.
 */
@AllArgsConstructor
@Getter
public enum ProductTypes {
    
    /**
     * Prescription and non-prescription eyeglasses
     */
    EYEGLASSES("Eyeglasses", "OPTICAL", "Prescription and non-prescription eyeglasses for vision correction and style"),
    
    /**
     * Sunglasses for UV protection and style
     */
    SUNGLASSES("Sunglasses", "OPTICAL", "UV protection sunglasses for outdoor activities and fashion"),
    
    /**
     * Contact lenses for vision correction
     */
    CONTACT_LENS("Contact Lens", "OPTICAL", "Contact lenses for vision correction and cosmetic enhancement"),


    LOYALTY("Loyalty Services", "OPTICAL", "Contact lenses for vision correction and cosmetic enhancement"),

    ACCESSORIES("Accessories", "OPTICAL", "Contact lenses for vision correction and cosmetic enhancement");
    
    /**
     * Display name for the product type
     */
    private final String displayName;
    
    /**
     * Product category
     */
    private final String category;
    
    /**
     * Description of the product type
     */
    private final String description;
    
    /**
     * Get product type by display name
     * 
     * @param displayName The display name of the product type
     * @return The corresponding ProductTypes enum value
     * @throws IllegalArgumentException if the display name is not found
     */
    public static ProductTypes getByDisplayName(String displayName) {
        if (displayName == null) {
            throw new IllegalArgumentException("Display name cannot be null");
        }
        
        for (ProductTypes productType : values()) {
            if (productType.getDisplayName().equalsIgnoreCase(displayName)) {
                return productType;
            }
        }
        
        throw new IllegalArgumentException("Product type not found for display name: " + displayName);
    }
    
    /**
     * Get product type by enum name (case-insensitive)
     * 
     * @param name The enum name
     * @return The corresponding ProductTypes enum value
     * @throws IllegalArgumentException if the name is not found
     */
    public static ProductTypes getByName(String name) {
        if (name == null) {
            throw new IllegalArgumentException("Name cannot be null");
        }
        
        for (ProductTypes productType : values()) {
            if (productType.name().equalsIgnoreCase(name)) {
                return productType;
            }
        }
        
        throw new IllegalArgumentException("Product type not found for name: " + name);
    }
    
    /**
     * Get all product types by category
     * 
     * @param category The product category
     * @return Array of ProductTypes in the specified category
     */
    public static ProductTypes[] getByCategory(String category) {
        if (category == null) {
            throw new IllegalArgumentException("Category cannot be null");
        }
        
        return java.util.Arrays.stream(values())
                .filter(productType -> productType.getCategory().equalsIgnoreCase(category))
                .toArray(ProductTypes[]::new);
    }
    
    /**
     * Check if a product type name is valid
     * 
     * @param name The product type name
     * @return true if the product type name is valid, false otherwise
     */
    public static boolean isValidProductType(String name) {
        if (name == null) {
            return false;
        }
        
        for (ProductTypes productType : values()) {
            if (productType.name().equalsIgnoreCase(name) || 
                productType.getDisplayName().equalsIgnoreCase(name)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Get all product type names as an array
     * 
     * @return Array of all product type names
     */
    public static String[] getAllProductTypeNames() {
        ProductTypes[] productTypes = values();
        String[] names = new String[productTypes.length];
        
        for (int i = 0; i < productTypes.length; i++) {
            names[i] = productTypes[i].name();
        }
        
        return names;
    }
    
    /**
     * Get all display names as an array
     * 
     * @return Array of all display names
     */
    public static String[] getAllDisplayNames() {
        ProductTypes[] productTypes = values();
        String[] displayNames = new String[productTypes.length];
        
        for (int i = 0; i < productTypes.length; i++) {
            displayNames[i] = productTypes[i].getDisplayName();
        }
        
        return displayNames;
    }
    
    /**
     * Check if the product type requires prescription
     * 
     * @return true if the product type typically requires prescription, false otherwise
     */
    public boolean requiresPrescription() {
        return switch (this) {
            case EYEGLASSES, CONTACT_LENS -> true;
            case SUNGLASSES -> false;
            default -> false;
        };
    }
    
    /**
     * Check if the product type is wearable
     * 
     * @return true if the product type is wearable, false otherwise
     */
    public boolean isWearable() {
        // All current product types are wearable
        return true;
    }
    
    /**
     * Get the product type code for API calls
     * 
     * @return Product type code in lowercase
     */
    public String getProductTypeCode() {
        return this.name().toLowerCase();
    }
    
    @Override
    public String toString() {
        return String.format("%s (%s) - %s", displayName, category, description);
    }
}

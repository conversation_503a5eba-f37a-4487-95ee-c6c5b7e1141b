package com.lenskart.commons.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Enum representing different power types available for lenses.
 * Each power type has associated properties like display name, description, and characteristics.
 */
@AllArgsConstructor
@Getter
public enum PowerTypes {
    
    /**
     * Single vision lenses for correcting one field of vision
     */
    SINGLE_VISION("single_vision", "Lenses that correct one field of vision (distance or near)", true, false, "PRESCRIPTION"),
    
    /**
     * Bifocal lenses with two distinct optical powers
     */
    BIFOCAL("bifocal", "Lenses with two distinct optical powers for distance and near vision", true, true, "PRESCRIPTION"),
    
    /**
     * Zero power lenses with no prescription
     */
    ZERO_POWER("zero_power", "Lenses with no prescription power, used for protection or fashion", false, false, "NON_PRESCRIPTION"),
    
    /**
     * Sunglasses lenses with UV protection
     */
    SUNGLASSES("sunglasses", "Tinted lenses designed for UV protection and glare reduction", true, false, "PROTECTIVE"),
    
    /**
     * Contact lenses for vision correction
     */
    CONTACT_LENS("Contact Lens", "Soft or hard contact lenses for vision correction", true, false, "PRESCRIPTION"),
    
    /**
     * Tinted single vision lenses
     */
    TINTED_SV("Tinted Single Vision", "Single vision lenses with tinting for light sensitivity or fashion", true, false, "PRESCRIPTION");
    
    /**
     * Display name for the power type
     */
    private final String displayName;
    
    /**
     * Description of the power type
     */
    private final String description;
    
    /**
     * Whether the power type requires prescription
     */
    private final boolean requiresPrescription;
    
    /**
     * Whether the power type supports multiple focal points
     */
    private final boolean supportsMultipleFocals;
    
    /**
     * Category of the power type
     */
    private final String category;
    
    /**
     * Get power type by display name
     * 
     * @param displayName The display name of the power type
     * @return The corresponding PowerTypes enum value
     * @throws IllegalArgumentException if the display name is not found
     */
    public static PowerTypes getByDisplayName(String displayName) {
        if (displayName == null) {
            throw new IllegalArgumentException("Display name cannot be null");
        }
        
        for (PowerTypes powerType : values()) {
            if (powerType.getDisplayName().equalsIgnoreCase(displayName)) {
                return powerType;
            }
        }
        
        throw new IllegalArgumentException("Power type not found for display name: " + displayName);
    }
    
    /**
     * Get power type by enum name (case-insensitive)
     * 
     * @param name The enum name
     * @return The corresponding PowerTypes enum value
     * @throws IllegalArgumentException if the name is not found
     */
    public static PowerTypes getByName(String name) {
        if (name == null) {
            throw new IllegalArgumentException("Name cannot be null");
        }
        
        for (PowerTypes powerType : values()) {
            if (powerType.name().equalsIgnoreCase(name)) {
                return powerType;
            }
        }
        
        throw new IllegalArgumentException("Power type not found for name: " + name);
    }
    
    /**
     * Get all power types by category
     * 
     * @param category The power type category
     * @return Array of PowerTypes in the specified category
     */
    public static PowerTypes[] getByCategory(String category) {
        if (category == null) {
            throw new IllegalArgumentException("Category cannot be null");
        }
        
        return java.util.Arrays.stream(values())
                .filter(powerType -> powerType.category.equalsIgnoreCase(category))
                .toArray(PowerTypes[]::new);
    }
    
    /**
     * Get power types that require prescription
     * 
     * @return Array of PowerTypes that require prescription
     */
    public static PowerTypes[] getPrescriptionTypes() {
        return java.util.Arrays.stream(values())
                .filter(powerType -> powerType.requiresPrescription)
                .toArray(PowerTypes[]::new);
    }
    
    /**
     * Get power types that support multiple focal points
     * 
     * @return Array of PowerTypes that support multiple focal points
     */
    public static PowerTypes[] getMultiFocalTypes() {
        return java.util.Arrays.stream(values())
                .filter(powerType -> powerType.supportsMultipleFocals)
                .toArray(PowerTypes[]::new);
    }
    
    /**
     * Get non-prescription power types
     * 
     * @return Array of PowerTypes that don't require prescription
     */
    public static PowerTypes[] getNonPrescriptionTypes() {
        return java.util.Arrays.stream(values())
                .filter(powerType -> !powerType.requiresPrescription)
                .toArray(PowerTypes[]::new);
    }
    
    /**
     * Check if a power type name is valid
     * 
     * @param name The power type name
     * @return true if the power type name is valid, false otherwise
     */
    public static boolean isValidPowerType(String name) {
        if (name == null) {
            return false;
        }
        
        for (PowerTypes powerType : values()) {
            if (powerType.name().equalsIgnoreCase(name) || 
                powerType.getDisplayName().equalsIgnoreCase(name)) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Get all power type names as an array
     * 
     * @return Array of all power type names
     */
    public static String[] getAllPowerTypeNames() {
        PowerTypes[] powerTypes = values();
        String[] names = new String[powerTypes.length];
        
        for (int i = 0; i < powerTypes.length; i++) {
            names[i] = powerTypes[i].name();
        }
        
        return names;
    }
    
    /**
     * Get all display names as an array
     * 
     * @return Array of all display names
     */
    public static String[] getAllDisplayNames() {
        PowerTypes[] powerTypes = values();
        String[] displayNames = new String[powerTypes.length];
        
        for (int i = 0; i < powerTypes.length; i++) {
            displayNames[i] = powerTypes[i].getDisplayName();
        }
        
        return displayNames;
    }
    
    /**
     * Get the power type code for API calls
     * 
     * @return Power type code in lowercase
     */
    public String getPowerTypeCode() {
        return this.name().toLowerCase();
    }
    
    /**
     * Check if the power type is suitable for computer use
     * 
     * @return true if suitable for computer use, false otherwise
     */
    public boolean isSuitableForComputerUse() {
        return switch (this) {
            case SINGLE_VISION, TINTED_SV -> true; // Good for computer work
            case BIFOCAL -> false; // Can be challenging for computer work
            case ZERO_POWER, SUNGLASSES -> false; // Not designed for computer use
            case CONTACT_LENS -> true; // Good for computer work
            default -> false;
        };
    }
    
    /**
     * Check if the power type provides UV protection
     * 
     * @return true if provides UV protection, false otherwise
     */
    public boolean providesUVProtection() {
        return switch (this) {
            case SUNGLASSES -> true; // Primary purpose is UV protection
            case TINTED_SV -> true; // Tinted lenses often provide UV protection
            case SINGLE_VISION, BIFOCAL, ZERO_POWER, CONTACT_LENS ->
                    false; // Standard versions don't provide UV protection
            default -> false;
        };
    }
    
    /**
     * Get the complexity level of the power type
     * 
     * @return Complexity level (1-5, where 5 is most complex)
     */
    public int getComplexityLevel() {
        return switch (this) {
            case ZERO_POWER -> 1; // Simplest
            case SUNGLASSES -> 2; // Simple tinting
            case SINGLE_VISION, CONTACT_LENS -> 3; // Standard prescription
            case TINTED_SV -> 4; // Prescription + tinting
            case BIFOCAL -> 5; // Most complex with multiple focal points
            default -> 1;
        };
    }
    
    /**
     * Check if the power type is suitable for outdoor activities
     * 
     * @return true if suitable for outdoor activities, false otherwise
     */
    public boolean isSuitableForOutdoor() {
        return switch (this) {
            case SUNGLASSES, TINTED_SV -> true; // Designed for outdoor use
            case CONTACT_LENS -> true; // Good for sports and outdoor activities
            case SINGLE_VISION, BIFOCAL, ZERO_POWER ->
                    false; // Standard versions not ideal for bright outdoor conditions
            default -> false;
        };
    }
    
    @Override
    public String toString() {
        return String.format("%s (%s) - Complexity: %d/5, UV Protection: %s", 
                displayName, category, getComplexityLevel(), providesUVProtection());
    }
}

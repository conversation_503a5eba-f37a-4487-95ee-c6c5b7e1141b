package com.lenskart.commons.utils;

import com.aventstack.extentreports.ExtentTest;
import com.aventstack.extentreports.Status;
import com.aventstack.extentreports.markuputils.ExtentColor;
import com.aventstack.extentreports.markuputils.MarkupHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Utility class for enhanced ExtentReport logging
 * Provides methods to add detailed step-by-step logging to extent reports
 */
public class ExtentReportUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(ExtentReportUtils.class);
    private static final DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss.SSS");
    private static ThreadLocal<ExtentTest> currentTest = new ThreadLocal<>();
    
    /**
     * Set the current test for the thread
     * @param test ExtentTest instance
     */
    public static void setCurrentTest(ExtentTest test) {
        currentTest.set(test);
    }
    
    /**
     * Get the current test for the thread
     * @return ExtentTest instance or null if not set
     */
    public static ExtentTest getCurrentTest() {
        return currentTest.get();
    }
    
    /**
     * Clear the current test for the thread
     */
    public static void clearCurrentTest() {
        currentTest.remove();
    }
    
    /**
     * Log an info step with timestamp
     * @param message Step message
     */
    public static void logInfo(String message) {
        ExtentTest test = getCurrentTest();
        if (test != null) {
            String timestamp = LocalDateTime.now().format(timeFormatter);
            test.info("🔵 [" + timestamp + "] " + message);
            logger.info(message);
        }
    }
    
    /**
     * Log a success step with timestamp
     * @param message Step message
     */
    public static void logPass(String message) {
        ExtentTest test = getCurrentTest();
        if (test != null) {
            String timestamp = LocalDateTime.now().format(timeFormatter);
            test.pass("✅ [" + timestamp + "] " + message);
            logger.info("PASS: {}", message);
        }
    }
    
    /**
     * Log a failure step with timestamp
     * @param message Step message
     */
    public static void logFail(String message) {
        ExtentTest test = getCurrentTest();
        if (test != null) {
            String timestamp = LocalDateTime.now().format(timeFormatter);
            test.fail("❌ [" + timestamp + "] " + message);
            logger.error("FAIL: {}", message);
        }
    }
    
    /**
     * Log a warning step with timestamp
     * @param message Step message
     */
    public static void logWarning(String message) {
        ExtentTest test = getCurrentTest();
        if (test != null) {
            String timestamp = LocalDateTime.now().format(timeFormatter);
            test.warning("⚠️ [" + timestamp + "] " + message);
            logger.warn("WARNING: {}", message);
        }
    }
    
    /**
     * Log a skip step with timestamp
     * @param message Step message
     */
    public static void logSkip(String message) {
        ExtentTest test = getCurrentTest();
        if (test != null) {
            String timestamp = LocalDateTime.now().format(timeFormatter);
            test.skip("⏭️ [" + timestamp + "] " + message);
            logger.info("SKIP: {}", message);
        }
    }
    
    /**
     * Log an API request details
     * @param method HTTP method
     * @param url Request URL
     * @param payload Request payload (optional)
     */
    public static void logApiRequest(String method, String url, String payload) {
        ExtentTest test = getCurrentTest();
        if (test != null) {
            String timestamp = LocalDateTime.now().format(timeFormatter);
            test.info("🌐 [" + timestamp + "] <b>API Request:</b>");
            test.info("📤 <b>Method:</b> " + method);
            test.info("🔗 <b>URL:</b> " + url);
            if (payload != null && !payload.isEmpty()) {
                test.info("📋 <b>Payload:</b><br><pre>" + payload + "</pre>");
            }
            logger.info("API Request: {} {}", method, url);
        }
    }
    
    /**
     * Log an API response details
     * @param statusCode Response status code
     * @param responseBody Response body (optional)
     * @param responseTime Response time in milliseconds
     */
    public static void logApiResponse(int statusCode, String responseBody, long responseTime) {
        ExtentTest test = getCurrentTest();
        if (test != null) {
            String timestamp = LocalDateTime.now().format(timeFormatter);
            test.info("🌐 [" + timestamp + "] <b>API Response:</b>");
            
            // Color code status based on HTTP status
            String statusColor = getStatusColor(statusCode);
            test.info("📥 <b>Status Code:</b> <span style='color:" + statusColor + "'>" + statusCode + "</span>");
            test.info("⏱️ <b>Response Time:</b> " + responseTime + " ms");
            
            if (responseBody != null && !responseBody.isEmpty()) {
                // Limit response body to avoid too much noise
                String truncatedBody = responseBody.length() > 1000 ? 
                    responseBody.substring(0, 1000) + "... (truncated)" : responseBody;
                test.info("📋 <b>Response Body:</b><br><pre>" + truncatedBody + "</pre>");
            }
            
            logger.info("API Response: {} - {} ms", statusCode, responseTime);
        }
    }
    
    /**
     * Log database operation details
     * @param operation Database operation (SELECT, INSERT, UPDATE, DELETE)
     * @param table Table name
     * @param details Additional details
     */
    public static void logDatabaseOperation(String operation, String table, String details) {
        ExtentTest test = getCurrentTest();
        if (test != null) {
            String timestamp = LocalDateTime.now().format(timeFormatter);
            test.info("🗄️ [" + timestamp + "] <b>Database Operation:</b>");
            test.info("🔧 <b>Operation:</b> " + operation);
            test.info("📊 <b>Table:</b> " + table);
            if (details != null && !details.isEmpty()) {
                test.info("📝 <b>Details:</b> " + details);
            }
            logger.info("Database Operation: {} on {}", operation, table);
        }
    }
    
    /**
     * Log test step with custom icon
     * @param icon Custom icon/emoji
     * @param stepName Step name
     * @param details Step details
     */
    public static void logStep(String icon, String stepName, String details) {
        ExtentTest test = getCurrentTest();
        if (test != null) {
            String timestamp = LocalDateTime.now().format(timeFormatter);
            test.info(icon + " [" + timestamp + "] <b>" + stepName + "</b>");
            if (details != null && !details.isEmpty()) {
                test.info("📝 " + details);
            }
            logger.info("Step: {} - {}", stepName, details);
        }
    }
    
    /**
     * Log test data being used
     * @param dataName Name of the test data
     * @param dataValue Value of the test data
     */
    public static void logTestData(String dataName, String dataValue) {
        ExtentTest test = getCurrentTest();
        if (test != null) {
            String timestamp = LocalDateTime.now().format(timeFormatter);
            test.info("📊 [" + timestamp + "] <b>Test Data:</b> " + dataName + " = " + dataValue);
            logger.info("Test Data: {} = {}", dataName, dataValue);
        }
    }
    
    /**
     * Log assertion details
     * @param expected Expected value
     * @param actual Actual value
     * @param passed Whether assertion passed
     */
    public static void logAssertion(String expected, String actual, boolean passed) {
        ExtentTest test = getCurrentTest();
        if (test != null) {
            String timestamp = LocalDateTime.now().format(timeFormatter);
            String icon = passed ? "✅" : "❌";
            String status = passed ? "PASSED" : "FAILED";
            
            test.info(icon + " [" + timestamp + "] <b>Assertion " + status + ":</b>");
            test.info("🎯 <b>Expected:</b> " + expected);
            test.info("📋 <b>Actual:</b> " + actual);
            
            if (passed) {
                logger.info("Assertion PASSED: Expected={}, Actual={}", expected, actual);
            } else {
                logger.error("Assertion FAILED: Expected={}, Actual={}", expected, actual);
            }
        }
    }
    
    /**
     * Get color for HTTP status code
     * @param statusCode HTTP status code
     * @return Color string
     */
    private static String getStatusColor(int statusCode) {
        if (statusCode >= 200 && statusCode < 300) {
            return "#28a745"; // Green for success
        } else if (statusCode >= 300 && statusCode < 400) {
            return "#ffc107"; // Yellow for redirects
        } else if (statusCode >= 400 && statusCode < 500) {
            return "#fd7e14"; // Orange for client errors
        } else if (statusCode >= 500) {
            return "#dc3545"; // Red for server errors
        } else {
            return "#6c757d"; // Gray for others
        }
    }
    
    /**
     * Create a section header in the extent report
     * @param sectionName Name of the section
     */
    public static void createSection(String sectionName) {
        ExtentTest test = getCurrentTest();
        if (test != null) {
            String timestamp = LocalDateTime.now().format(timeFormatter);
            test.info(MarkupHelper.createLabel("📋 " + sectionName + " [" + timestamp + "]", ExtentColor.BLUE));
            logger.info("=== {} ===", sectionName);
        }
    }
}

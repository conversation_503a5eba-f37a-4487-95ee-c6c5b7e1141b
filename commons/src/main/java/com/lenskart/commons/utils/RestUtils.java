package com.lenskart.commons.utils;

import io.restassured.RestAssured;
import io.restassured.filter.log.RequestLoggingFilter;
import io.restassured.filter.log.ResponseLoggingFilter;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import io.restassured.specification.RequestSpecification;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * Utility class for making REST API calls using Rest Assured
 */
@Slf4j
public class RestUtils {

    /**
     * Performs a GET request to the specified URL
     *
     * @param url         The URL to send the GET request to
     * @param headers     Map of headers to include in the request (can be null)
     * @param queryParams Map of query parameters to include in the request (can be null)
     * @return Response object containing the API response
     */
    public static Response get(String url, Map<String, String> headers, Map<String, Object> queryParams) {
        log.info("Making GET request to: {}", url);

        RequestSpecification request = RestAssured.given()
                .filter(new RequestLoggingFilter())
                .filter(new ResponseLoggingFilter());

        // Add headers if provided
        if (headers != null && !headers.isEmpty()) {
            log.debug("Request headers: {}", headers);
            request.headers(headers);
        }

        // Add query parameters if provided
        if (queryParams != null && !queryParams.isEmpty()) {
            log.debug("Query parameters: {}", queryParams);
            request.queryParams(queryParams);
        }

        // Execute the GET request
        Response response = request.get(url);

        // Log response details
        logResponse(response);

        return response;
    }

    /**
     * Performs a GET request to the specified URL
     *
     * @param url         The URL to send the GET request to
     * @param headers     Map of headers to include in the request (can be null)
     * @param queryParams Map of query parameters to include in the request (can be null)
     * @param expectedStatusCode Status Code to match
     * @return Response object containing the API response
     */

    public static Response get(String url, Map<String, String> headers, Map<String, Object> queryParams, int expectedStatusCode) {
        Response response = get(url, headers, queryParams);
        if (response.getStatusCode() != expectedStatusCode) {
            throw new RuntimeException("API call failed : ".concat(url));
        }
        return response;
    }

    /**
     * Performs a POST request to the specified URL with a JSON payload
     *
     * @param url     The URL to send the POST request to
     * @param headers Map of headers to include in the request (can be null)
     * @param payload The object to be serialized as JSON and sent as the request body
     * @return Response object containing the API response
     */
    public static Response post(String url, Map<String, String> headers, Object payload) {
        log.info("Making POST request to: {}", url);

        RequestSpecification request = RestAssured.given()
                .filter(new RequestLoggingFilter())
                .filter(new ResponseLoggingFilter());

        // Add headers if provided
        if (headers != null && !headers.isEmpty()) {
            log.debug("Request headers: {}", headers);
            request.headers(headers);
        }

        // Add payload if provided
        if (payload != null) {
            log.debug("Request payload: {}", payload);
            request.contentType(ContentType.JSON);
            request.body(payload);
        }

        // Execute the POST request
        Response response = request.post(url);

        // Log response details
        logResponse(response);

        return response;
    }


    /**
     * Performs a POST request to the specified URL
     *
     * @param url         The URL to send the GET request to
     * @param headers     Map of headers to include in the request (can be null)
     * @param payload     The object to be serialized as JSON and sent as the request body
     * @param expectedStatusCode Status Code to match
     * @return Response object containing the API response
     */

    public static Response post(String url, Map<String, String> headers, String payload, int expectedStatusCode) {
        Response response = post(url, headers, payload);
        if (response.getStatusCode() != expectedStatusCode) {
            throw new RuntimeException("API call failed : ".concat(url));
        }
        return response;
    }

    /**
     * Performs a PUT request to the specified URL with a JSON payload
     *
     * @param url     The URL to send the PUT request to
     * @param headers Map of headers to include in the request (can be null)
     * @param payload The object to be serialized as JSON and sent as the request body
     * @return Response object containing the API response
     */
    public static Response put(String url, Map<String, String> headers, Object payload) {
        log.info("Making PUT request to: {}", url);

        RequestSpecification request = RestAssured.given()
                .filter(new RequestLoggingFilter())
                .filter(new ResponseLoggingFilter());

        // Add headers if provided
        if (headers != null && !headers.isEmpty()) {
            log.debug("Request headers: {}", headers);
            request.headers(headers);
        }

        // Add payload if provided
        if (payload != null) {
            log.debug("Request payload: {}", payload);
            request.contentType(ContentType.JSON);
            request.body(payload);
        }

        // Execute the PUT request
        Response response = request.put(url);

        // Log response details
        logResponse(response);

        return response;
    }


    /**
     * Performs a PUT request to the specified URL with a JSON payload
     *
     * @param url     The URL to send the PUT request to
     * @param headers Map of headers to include in the request (can be null)
     * @param payload The object to be serialized as JSON and sent as the request body
     * @param expectedStatusCode Status Code to match
     * @return Response object containing the API response
     */

    public static Response put(String url, Map<String, String> headers, String payload, int expectedStatusCode) {
        Response response = put(url, headers, payload);
        if (response.getStatusCode() != expectedStatusCode) {
            throw new RuntimeException("API call failed : ".concat(url));
        }
        return response;
    }

    /**
     * Performs a DELETE request to the specified URL
     *
     * @param url     The URL to send the DELETE request to
     * @param headers Map of headers to include in the request (can be null)
     * @return Response object containing the API response
     */
    public static Response delete(String url, Map<String, String> headers) {
        log.info("Making DELETE request to: {}", url);

        RequestSpecification request = RestAssured.given()
                .filter(new RequestLoggingFilter())
                .filter(new ResponseLoggingFilter());

        // Add headers if provided
        if (headers != null && !headers.isEmpty()) {
            log.debug("Request headers: {}", headers);
            request.headers(headers);
        }

        // Execute the DELETE request
        Response response = request.delete(url);

        // Log response details
        logResponse(response);

        return response;
    }


    /**
     * Performs a DELETE request to the specified URL
     *
     * @param url     The URL to send the DELETE request to
     * @param headers Map of headers to include in the request (can be null)
     * @param expectedStatusCode Status Code to match
     * @return Response object containing the API response
     */

    public static Response delete(String url, Map<String, String> headers, int expectedStatusCode) {
        Response response = delete(url, headers);
        if (response.getStatusCode() != expectedStatusCode) {
            throw new RuntimeException("API call failed : ".concat(url));
        }
        return response;
    }

    /**
     * Validates that the response status code is as expected
     *
     * @param response     The response to validate
     * @param expectedCode The expected status code
     * @return True if the status code matches, false otherwise
     */
    public static boolean validateStatusCode(Response response, int expectedCode) {
        return response.getStatusCode() == expectedCode;
    }

    /**
     * Extracts a value from the response JSON using a JSON path
     *
     * @param response The response containing JSON
     * @param jsonPath The JSON path to extract the value from
     * @return The extracted value
     */
    public static Object getValueFromResponse(Response response, String jsonPath) {
        return response.jsonPath().get(jsonPath);
    }

    /**
     * Logs details about the API response
     *
     * @param response The response to log
     */
    private static void logResponse(Response response) {
        log.info("Response Status Code: {}", response.getStatusCode());
        // Log response body if not too large
        String responseBody = response.getBody().asString();
        if (responseBody.length() > 1000) {
            log.debug("Response Body (truncated): {}", responseBody.substring(0, 1000) + "...");
        } else {
            log.debug("Response Body: {}", responseBody);
        }
    }
}
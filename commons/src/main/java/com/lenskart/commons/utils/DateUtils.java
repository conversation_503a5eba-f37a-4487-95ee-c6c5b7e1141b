package com.lenskart.commons.utils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;

public class DateUtils {


    public static LocalDate getFirstDayOfCurrentMonth() { return LocalDate.now().withDayOfMonth(1); }

    public static LocalDate getCurrentDayOfCurrentMonth() { return LocalDate.now(); }

    public static String formatDateToString(LocalDate date) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        return date.format(formatter);
    }

}

package com.lenskart.commons.utils;

import lombok.extern.slf4j.Slf4j;
import org.awaitility.Awaitility;
import org.awaitility.core.ConditionFactory;
import org.awaitility.core.ConditionTimeoutException;

import java.time.Duration;
import java.util.concurrent.Callable;
import java.util.function.Supplier;

/**
 * Comprehensive utility class for waiting, polling, and synchronization operations
 * using the Awaitility library. Provides various methods for different waiting scenarios
 * commonly used in automation testing.
 */
@Slf4j
public class AwaitUtils {

    // Default timeout configurations
    public static final Duration DEFAULT_TIMEOUT = Duration.ofSeconds(30);
    public static final Duration DEFAULT_POLL_INTERVAL = Duration.ofMillis(500);
    public static final Duration SHORT_TIMEOUT = Duration.ofSeconds(10);
    public static final Duration LONG_TIMEOUT = Duration.ofMinutes(2);
    public static final Duration VERY_LONG_TIMEOUT = Duration.ofMinutes(5);

    /**
     * Private constructor to prevent instantiation
     */
    private AwaitUtils() {
        throw new UnsupportedOperationException("AwaitUtils is a utility class and cannot be instantiated");
    }

    // ==================== BASIC SLEEP METHODS ====================

    /**
     * Simple sleep for specified duration
     *
     * @param duration Duration to sleep
     */
    public static void sleep(Duration duration) {
        log.debug("Sleeping for {}", duration);
        try {
            Thread.sleep(duration.toMillis());
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("Sleep interrupted: {}", e.getMessage());
        }
    }

    /**
     * Simple sleep for specified seconds
     *
     * @param seconds Number of seconds to sleep
     */
    public static void sleepSeconds(int seconds) {
        sleep(Duration.ofSeconds(seconds));
    }

    /**
     * Simple sleep for specified milliseconds
     *
     * @param milliseconds Number of milliseconds to sleep
     */
    public static void sleepMillis(long milliseconds) {
        sleep(Duration.ofMillis(milliseconds));
    }

    // ==================== CONDITION WAITING METHODS ====================

    /**
     * Wait until a condition becomes true with default timeout
     *
     * @param condition Condition to wait for
     * @param description Description of what we're waiting for (for logging)
     * @return true if condition was met, false if timeout occurred
     */
    public static boolean waitUntil(Callable<Boolean> condition, String description) {
        return waitUntil(condition, description, DEFAULT_TIMEOUT);
    }

    /**
     * Wait until a condition becomes true with specified timeout
     *
     * @param condition Condition to wait for
     * @param description Description of what we're waiting for (for logging)
     * @param timeout Maximum time to wait
     * @return true if condition was met, false if timeout occurred
     */
    public static boolean waitUntil(Callable<Boolean> condition, String description, Duration timeout) {
        return waitUntil(condition, description, timeout, DEFAULT_POLL_INTERVAL);
    }

    /**
     * Wait until a condition becomes true with specified timeout and poll interval
     *
     * @param condition Condition to wait for
     * @param description Description of what we're waiting for (for logging)
     * @param timeout Maximum time to wait
     * @param pollInterval How often to check the condition
     * @return true if condition was met, false if timeout occurred
     */
    public static boolean waitUntil(Callable<Boolean> condition, String description, Duration timeout, Duration pollInterval) {
        log.info("Waiting for: {} (timeout: {}, poll interval: {})", description, timeout, pollInterval);

        try {
            Awaitility.await(description)
                    .atMost(timeout)
                    .pollInterval(pollInterval)
                    .until(condition);

            log.info("✅ Condition met: {}", description);
            return true;

        } catch (ConditionTimeoutException e) {
            log.warn("⏰ Timeout waiting for: {} after {}", description, timeout);
            return false;
        } catch (Exception e) {
            log.error("❌ Error while waiting for: {} - {}", description, e.getMessage());
            return false;
        }
    }

    // ==================== PREDEFINED TIMEOUT METHODS ====================

    /**
     * Wait with short timeout (10 seconds)
     */
    public static boolean waitUntilShort(Callable<Boolean> condition, String description) {
        return waitUntil(condition, description, SHORT_TIMEOUT);
    }

    /**
     * Wait with long timeout (2 minutes)
     */
    public static boolean waitUntilLong(Callable<Boolean> condition, String description) {
        return waitUntil(condition, description, LONG_TIMEOUT);
    }

    /**
     * Wait with very long timeout (5 minutes)
     */
    public static boolean waitUntilVeryLong(Callable<Boolean> condition, String description) {
        return waitUntil(condition, description, VERY_LONG_TIMEOUT);
    }

    // ==================== POLLING METHODS ====================

    /**
     * Poll a condition with custom configuration
     *
     * @param condition Condition to poll
     * @param description Description for logging
     * @param timeout Maximum time to wait
     * @param pollInterval How often to check
     * @param ignoreExceptions Whether to ignore exceptions during polling
     * @return true if condition was met, false if timeout
     */
    public static boolean pollUntil(Callable<Boolean> condition, String description,
                                   Duration timeout, Duration pollInterval, boolean ignoreExceptions) {
        log.info("Polling for: {} (timeout: {}, poll interval: {}, ignore exceptions: {})",
                description, timeout, pollInterval, ignoreExceptions);

        try {
            ConditionFactory await = Awaitility.await(description)
                    .atMost(timeout)
                    .pollInterval(pollInterval);

            if (ignoreExceptions) {
                await = await.ignoreExceptions();
            }

            await.until(condition);

            log.info("✅ Polling successful: {}", description);
            return true;

        } catch (ConditionTimeoutException e) {
            log.warn("⏰ Polling timeout: {} after {}", description, timeout);
            return false;
        } catch (Exception e) {
            log.error("❌ Polling error: {} - {}", description, e.getMessage());
            return false;
        }
    }

    /**
     * Poll with exponential backoff
     *
     * @param condition Condition to poll
     * @param description Description for logging
     * @param timeout Maximum time to wait
     * @param initialInterval Initial poll interval
     * @param maxInterval Maximum poll interval
     * @return true if condition was met, false if timeout
     */
    public static boolean pollWithBackoff(Callable<Boolean> condition, String description,
                                         Duration timeout, Duration initialInterval, Duration maxInterval) {
        log.info("Polling with backoff: {} (timeout: {}, initial: {}, max: {})",
                description, timeout, initialInterval, maxInterval);

        try {
            Awaitility.await(description)
                    .atMost(timeout)
                    .pollInterval(initialInterval)
                    .pollInSameThread()
                    .until(condition);

            log.info("✅ Backoff polling successful: {}", description);
            return true;

        } catch (ConditionTimeoutException e) {
            log.warn("⏰ Backoff polling timeout: {} after {}", description, timeout);
            return false;
        } catch (Exception e) {
            log.error("❌ Backoff polling error: {} - {}", description, e.getMessage());
            return false;
        }
    }

    // ==================== UTILITY METHODS ====================

    /**
     * Wait for a specific amount of time with logging
     *
     * @param duration Duration to wait
     * @param reason Reason for waiting (for logging)
     */
    public static void waitFor(Duration duration, String reason) {
        log.info("Waiting for {} - Reason: {}", duration, reason);
        sleep(duration);
        log.debug("Wait completed: {}", reason);
    }

    /**
     * Wait until a condition is false (opposite of waitUntil)
     *
     * @param condition Condition that should become false
     * @param description Description for logging
     * @param timeout Maximum time to wait
     * @return true if condition became false, false if timeout
     */
    public static boolean waitUntilNot(Callable<Boolean> condition, String description, Duration timeout) {
        return waitUntil(() -> !condition.call(), "NOT " + description, timeout);
    }

    /**
     * Wait until a condition is false with default timeout
     *
     * @param condition Condition that should become false
     * @param description Description for logging
     * @return true if condition became false, false if timeout
     */
    public static boolean waitUntilNot(Callable<Boolean> condition, String description) {
        return waitUntilNot(condition, description, DEFAULT_TIMEOUT);
    }

    // ==================== CONFIGURATION METHODS ====================

    /**
     * Get default timeout duration
     *
     * @return Default timeout
     */
    public static Duration getDefaultTimeout() {
        return DEFAULT_TIMEOUT;
    }

    /**
     * Get default poll interval
     *
     * @return Default poll interval
     */
    public static Duration getDefaultPollInterval() {
        return DEFAULT_POLL_INTERVAL;
    }

    /**
     * Create a custom condition factory with specific settings
     *
     * @param timeout Maximum time to wait
     * @param pollInterval How often to check
     * @param description Description for the condition
     * @return Configured ConditionFactory
     */
    public static ConditionFactory createConditionFactory(Duration timeout, Duration pollInterval, String description) {
        return Awaitility.await(description)
                .atMost(timeout)
                .pollInterval(pollInterval);
    }

    // ==================== API RESPONSE WAITING METHODS ====================

    /**
     * Wait for an API response to return a specific status code
     *
     * @param apiCall Supplier that makes the API call and returns status code
     * @param expectedStatusCode Expected status code
     * @param description Description of the API call
     * @return true if expected status code received, false if timeout
     */
    public static boolean waitForApiResponse(Supplier<Integer> apiCall, int expectedStatusCode, String description) {
        return waitUntil(() -> {
            try {
                Integer statusCode = apiCall.get();
                return statusCode != null && statusCode == expectedStatusCode;
            } catch (Exception e) {
                log.debug("API call failed during wait: {}", e.getMessage());
                return false;
            }
        }, String.format("API %s to return status %d", description, expectedStatusCode));
    }

    /**
     * Wait for an API response to be successful (2xx status codes)
     *
     * @param apiCall Supplier that makes the API call and returns status code
     * @param description Description of the API call
     * @return true if successful status code received, false if timeout
     */
    public static boolean waitForApiSuccess(Supplier<Integer> apiCall, String description) {
        return waitUntil(() -> {
            try {
                Integer statusCode = apiCall.get();
                return statusCode != null && statusCode >= 200 && statusCode < 300;
            } catch (Exception e) {
                log.debug("API call failed during wait: {}", e.getMessage());
                return false;
            }
        }, String.format("API %s to return successful status", description));
    }

    // ==================== DATABASE WAITING METHODS ====================

    /**
     * Wait for a database record to exist
     *
     * @param recordChecker Supplier that checks if record exists
     * @param description Description of the record being checked
     * @return true if record exists, false if timeout
     */
    public static boolean waitForDatabaseRecord(Supplier<Boolean> recordChecker, String description) {
        return waitUntil(recordChecker::get, String.format("Database record: %s", description), LONG_TIMEOUT);
    }

    /**
     * Wait for a database record to be updated
     *
     * @param valueChecker Supplier that returns the current value
     * @param expectedValue Expected value after update
     * @param description Description of the field being checked
     * @param <T> Type of the value
     * @return true if value matches expected, false if timeout
     */
    public static <T> boolean waitForDatabaseUpdate(Supplier<T> valueChecker, T expectedValue, String description) {
        return waitUntil(() -> {
            try {
                T currentValue = valueChecker.get();
                return expectedValue.equals(currentValue);
            } catch (Exception e) {
                log.debug("Database check failed during wait: {}", e.getMessage());
                return false;
            }
        }, String.format("Database field %s to be updated to %s", description, expectedValue), LONG_TIMEOUT);
    }

    // ==================== FILE SYSTEM WAITING METHODS ====================

    /**
     * Wait for a file to exist
     *
     * @param filePath Path to the file
     * @return true if file exists, false if timeout
     */
    public static boolean waitForFileToExist(String filePath) {
        return waitUntil(() -> new java.io.File(filePath).exists(),
                String.format("File to exist: %s", filePath));
    }

    /**
     * Wait for a file to be deleted
     *
     * @param filePath Path to the file
     * @return true if file is deleted, false if timeout
     */
    public static boolean waitForFileToBeDeleted(String filePath) {
        return waitUntilNot(() -> new java.io.File(filePath).exists(),
                String.format("File to be deleted: %s", filePath));
    }

    /**
     * Wait for a file to reach a specific size
     *
     * @param filePath Path to the file
     * @param expectedSize Expected file size in bytes
     * @return true if file reaches expected size, false if timeout
     */
    public static boolean waitForFileSize(String filePath, long expectedSize) {
        return waitUntil(() -> {
            java.io.File file = new java.io.File(filePath);
            return file.exists() && file.length() == expectedSize;
        }, String.format("File %s to reach size %d bytes", filePath, expectedSize));
    }

    // ==================== RETRY METHODS ====================

    /**
     * Retry an operation until it succeeds or timeout
     *
     * @param operation Operation to retry
     * @param description Description of the operation
     * @param maxAttempts Maximum number of attempts
     * @param delayBetweenAttempts Delay between attempts
     * @return true if operation succeeded, false if all attempts failed
     */
    public static boolean retryOperation(Callable<Boolean> operation, String description,
                                       int maxAttempts, Duration delayBetweenAttempts) {
        log.info("Retrying operation: {} (max attempts: {}, delay: {})", description, maxAttempts, delayBetweenAttempts);

        for (int attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                log.debug("Attempt {}/{} for: {}", attempt, maxAttempts, description);

                if (operation.call()) {
                    log.info("✅ Operation succeeded on attempt {}: {}", attempt, description);
                    return true;
                }

                if (attempt < maxAttempts) {
                    log.debug("Attempt {} failed, waiting {} before retry", attempt, delayBetweenAttempts);
                    sleep(delayBetweenAttempts);
                }

            } catch (Exception e) {
                log.warn("Attempt {} failed with exception: {} - {}", attempt, description, e.getMessage());

                if (attempt < maxAttempts) {
                    sleep(delayBetweenAttempts);
                }
            }
        }

        log.error("❌ Operation failed after {} attempts: {}", maxAttempts, description);
        return false;
    }

    /**
     * Retry an operation with exponential backoff
     *
     * @param operation Operation to retry
     * @param description Description of the operation
     * @param maxAttempts Maximum number of attempts
     * @param initialDelay Initial delay between attempts
     * @param maxDelay Maximum delay between attempts
     * @return true if operation succeeded, false if all attempts failed
     */
    public static boolean retryWithBackoff(Callable<Boolean> operation, String description,
                                         int maxAttempts, Duration initialDelay, Duration maxDelay) {
        log.info("Retrying with backoff: {} (max attempts: {}, initial delay: {}, max delay: {})",
                description, maxAttempts, initialDelay, maxDelay);

        Duration currentDelay = initialDelay;

        for (int attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                log.debug("Attempt {}/{} for: {}", attempt, maxAttempts, description);

                if (operation.call()) {
                    log.info("✅ Operation succeeded on attempt {}: {}", attempt, description);
                    return true;
                }

                if (attempt < maxAttempts) {
                    log.debug("Attempt {} failed, waiting {} before retry", attempt, currentDelay);
                    sleep(currentDelay);

                    // Exponential backoff: double the delay, but don't exceed maxDelay
                    currentDelay = Duration.ofMillis(Math.min(currentDelay.toMillis() * 2, maxDelay.toMillis()));
                }

            } catch (Exception e) {
                log.warn("Attempt {} failed with exception: {} - {}", attempt, description, e.getMessage());

                if (attempt < maxAttempts) {
                    sleep(currentDelay);
                    currentDelay = Duration.ofMillis(Math.min(currentDelay.toMillis() * 2, maxDelay.toMillis()));
                }
            }
        }

        log.error("❌ Operation failed after {} attempts with backoff: {}", maxAttempts, description);
        return false;
    }
}

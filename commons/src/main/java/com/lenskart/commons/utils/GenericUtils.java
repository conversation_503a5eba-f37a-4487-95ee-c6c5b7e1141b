package com.lenskart.commons.utils;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.json.JSONObject;
import oshi.SystemInfo;
import oshi.hardware.ComputerSystem;
import oshi.hardware.HardwareAbstractionLayer;
import oshi.software.os.OperatingSystem;

import java.util.Map;

@Slf4j
public class GenericUtils {

    public static String getSerialNumber() {
        SystemInfo systemInfo = new SystemInfo();
        OperatingSystem operatingSystem = systemInfo.getOperatingSystem();
        log.info("operatingSystem: {}", operatingSystem);
        HardwareAbstractionLayer hardwareAbstractionLayer = systemInfo.getHardware();
        ComputerSystem computerSystem = hardwareAbstractionLayer.getComputerSystem();
        log.info("SerialNumber: {}", computerSystem.getSerialNumber());
        return computerSystem.getSerialNumber();
    }

    public static String genrateRandomNumericString(int num) {
        return RandomStringUtils.randomNumeric(num);
    }

    public static String genrateRandomAlphabeticString(int num) {
        return RandomStringUtils.randomAlphabetic(num);
    }

    /**
     * Builds a curl command string for a REST API request with an object payload
     *
     * @param method   HTTP method (GET, POST, PUT, DELETE)
     * @param url   URL of the API
     * @param queryParams   List of query parameters (can be null)
     * @param headers  List of headers (can be null)
     * @param payload  Request body as an object (can be null)
     */

    public static String curlBuilder(String method,String url,Map<String, String> headers, Map<String, Object> queryParams, Object payload) {
        StringBuilder curl = new StringBuilder("curl -X "+ method+" ");
        if (url != null)
            curl.append("'").append(url).append("'");
        if (queryParams != null) {
            queryParams.forEach((key, value) -> curl.append("?").append(key).append(": ").append(value).append("'"));
        }
        if (headers != null)
            headers.forEach((key, value) -> curl.append(" -H '").append(key).append(": ").append(value).append("'"));

        if (payload != null) {
            curl.append(" -H 'Content-Type: application/json'");
            curl.append(" -d '").append(payload.toString().replace("=", ": ")).append("'");
        }
        return curl.toString();
    }

    /**
     * Logs a curl command for the given request parameters
     *
     * @param method      HTTP method (GET, POST, PUT, DELETE)
     * @param url         Request URL
     * @param headers     Map of headers to include in the request (can be null)
     * @param queryParams Map of query parameters to include in the request (can be null)
     * @param payload     Request body (can be null)
     */
    public static void logCurl(String method, String url, Map<String, String> headers, Map<String, Object> queryParams, Object payload) {
        try {
            String curlCommand = curlBuilder(method, url, headers, queryParams, payload);
            log.info("Curl Command: {}", curlCommand);

        } catch (Exception e) {
           log.info("Failed to log curl command: {}", e.getMessage());
        }
    }

}

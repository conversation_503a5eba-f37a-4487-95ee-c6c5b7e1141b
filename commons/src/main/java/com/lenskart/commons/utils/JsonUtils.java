package com.lenskart.commons.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.testng.Assert;

public class JsonUtils {

    public static <T> T parseJsonString(String json, Class<T> clazz) {
        if (json.isEmpty()) {
            return null;
        } else {
            try {
                ObjectMapper objectMapper = new ObjectMapper();
                return (T) objectMapper.readValue(json, clazz);
            } catch (JsonProcessingException e) {
                Assert.fail(String.format("Failed to parse JSON string to object: %s", e.getMessage()));
                return null;
            }
        }
    }

    public static <T> T parseObject(Object json, Class<T> clazz) {
        if (json == null) {
            return null;
        }

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.convertValue(json, clazz);
        } catch (IllegalArgumentException e) {
            Assert.fail(String.format("Failed to parse object to class %s: %s", clazz.getSimpleName(), e.getMessage()));
            return null;
        }
    }

    public static String printJson(Object object) {
        try {
            return new ObjectMapper().writeValueAsString(object);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }


}
package com.lenskart.commons.utils;


import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import oshi.SystemInfo;
import oshi.hardware.ComputerSystem;
import oshi.hardware.HardwareAbstractionLayer;
import oshi.software.os.OperatingSystem;

@Slf4j
public class GenricUtils {

    public static String getSerialNumber() {
        SystemInfo systemInfo = new SystemInfo();
        OperatingSystem operatingSystem = systemInfo.getOperatingSystem();
        log.info("operatingSystem: {}", operatingSystem);
        HardwareAbstractionLayer hardwareAbstractionLayer = systemInfo.getHardware();
        ComputerSystem computerSystem = hardwareAbstractionLayer.getComputerSystem();
        log.info("SerialNumber: {}", computerSystem.getSerialNumber());
        return computerSystem.getSerialNumber();
    }

    public static String genrateRandomNumericString(int num) {
        return RandomStringUtils.randomNumeric(num);
    }
}

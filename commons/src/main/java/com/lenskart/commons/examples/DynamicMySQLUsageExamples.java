package com.lenskart.commons.examples;

import com.lenskart.commons.database.mysql.DynamicMySQLConnectionManager;
import com.lenskart.commons.database.mysql.DynamicQueryExecutor;
import com.lenskart.commons.database.mysql.ResultSetMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

/**
 * Examples demonstrating how to use the new Dynamic MySQL Multi-Cluster system
 */
@Slf4j
public class DynamicMySQLUsageExamples {

    /**
     * Example 1: Simple query execution across different clusters
     */
    public static void basicQueryExamples() {
        log.info("=== Example 1: Basic Query Execution ===");
        
        try {
            // Query orders from production cluster
            List<Map<String, Object>> orders = DynamicQueryExecutor.executeQuery(
                "prod_cluster", 
                "orders", 
                "SELECT order_id, customer_id, status, created_at FROM orders WHERE status = 'PENDING' LIMIT 10"
            );
            log.info("Found {} pending orders", orders.size());
            
            // Query analytics from analytics cluster
            Map<String, Object> salesSummary = DynamicQueryExecutor.executeQuerySingle(
                "analytics_cluster", 
                "sales_analytics", 
                "SELECT COUNT(*) as total_orders, SUM(amount) as total_revenue FROM daily_sales WHERE date = CURDATE()"
            );
            log.info("Today's sales: {} orders, {} revenue", 
                    salesSummary.get("total_orders"), salesSummary.get("total_revenue"));
            
            // Query inventory from warehouse cluster
            Object lowStockCount = DynamicQueryExecutor.executeScalar(
                "warehouse_cluster", 
                "inventory", 
                "SELECT COUNT(*) FROM products WHERE stock_quantity < 10"
            );
            log.info("Products with low stock: {}", lowStockCount);
            
        } catch (SQLException e) {
            log.error("Error in basic query examples: {}", e.getMessage());
        }
    }

    /**
     * Example 2: Parameterized queries for security
     */
    public static void parameterizedQueryExamples() {
        log.info("=== Example 2: Parameterized Queries ===");
        
        try {
            String customerId = "CUST12345";
            String status = "SHIPPED";
            
            // Get customer orders with parameters
            List<Map<String, Object>> customerOrders = DynamicQueryExecutor.executeQuery(
                "prod_cluster", 
                "orders", 
                "SELECT order_id, status, total_amount, created_at FROM orders WHERE customer_id = ? AND status = ? ORDER BY created_at DESC",
                customerId, status
            );
            log.info("Customer {} has {} {} orders", customerId, customerOrders.size(), status);
            
            // Get user profile with parameters
            Map<String, Object> userProfile = DynamicQueryExecutor.executeQuerySingle(
                "user_cluster", 
                "user_profiles", 
                "SELECT user_id, email, first_name, last_name, created_at FROM users WHERE user_id = ?",
                customerId
            );
            if (userProfile != null) {
                log.info("User profile: {} {} ({})", 
                        userProfile.get("first_name"), userProfile.get("last_name"), userProfile.get("email"));
            }
            
            // Check product availability with parameters
            String productId = "PROD67890";
            Object stockQuantity = DynamicQueryExecutor.executeScalar(
                "warehouse_cluster", 
                "inventory", 
                "SELECT stock_quantity FROM products WHERE product_id = ?",
                productId
            );
            log.info("Product {} stock: {}", productId, stockQuantity);
            
        } catch (SQLException e) {
            log.error("Error in parameterized query examples: {}", e.getMessage());
        }
    }

    /**
     * Example 3: Update operations across clusters
     */
    public static void updateOperationExamples() {
        log.info("=== Example 3: Update Operations ===");
        
        try {
            String orderId = "ORD12345";
            String newStatus = "PROCESSING";
            
            // Update order status in production cluster
            int updatedRows = DynamicQueryExecutor.executeUpdate(
                "prod_cluster", 
                "orders", 
                "UPDATE orders SET status = ?, updated_at = NOW() WHERE order_id = ?",
                newStatus, orderId
            );
            log.info("Updated {} order(s) to status {}", updatedRows, newStatus);
            
            // Update inventory in warehouse cluster
            String productId = "PROD67890";
            int quantityReduction = 5;
            int inventoryUpdated = DynamicQueryExecutor.executeUpdate(
                "warehouse_cluster", 
                "inventory", 
                "UPDATE products SET stock_quantity = stock_quantity - ?, updated_at = NOW() WHERE product_id = ? AND stock_quantity >= ?",
                quantityReduction, productId, quantityReduction
            );
            log.info("Updated inventory for product {}: {} rows affected", productId, inventoryUpdated);
            
            // Insert audit log in analytics cluster
            int auditInserted = DynamicQueryExecutor.executeUpdate(
                "analytics_cluster", 
                "audit_logs", 
                "INSERT INTO audit_logs (action, entity_type, entity_id, details, created_at) VALUES (?, ?, ?, ?, NOW())",
                "ORDER_STATUS_UPDATE", "ORDER", orderId, "Status changed to " + newStatus
            );
            log.info("Inserted {} audit log entry", auditInserted);
            
        } catch (SQLException e) {
            log.error("Error in update operation examples: {}", e.getMessage());
        }
    }

    /**
     * Example 4: Cross-cluster data aggregation
     */
    public static void crossClusterAggregationExample() {
        log.info("=== Example 4: Cross-Cluster Data Aggregation ===");
        
        try {
            // Get order summary from production
            List<Map<String, Object>> orderSummary = DynamicQueryExecutor.executeQuery(
                "prod_cluster", 
                "orders", 
                "SELECT status, COUNT(*) as count, SUM(total_amount) as total FROM orders WHERE DATE(created_at) = CURDATE() GROUP BY status"
            );
            
            // Get inventory summary from warehouse
            List<Map<String, Object>> inventorySummary = DynamicQueryExecutor.executeQuery(
                "warehouse_cluster", 
                "inventory", 
                "SELECT category, COUNT(*) as product_count, SUM(stock_quantity) as total_stock FROM products GROUP BY category"
            );
            
            // Get user activity from user cluster
            Object activeUsers = DynamicQueryExecutor.executeScalar(
                "user_cluster", 
                "user_activity", 
                "SELECT COUNT(DISTINCT user_id) FROM user_sessions WHERE DATE(created_at) = CURDATE()"
            );
            
            // Combine data for dashboard
            log.info("=== Daily Dashboard ===");
            log.info("Active Users Today: {}", activeUsers);
            
            log.info("Order Summary:");
            for (Map<String, Object> row : orderSummary) {
                log.info("  {}: {} orders, ${} total", 
                        row.get("status"), row.get("count"), row.get("total"));
            }
            
            log.info("Inventory Summary:");
            for (Map<String, Object> row : inventorySummary) {
                log.info("  {}: {} products, {} total stock", 
                        row.get("category"), row.get("product_count"), row.get("total_stock"));
            }
            
        } catch (SQLException e) {
            log.error("Error in cross-cluster aggregation example: {}", e.getMessage());
        }
    }

    /**
     * Example 5: Transaction handling within a cluster
     */
    public static void transactionExample() {
        log.info("=== Example 5: Transaction Handling ===");
        
        try {
            String orderId = "ORD12345";
            String productId = "PROD67890";
            int quantity = 2;
            
            // Prepare transaction queries for warehouse operations
            List<String> warehouseQueries = List.of(
                "UPDATE products SET stock_quantity = stock_quantity - " + quantity + " WHERE product_id = '" + productId + "'",
                "INSERT INTO stock_movements (product_id, movement_type, quantity, order_id, created_at) VALUES ('" + productId + "', 'SALE', -" + quantity + ", '" + orderId + "', NOW())",
                "UPDATE product_reservations SET status = 'FULFILLED' WHERE order_id = '" + orderId + "' AND product_id = '" + productId + "'"
            );
            
            // Execute transaction in warehouse cluster
            int[] results = DynamicQueryExecutor.executeTransaction(
                "warehouse_cluster", 
                "inventory", 
                warehouseQueries
            );
            
            log.info("Transaction completed successfully:");
            log.info("  Stock updated: {} rows", results[0]);
            log.info("  Movement logged: {} rows", results[1]);
            log.info("  Reservation fulfilled: {} rows", results[2]);
            
        } catch (SQLException e) {
            log.error("Error in transaction example: {}", e.getMessage());
        }
    }

    /**
     * Example 6: Connection testing and health checks
     */
    public static void healthCheckExample() {
        log.info("=== Example 6: Health Checks ===");
        
        // Test all cluster connections
        String[] clusters = {"prod_cluster", "analytics_cluster", "warehouse_cluster", "user_cluster", "legacy_cluster"};
        String[] databases = {"orders", "reports", "inventory", "users", "legacy_data"};
        
        for (int i = 0; i < clusters.length; i++) {
            String cluster = clusters[i];
            String database = databases[i];
            
            boolean isHealthy = DynamicQueryExecutor.testConnection(cluster, database);
            log.info("Health check - {}/{}: {}", cluster, database, isHealthy ? "✅ HEALTHY" : "❌ UNHEALTHY");
        }
    }

    /**
     * Example 7: Legacy data migration scenario
     */
    public static void legacyMigrationExample() {
        log.info("=== Example 7: Legacy Data Migration ===");
        
        try {
            // Read data from legacy cluster
            List<Map<String, Object>> legacyCustomers = DynamicQueryExecutor.executeQuery(
                "legacy_cluster", 
                "legacy_customers", 
                "SELECT customer_id, name, email, phone FROM customers WHERE migrated = 0 LIMIT 100"
            );
            
            log.info("Found {} legacy customers to migrate", legacyCustomers.size());
            
            // Migrate each customer to new user cluster
            for (Map<String, Object> customer : legacyCustomers) {
                String customerId = (String) customer.get("customer_id");
                String name = (String) customer.get("name");
                String email = (String) customer.get("email");
                String phone = (String) customer.get("phone");
                
                // Insert into new user cluster
                int inserted = DynamicQueryExecutor.executeUpdate(
                    "user_cluster", 
                    "users", 
                    "INSERT INTO users (legacy_customer_id, full_name, email, phone, created_at, migration_date) VALUES (?, ?, ?, ?, NOW(), NOW())",
                    customerId, name, email, phone
                );
                
                if (inserted > 0) {
                    // Mark as migrated in legacy system
                    DynamicQueryExecutor.executeUpdate(
                        "legacy_cluster", 
                        "legacy_customers", 
                        "UPDATE customers SET migrated = 1, migration_date = NOW() WHERE customer_id = ?",
                        customerId
                    );
                    
                    log.debug("Migrated customer: {} ({})", name, customerId);
                }
            }
            
            log.info("Migration batch completed");
            
        } catch (SQLException e) {
            log.error("Error in legacy migration example: {}", e.getMessage());
        }
    }


    @Data
    @AllArgsConstructor
    public static class User {
        private Long id;
        private String username;
        private String email;

        /**
         * ResultSet mapper for User objects
         */
        public static final ResultSetMapper<User> MAPPER = rs -> new User(
                rs.getLong("id"),
                rs.getString("username"),
                rs.getString("email")
        );
    }

    public static List<User> getUsers() {
        return DynamicQueryExecutor.executeQuery(
                "user_cluster",
                "test_user",
                "SELECT id, username, email FROM users",
                User.MAPPER
        );
    }



    /**
     * Main method to run all examples
     */
    public static void main(String[] args) {
        log.info("🚀 Running Dynamic MySQL Usage Examples");
        
        // Note: These examples will fail in test environment without actual database servers
        // but demonstrate the API usage patterns
        
        basicQueryExamples();
        parameterizedQueryExamples();
        updateOperationExamples();
        crossClusterAggregationExample();
        transactionExample();
        healthCheckExample();
        legacyMigrationExample();
        getUsers();
        
        log.info("✅ Dynamic MySQL Usage Examples completed");
    }
}

package com.lenskart.commons.examples;

import com.lenskart.commons.loader.ConfigRegistry;
import com.lenskart.commons.config.DatabaseConfig;
import com.lenskart.commons.loader.DatabaseConfigLoader;
import com.lenskart.commons.config.MongoDBConfig;
import com.lenskart.commons.loader.MongoDBConfigLoader;
import com.lenskart.commons.config.SSHConfig;
import com.lenskart.commons.loader.SSHConfigLoader;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * Example class demonstrating how to use the unified configuration system
 */
@Slf4j
public class ConfigExample {

    /**
     * Example of getting database configurations
     */
    public static void printDatabaseConfigs() {
        // Using the ConfigRegistry directly
        DatabaseConfig defaultConfig = ConfigRegistry.getInstance().getDatabaseConfig("default");
        if (defaultConfig != null) {
            log.info("Default Database URL (Registry): {}", defaultConfig.getUrl());
        }
        
        // Using the DatabaseConfigLoader facade
        DatabaseConfig userServiceConfig = DatabaseConfigLoader.loadConfig("userservice");
        log.info("User Service Database URL (Loader): {}", userServiceConfig.getUrl());
        
        // Getting all database configurations
        Map<String, DatabaseConfig> allConfigs = ConfigRegistry.getInstance().getAllDatabaseConfigs();
        log.info("Number of database configurations: {}", allConfigs.size());
    }
    
    /**
     * Example of getting MongoDB configurations
     */
    public static void printMongoDBConfigs() {
        // Using the ConfigRegistry directly
        MongoDBConfig userDbConfig = ConfigRegistry.getInstance().getMongoDBConfig("userdb");
        if (userDbConfig != null) {
            log.info("User MongoDB URI (Registry): {}", userDbConfig.getUri());
        }
        
        // Using the MongoDBConfigLoader facade
        MongoDBConfig inventoryDbConfig = MongoDBConfigLoader.loadConfig("inventorydb");
        log.info("Inventory MongoDB URI (Loader): {}", inventoryDbConfig.getUri());
        
        // Getting all MongoDB configurations
        Map<String, MongoDBConfig> allConfigs = ConfigRegistry.getInstance().getAllMongoDBConfigs();
        log.info("Number of MongoDB configurations: {}", allConfigs.size());
    }
    
    /**
     * Example of getting SSH configuration
     */
    public static void printSSHConfig() {
        // Using the ConfigRegistry directly
        SSHConfig sshConfig = ConfigRegistry.getInstance().getSSHConfig();
        if (sshConfig != null) {
            log.info("SSH Hostname (Registry): {}", sshConfig.getHostname());
            log.info("SSH Tunneling Enabled (Registry): {}", sshConfig.isEnabled());
        }
        
        // Using the SSHConfigLoader facade
        SSHConfig sshConfig2 = SSHConfigLoader.loadConfig();
        if (sshConfig2 != null) {
            log.info("SSH Hostname (Loader): {}", sshConfig2.getHostname());
            log.info("SSH Tunneling Enabled (Loader): {}", sshConfig2.isEnabled());
        }
        
        // Checking if SSH tunneling is enabled
        boolean enabled = ConfigRegistry.getInstance().isSSHTunnelingEnabled();
        log.info("SSH Tunneling Enabled (Registry Method): {}", enabled);
        
        boolean enabled2 = SSHConfigLoader.isSSHTunnelingEnabled();
        log.info("SSH Tunneling Enabled (Loader Method): {}", enabled2);
    }
    
    /**
     * Example of getting base URLs
     */
    public static void printBaseUrls() {
        // Using the ConfigRegistry directly
        String sessionServiceUrl = ConfigRegistry.getInstance().getBaseUrl("sessionService");
        if (sessionServiceUrl != null) {
            log.info("Session Service Base URL (Registry): {}", sessionServiceUrl);
        }
        
        // Getting all base URLs
        Map<String, String> allBaseUrls = ConfigRegistry.getInstance().getAllBaseUrls();
        log.info("Number of base URLs: {}", allBaseUrls.size());
    }
    
    /**
     * Example of refreshing configurations
     */
    public static void refreshConfigs() {
        log.info("Refreshing all configurations...");
        ConfigRegistry.getInstance().refresh();
        log.info("All configurations refreshed");
    }
    
    /**
     * Main method to run the examples
     */
    public static void main(String[] args) {
        printDatabaseConfigs();
        printMongoDBConfigs();
        printSSHConfig();
        printBaseUrls();
        refreshConfigs();
    }
}

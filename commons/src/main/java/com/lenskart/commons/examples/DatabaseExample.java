package com.lenskart.commons.examples;

import com.lenskart.commons.database.mysql.QueryExecutor;
import com.lenskart.commons.database.mysql.ResultSetMapper;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;

/**
 * Example class demonstrating how to use the database utilities
 */
@Slf4j
public class DatabaseExample {
    
    /**
     * Example entity class
     */
    @Data
    @AllArgsConstructor
    public static class User {
        private Long id;
        private String username;
        private String email;
        
        /**
         * ResultSet mapper for User objects
         */
        public static final ResultSetMapper<User> MAPPER = rs -> new User(
                rs.getLong("id"),
                rs.getString("username"),
                rs.getString("email")
        );
    }
    
    /**
     * Example of querying users with a custom mapper
     * 
     * @param dbName    Database name
     * @return          List of User objects
     */
    public static List<User> getUsers(String dbName) {
        return QueryExecutor.executeQuery(
                dbName,
                "SELECT id, username, email FROM users",
                User.MAPPER
        );
    }
    
    /**
     * Example of querying users with a status filter
     * 
     * @param dbName    Database name
     * @param status    Status to filter by
     * @return          List of User objects
     */
    public static List<User> getUsersByStatus(String dbName, String status) {
        return QueryExecutor.executeQuery(
                dbName,
                "SELECT id, username, email FROM users WHERE status = ?",
                User.MAPPER,
                status
        );
    }
    
    /**
     * Example of querying data as a map
     * 
     * @param dbName    Database name
     * @param userId    User ID to query
     * @return          Map of user data
     */
    public static Map<String, Object> getUserAsMap(String dbName, long userId) {
        List<Map<String, Object>> results = QueryExecutor.executeQueryAsMap(
                dbName,
                "SELECT * FROM users WHERE id = ?",
                userId
        );
        
        return results.isEmpty() ? null : results.get(0);
    }
    
    /**
     * Example of inserting a user
     * 
     * @param dbName    Database name
     * @param username  Username to insert
     * @param email     Email to insert
     * @return          Generated user ID
     */
    public static Long insertUser(String dbName, String username, String email) {
        List<Object> keys = QueryExecutor.executeInsertWithGeneratedKeys(
                dbName,
                "INSERT INTO users (username, email, status) VALUES (?, ?, 'active')",
                username,
                email
        );
        
        return keys.isEmpty() ? null : (Long) keys.get(0);
    }
    
    /**
     * Example of updating a user
     * 
     * @param dbName    Database name
     * @param userId    User ID to update
     * @param email     New email value
     * @return          Number of rows updated
     */
    public static int updateUserEmail(String dbName, long userId, String email) {
        return QueryExecutor.executeUpdate(
                dbName,
                "UPDATE users SET email = ? WHERE id = ?",
                email,
                userId
        );
    }
}
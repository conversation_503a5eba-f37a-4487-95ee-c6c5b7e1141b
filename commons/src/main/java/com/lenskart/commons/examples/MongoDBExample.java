package com.lenskart.commons.examples;

import com.lenskart.commons.database.mongodb.MongoDBQueryExecutor;
import com.mongodb.client.model.Filters;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.types.ObjectId;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Example class demonstrating how to use the MongoDB utilities
 */
@Slf4j
public class MongoDBExample {
    
    /**
     * Example of finding all users
     * 
     * @param dbName Database name
     * @return List of user documents
     */
    public static List<Document> getAllUsers(String dbName) {
        return MongoDBQueryExecutor.findAll(dbName, "users");
    }
    
    /**
     * Example of finding a user by ID
     * 
     * @param dbName Database name
     * @param userId User ID
     * @return User document
     */
    public static Document getUserById(String dbName, String userId) {
        return MongoDBQueryExecutor.findById(dbName, "users", userId);
    }
    
    /**
     * Example of finding users by name
     * 
     * @param dbName Database name
     * @param name User name
     * @return List of user documents
     */
    public static List<Document> getUsersByName(String dbName, String name) {
        return MongoDBQueryExecutor.find(dbName, "users", Filters.eq("name", name));
    }
    
    /**
     * Example of creating a new user
     * 
     * @param dbName Database name
     * @param name User name
     * @param email User email
     * @param age User age
     * @return ID of the created user
     */
    public static String createUser(String dbName, String name, String email, int age) {
        Document user = new Document()
                .append("name", name)
                .append("email", email)
                .append("age", age)
                .append("createdAt", System.currentTimeMillis());
        
        return MongoDBQueryExecutor.insertOne(dbName, "users", user);
    }
    
    /**
     * Example of updating a user
     * 
     * @param dbName Database name
     * @param userId User ID
     * @param name New name
     * @param email New email
     * @return Number of documents updated
     */
    public static long updateUser(String dbName, String userId, String name, String email) {
        Map<String, Object> updates = new HashMap<>();
        updates.put("name", name);
        updates.put("email", email);
        updates.put("updatedAt", System.currentTimeMillis());
        
        return MongoDBQueryExecutor.updateOne(dbName, "users", 
                Filters.eq("_id", new ObjectId(userId)), updates, false);
    }
    
    /**
     * Example of deleting a user
     * 
     * @param dbName Database name
     * @param userId User ID
     * @return Number of documents deleted
     */
    public static long deleteUser(String dbName, String userId) {
        return MongoDBQueryExecutor.deleteOne(dbName, "users", 
                Filters.eq("_id", new ObjectId(userId)));
    }
    
    /**
     * Example of counting users
     * 
     * @param dbName Database name
     * @return Number of users
     */
    public static long countUsers(String dbName) {
        return MongoDBQueryExecutor.count(dbName, "users", null);
    }
    
    /**
     * Example of creating multiple users
     * 
     * @param dbName Database name
     * @return Number of users created
     */
    public static int createMultipleUsers(String dbName) {
        List<Document> users = Arrays.asList(
                new Document()
                        .append("name", "John Doe")
                        .append("email", "<EMAIL>")
                        .append("age", 30)
                        .append("createdAt", System.currentTimeMillis()),
                new Document()
                        .append("name", "Jane Smith")
                        .append("email", "<EMAIL>")
                        .append("age", 25)
                        .append("createdAt", System.currentTimeMillis()),
                new Document()
                        .append("name", "Bob Johnson")
                        .append("email", "<EMAIL>")
                        .append("age", 40)
                        .append("createdAt", System.currentTimeMillis())
        );
        
        return MongoDBQueryExecutor.insertMany(dbName, "users", users);
    }
}

package com.lenskart.commons.endpoints;

import com.lenskart.commons.config.UnifiedConfigRegistry;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.Map;

/**
 * Comprehensive test suite for the endpoint refactoring implementation.
 * Tests all aspects of the new unified endpoint management system.
 */
@Slf4j
public class EndpointRefactoringTest {
    
    private EndpointRegistry endpointRegistry;
    private UnifiedConfigRegistry configRegistry;
    
    @BeforeClass
    public void setup() {
        log.info("=== Setting up Endpoint Refactoring Test Suite ===");
        
        // Initialize registries
        configRegistry = UnifiedConfigRegistry.getInstance();
        endpointRegistry = EndpointRegistry.getInstance();
        endpointRegistry.initialize();
        
        log.info("Test setup completed");
    }
    
    @Test
    public void testUnifiedConfigRegistry() {
        log.info("=== Testing Unified Config Registry ===");
        
        // Test basic functionality
        assert configRegistry.isInitialized() : "Config registry should be initialized";
        
        // Test statistics
        Map<String, Object> stats = configRegistry.getStatistics();
        log.info("Config registry statistics: {}", stats);
        
        // Test base URL retrieval
        String sessionServiceUrl = configRegistry.getBaseUrl("sessionService");
        log.info("Session service URL: {}", sessionServiceUrl);
        
        // Test all base URLs
        Map<String, String> allUrls = configRegistry.getAllBaseUrls();
        log.info("Total base URLs loaded: {}", allUrls.size());
        
        for (Map.Entry<String, String> entry : allUrls.entrySet()) {
            log.info("  {}: {}", entry.getKey(), entry.getValue());
        }
        
        log.info("✅ Unified Config Registry test completed successfully\n");
    }
    
    @Test
    public void testEndpointRegistry() {
        log.info("=== Testing Endpoint Registry ===");
        
        // Test initialization
        assert endpointRegistry.isInitialized() : "Endpoint registry should be initialized";
        
        // Test module count
        int moduleCount = endpointRegistry.getModuleCount();
        log.info("Registered modules: {}", moduleCount);
        assert moduleCount > 0 : "Should have at least one module registered";
        
        // Test statistics
        Map<String, Object> stats = endpointRegistry.getStatistics();
        log.info("Endpoint registry statistics: {}", stats);
        
        // Test individual endpoint managers
        Map<String, EndpointManager<?>> managers = endpointRegistry.getAllEndpointManagers();
        for (Map.Entry<String, EndpointManager<?>> entry : managers.entrySet()) {
            String module = entry.getKey();
            EndpointManager<?> manager = entry.getValue();
            
            log.info("Testing module: {}", module);
            assert manager != null : "Manager should not be null for module: " + module;
            assert manager.isInitialized() : "Manager should be initialized for module: " + module;
            
            // Test URL generation
            Map<String, String> urls = manager.getAllUrls();
            log.info("  Module {} has {} endpoints", module, urls.size());
            
            for (Map.Entry<String, String> urlEntry : urls.entrySet()) {
                log.info("    {}: {}", urlEntry.getKey(), urlEntry.getValue());
                assert urlEntry.getValue() != null : "URL should not be null for endpoint: " + urlEntry.getKey();
                assert urlEntry.getValue().startsWith("http") : "URL should be valid for endpoint: " + urlEntry.getKey();
            }
        }
        
        log.info("✅ Endpoint Registry test completed successfully\n");
    }
    
    @Test
    public void testJunoEndpoints() {
        log.info("=== Testing Juno Endpoints ===");
        
        try {
            // Test using reflection to avoid compile-time dependencies
            Class<?> junoEndpointsClass = Class.forName("com.lenskart.juno.endpoints.JunoEndpoints");
            Object[] endpoints = (Object[]) junoEndpointsClass.getMethod("values").invoke(null);
            
            log.info("Found {} Juno endpoints", endpoints.length);
            
            for (Object endpoint : endpoints) {
                String name = endpoint.toString();
                String url = (String) endpoint.getClass().getMethod("getUrl").invoke(endpoint);
                
                log.info("  {}: {}", name, url);
                assert url != null : "URL should not be null for endpoint: " + name;
                assert url.startsWith("http") : "URL should be valid for endpoint: " + name;
            }
            
            log.info("✅ Juno Endpoints test completed successfully\n");
            
        } catch (ClassNotFoundException e) {
            log.warn("Juno module not available for testing");
        } catch (Exception e) {
            log.error("Error testing Juno endpoints: {}", e.getMessage(), e);
            throw new RuntimeException("Juno endpoints test failed", e);
        }
    }
    
    @Test
    public void testPosEndpoints() {
        log.info("=== Testing POS Endpoints ===");
        
        try {
            // Test using reflection to avoid compile-time dependencies
            Class<?> posEndpointsClass = Class.forName("com.lenskart.pos.endpoints.PosEndpoints");
            Object[] endpoints = (Object[]) posEndpointsClass.getMethod("values").invoke(null);
            
            log.info("Found {} POS endpoints", endpoints.length);
            
            for (Object endpoint : endpoints) {
                String name = endpoint.toString();
                String url = (String) endpoint.getClass().getMethod("getUrl").invoke(endpoint);
                
                log.info("  {}: {}", name, url);
                assert url != null : "URL should not be null for endpoint: " + name;
                assert url.startsWith("http") : "URL should be valid for endpoint: " + name;
            }
            
            log.info("✅ POS Endpoints test completed successfully\n");
            
        } catch (ClassNotFoundException e) {
            log.warn("POS module not available for testing");
        } catch (Exception e) {
            log.error("Error testing POS endpoints: {}", e.getMessage(), e);
            throw new RuntimeException("POS endpoints test failed", e);
        }
    }
    
    @Test
    public void testNexsEndpoints() {
        log.info("=== Testing NEXS Endpoints ===");
        
        try {
            // Test using reflection to avoid compile-time dependencies
            Class<?> nexsEndpointsClass = Class.forName("com.lenskart.nexs.endpoints.NexsOrderProcessingEndpoints");
            Object[] endpoints = (Object[]) nexsEndpointsClass.getMethod("values").invoke(null);
            
            log.info("Found {} NEXS endpoints", endpoints.length);
            
            for (Object endpoint : endpoints) {
                String name = endpoint.toString();
                String url = (String) endpoint.getClass().getMethod("getUrl").invoke(endpoint);
                
                log.info("  {}: {}", name, url);
                assert url != null : "URL should not be null for endpoint: " + name;
                assert url.startsWith("http") : "URL should be valid for endpoint: " + name;
            }
            
            log.info("✅ NEXS Endpoints test completed successfully\n");
            
        } catch (ClassNotFoundException e) {
            log.warn("NEXS module not available for testing");
        } catch (Exception e) {
            log.error("Error testing NEXS endpoints: {}", e.getMessage(), e);
            throw new RuntimeException("NEXS endpoints test failed", e);
        }
    }
    
    @Test
    public void testPathParameterReplacement() {
        log.info("=== Testing Path Parameter Replacement ===");
        
        try {
            // Test Juno endpoints with path parameters
            Class<?> junoEndpointsClass = Class.forName("com.lenskart.juno.endpoints.JunoEndpoints");
            Object[] endpoints = (Object[]) junoEndpointsClass.getMethod("values").invoke(null);
            
            for (Object endpoint : endpoints) {
                String name = endpoint.toString();
                String endpointPath = (String) endpoint.getClass().getMethod("getEndpoint").invoke(endpoint);
                
                if (endpointPath.contains("{$")) {
                    log.info("Testing path parameter replacement for: {}", name);
                    
                    // Create test parameters
                    Map<String, String> params = Map.of(
                        "productID", "12345",
                        "orderID", "67890",
                        "itemId", "11111"
                    );
                    
                    String urlWithParams = (String) endpoint.getClass()
                        .getMethod("getUrl", Map.class)
                        .invoke(endpoint, params);
                    
                    log.info("  Original: {}", endpointPath);
                    log.info("  With params: {}", urlWithParams);
                    
                    // Verify parameters were replaced
                    assert !urlWithParams.contains("{$") : "Parameters should be replaced in URL";
                    assert urlWithParams.startsWith("http") : "URL should be valid";
                }
            }
            
            log.info("✅ Path Parameter Replacement test completed successfully\n");
            
        } catch (Exception e) {
            log.error("Error testing path parameter replacement: {}", e.getMessage(), e);
            throw new RuntimeException("Path parameter replacement test failed", e);
        }
    }
    
    @Test
    public void testValidationAndRefresh() {
        log.info("=== Testing Validation and Refresh ===");
        
        // Test validation
        Map<String, Boolean> validationResults = endpointRegistry.validateAll();
        log.info("Validation results: {}", validationResults);
        
        for (Map.Entry<String, Boolean> entry : validationResults.entrySet()) {
            assert entry.getValue() : "Validation should pass for module: " + entry.getKey();
        }
        
        // Test refresh
        endpointRegistry.refreshAll();
        log.info("Refresh completed successfully");
        
        // Test validation after refresh
        Map<String, Boolean> validationAfterRefresh = endpointRegistry.validateAll();
        log.info("Validation after refresh: {}", validationAfterRefresh);
        
        for (Map.Entry<String, Boolean> entry : validationAfterRefresh.entrySet()) {
            assert entry.getValue() : "Validation should pass after refresh for module: " + entry.getKey();
        }
        
        log.info("✅ Validation and Refresh test completed successfully\n");
    }
    
    @Test
    public void testPerformance() {
        log.info("=== Testing Performance ===");
        
        long startTime = System.currentTimeMillis();
        
        // Test URL generation performance
        for (int i = 0; i < 1000; i++) {
            Map<String, EndpointManager<?>> managers = endpointRegistry.getAllEndpointManagers();
            for (EndpointManager<?> manager : managers.values()) {
                manager.getAllUrls();
            }
        }
        
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        log.info("Performance test completed in {} ms", duration);
        log.info("Average time per iteration: {} ms", duration / 1000.0);
        
        // Performance should be reasonable (less than 10ms per iteration)
        assert duration < 10000 : "Performance should be reasonable";
        
        log.info("✅ Performance test completed successfully\n");
    }
}

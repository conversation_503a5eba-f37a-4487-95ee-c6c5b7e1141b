package com.lenskart.commons.database;

import com.lenskart.commons.config.MySQLMultiClusterConfig;
import com.lenskart.commons.database.mysql.DynamicMySQLConnectionManager;
import com.lenskart.commons.database.mysql.DynamicQueryExecutor;
import com.lenskart.commons.loader.ConfigRegistry;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.Map;
import java.util.Set;

/**
 * Test class for Dynamic MySQL Multi-Cluster functionality
 */
@Slf4j
public class DynamicMySQLTest {

    private ConfigRegistry configRegistry;
    private MySQLMultiClusterConfig multiClusterConfig;

    @BeforeClass
    public void setup() {
        log.info("=== Setting up Dynamic MySQL Multi-Cluster Test ===");
        configRegistry = ConfigRegistry.getInstance();
        multiClusterConfig = configRegistry.getMySQLMultiClusterConfig();
    }

    @Test
    public void testMultiClusterConfigLoading() {
        log.info("=== Testing Multi-Cluster Configuration Loading ===");
        
        assert multiClusterConfig != null : "Multi-cluster configuration should be loaded";
        
        Set<String> clusters = multiClusterConfig.getConfiguredClusters();
        log.info("Configured clusters: {}", clusters);
        
        assert clusters.contains("prod_cluster") : "Should contain prod_cluster";
        assert clusters.contains("analytics_cluster") : "Should contain analytics_cluster";
        assert clusters.contains("warehouse_cluster") : "Should contain warehouse_cluster";
        assert clusters.contains("user_cluster") : "Should contain user_cluster";
        assert clusters.contains("legacy_cluster") : "Should contain legacy_cluster";
        
        assert clusters.size() == 5 : "Should have exactly 5 clusters";
        
        log.info("✅ Multi-cluster configuration loaded successfully");
    }

    @Test
    public void testClusterSpecificConfigurations() {
        log.info("=== Testing Cluster-Specific Configurations ===");
        
        // Test prod_cluster
        MySQLMultiClusterConfig.ClusterConfig prodCluster = multiClusterConfig.getClusterConfig("prod_cluster");
        assert "mysql-prod.preprod.internal".equals(prodCluster.getHost()) : "Prod cluster host should match";
        assert "prod_user".equals(prodCluster.getUsername()) : "Prod cluster username should match";
        assert prodCluster.getMaxPoolSize() == 20 : "Prod cluster pool size should be 20";
        
        log.info("Prod Cluster - Host: {}, Username: {}, Pool Size: {}", 
                prodCluster.getHost(), prodCluster.getUsername(), prodCluster.getMaxPoolSize());
        
        // Test analytics_cluster
        MySQLMultiClusterConfig.ClusterConfig analyticsCluster = multiClusterConfig.getClusterConfig("analytics_cluster");
        assert "mysql-analytics.preprod.internal".equals(analyticsCluster.getHost()) : "Analytics cluster host should match";
        assert "analytics_reader".equals(analyticsCluster.getUsername()) : "Analytics cluster username should match";
        assert analyticsCluster.getMaxPoolSize() == 10 : "Analytics cluster pool size should be 10";
        assert analyticsCluster.getAdditionalParams() != null : "Analytics cluster should have additional params";
        assert "true".equals(analyticsCluster.getAdditionalParams().get("useCompression")) : "Should have compression enabled";
        
        log.info("Analytics Cluster - Host: {}, Username: {}, Pool Size: {}, Additional Params: {}", 
                analyticsCluster.getHost(), analyticsCluster.getUsername(), 
                analyticsCluster.getMaxPoolSize(), analyticsCluster.getAdditionalParams());
        
        // Test warehouse_cluster
        MySQLMultiClusterConfig.ClusterConfig warehouseCluster = multiClusterConfig.getClusterConfig("warehouse_cluster");
        assert "mysql-warehouse.preprod.internal".equals(warehouseCluster.getHost()) : "Warehouse cluster host should match";
        assert "warehouse_user".equals(warehouseCluster.getUsername()) : "Warehouse cluster username should match";
        assert warehouseCluster.getMaxPoolSize() == 15 : "Warehouse cluster pool size should be 15";
        
        log.info("Warehouse Cluster - Host: {}, Username: {}, Pool Size: {}", 
                warehouseCluster.getHost(), warehouseCluster.getUsername(), warehouseCluster.getMaxPoolSize());
        
        log.info("✅ Cluster-specific configurations working correctly");
    }

    @Test
    public void testDynamicUrlGeneration() {
        log.info("=== Testing Dynamic URL Generation ===");
        
        // Test URL generation for different cluster/database combinations
        String prodOrdersUrl = multiClusterConfig.createJdbcUrl("prod_cluster", "orders");
        String analyticsReportsUrl = multiClusterConfig.createJdbcUrl("analytics_cluster", "reports");
        String warehouseInventoryUrl = multiClusterConfig.createJdbcUrl("warehouse_cluster", "inventory");
        String userProfilesUrl = multiClusterConfig.createJdbcUrl("user_cluster", "user_profiles");
        String legacyDataUrl = multiClusterConfig.createJdbcUrl("legacy_cluster", "legacy_data");
        
        log.info("Generated URLs:");
        log.info("  Prod Orders: {}", prodOrdersUrl);
        log.info("  Analytics Reports: {}", analyticsReportsUrl);
        log.info("  Warehouse Inventory: {}", warehouseInventoryUrl);
        log.info("  User Profiles: {}", userProfilesUrl);
        log.info("  Legacy Data: {}", legacyDataUrl);
        
        // Verify URL structure
        assert prodOrdersUrl.contains("mysql-prod.preprod.internal:3306/orders") : "Prod URL should be correct";
        assert analyticsReportsUrl.contains("mysql-analytics.preprod.internal:3306/reports") : "Analytics URL should be correct";
        assert warehouseInventoryUrl.contains("mysql-warehouse.preprod.internal:3306/inventory") : "Warehouse URL should be correct";
        assert userProfilesUrl.contains("mysql-users.preprod.internal:3306/user_profiles") : "User URL should be correct";
        assert legacyDataUrl.contains("mysql-legacy.preprod.internal:3306/legacy_data") : "Legacy URL should be correct";
        
        // Verify additional parameters in analytics URL
        assert analyticsReportsUrl.contains("useCompression=true") : "Analytics URL should have compression";
        assert analyticsReportsUrl.contains("cachePrepStmts=true") : "Analytics URL should have cache params";
        
        log.info("✅ Dynamic URL generation working correctly");
    }

    @Test
    public void testDynamicConnectionManager() {
        log.info("=== Testing Dynamic Connection Manager ===");
        
        try {
            // Test getting DataSources for different cluster/database combinations
            javax.sql.DataSource prodOrdersDS = DynamicMySQLConnectionManager.getDataSource("prod_cluster", "orders");
            javax.sql.DataSource analyticsReportsDS = DynamicMySQLConnectionManager.getDataSource("analytics_cluster", "reports");
            javax.sql.DataSource warehouseInventoryDS = DynamicMySQLConnectionManager.getDataSource("warehouse_cluster", "inventory");
            
            assert prodOrdersDS != null : "Prod orders DataSource should not be null";
            assert analyticsReportsDS != null : "Analytics reports DataSource should not be null";
            assert warehouseInventoryDS != null : "Warehouse inventory DataSource should not be null";
            
            // Test getting the same DataSource again (should be cached)
            javax.sql.DataSource prodOrdersDS2 = DynamicMySQLConnectionManager.getDataSource("prod_cluster", "orders");
            assert prodOrdersDS == prodOrdersDS2 : "DataSource should be cached and return same instance";
            
            // Get statistics
            Map<String, Object> stats = DynamicMySQLConnectionManager.getStatistics();
            log.info("Connection Manager Statistics: {}", stats);
            
            assert (Integer) stats.get("activeDataSources") >= 3 : "Should have at least 3 active DataSources";
            
            log.info("✅ Dynamic Connection Manager working correctly");
            
        } catch (Exception e) {
            log.warn("Connection Manager test failed (expected in test environment): {}", e.getMessage());
            // This is expected to fail in test environment without actual database servers
        }
    }

    @Test
    public void testQueryExecutorAPI() {
        log.info("=== Testing Query Executor API ===");
        
        try {
            // Test connection validation (will fail but API should work)
            boolean prodConnection = DynamicQueryExecutor.testConnection("prod_cluster", "orders");
            boolean analyticsConnection = DynamicQueryExecutor.testConnection("analytics_cluster", "reports");
            
            log.info("Connection test results:");
            log.info("  Prod cluster orders: {}", prodConnection);
            log.info("  Analytics cluster reports: {}", analyticsConnection);
            
            // These will fail in test environment, but the API should work
            log.info("✅ Query Executor API working correctly (connections expected to fail in test env)");
            
        } catch (Exception e) {
            log.info("Query Executor test completed (expected failures in test environment): {}", e.getMessage());
        }
    }

    @Test
    public void testErrorHandling() {
        log.info("=== Testing Error Handling ===");
        
        try {
            // Test accessing non-existent cluster
            multiClusterConfig.getClusterConfig("non_existent_cluster");
            assert false : "Should throw exception for non-existent cluster";
        } catch (IllegalArgumentException e) {
            log.info("✅ Correctly threw exception for non-existent cluster: {}", e.getMessage());
            assert e.getMessage().contains("Available clusters:") : "Error message should list available clusters";
        }
        
        try {
            // Test creating URL for non-existent cluster
            multiClusterConfig.createJdbcUrl("non_existent_cluster", "some_db");
            assert false : "Should throw exception for non-existent cluster";
        } catch (IllegalArgumentException e) {
            log.info("✅ Correctly threw exception for URL creation with non-existent cluster: {}", e.getMessage());
        }
        
        // Test checking if cluster is configured
        assert !multiClusterConfig.isClusterConfigured("non_existent_cluster") : 
            "Should return false for non-existent cluster";
        assert multiClusterConfig.isClusterConfigured("prod_cluster") : 
            "Should return true for existing cluster";
        
        log.info("✅ Error handling working correctly");
    }

    @Test
    public void testConfigurationValidation() {
        log.info("=== Testing Configuration Validation ===");
        
        // Test cluster validation
        boolean isValid = multiClusterConfig.validateClusters();
        assert isValid : "Cluster configuration should be valid";
        
        // Test statistics
        Map<String, Object> stats = multiClusterConfig.getStatistics();
        log.info("Multi-cluster statistics: {}", stats);
        
        assert stats.containsKey("totalClusters") : "Statistics should contain total clusters";
        assert stats.containsKey("clusterNames") : "Statistics should contain cluster names";
        assert stats.containsKey("clusterDetails") : "Statistics should contain cluster details";
        
        assert (Integer) stats.get("totalClusters") == 5 : "Should have 5 total clusters";
        
        log.info("✅ Configuration validation working correctly");
    }

    @Test
    public void testDynamicUsageScenarios() {
        log.info("=== Testing Dynamic Usage Scenarios ===");
        
        // Scenario 1: E-commerce order processing
        log.info("Scenario 1: E-commerce Order Processing");
        String orderUrl = multiClusterConfig.createJdbcUrl("prod_cluster", "orders");
        String inventoryUrl = multiClusterConfig.createJdbcUrl("warehouse_cluster", "inventory");
        String userUrl = multiClusterConfig.createJdbcUrl("user_cluster", "users");
        
        log.info("  Orders DB: {}", orderUrl);
        log.info("  Inventory DB: {}", inventoryUrl);
        log.info("  Users DB: {}", userUrl);
        
        // Scenario 2: Analytics and reporting
        log.info("Scenario 2: Analytics and Reporting");
        String salesAnalyticsUrl = multiClusterConfig.createJdbcUrl("analytics_cluster", "sales_analytics");
        String customerAnalyticsUrl = multiClusterConfig.createJdbcUrl("analytics_cluster", "customer_analytics");
        
        log.info("  Sales Analytics: {}", salesAnalyticsUrl);
        log.info("  Customer Analytics: {}", customerAnalyticsUrl);
        
        // Scenario 3: Legacy data migration
        log.info("Scenario 3: Legacy Data Migration");
        String legacyOrdersUrl = multiClusterConfig.createJdbcUrl("legacy_cluster", "legacy_orders");
        String legacyCustomersUrl = multiClusterConfig.createJdbcUrl("legacy_cluster", "legacy_customers");
        
        log.info("  Legacy Orders: {}", legacyOrdersUrl);
        log.info("  Legacy Customers: {}", legacyCustomersUrl);
        
        // All URLs should be properly formed
        assert orderUrl.startsWith("jdbc:mysql://") : "Order URL should be valid JDBC URL";
        assert inventoryUrl.startsWith("jdbc:mysql://") : "Inventory URL should be valid JDBC URL";
        assert userUrl.startsWith("jdbc:mysql://") : "User URL should be valid JDBC URL";
        assert salesAnalyticsUrl.startsWith("jdbc:mysql://") : "Sales analytics URL should be valid JDBC URL";
        assert legacyOrdersUrl.startsWith("jdbc:mysql://") : "Legacy orders URL should be valid JDBC URL";
        
        log.info("✅ Dynamic usage scenarios working correctly");
    }

    @Test
    public void testConfigurationRefresh() {
        log.info("=== Testing Configuration Refresh ===");
        
        // Get initial configuration
        MySQLMultiClusterConfig initialConfig = configRegistry.getMySQLMultiClusterConfig();
        assert initialConfig != null : "Initial config should not be null";
        
        int initialClusterCount = initialConfig.getClusterCount();
        log.info("Initial cluster count: {}", initialClusterCount);
        
        // Refresh configuration
        configRegistry.refresh();
        
        // Get refreshed configuration
        MySQLMultiClusterConfig refreshedConfig = configRegistry.getMySQLMultiClusterConfig();
        assert refreshedConfig != null : "Refreshed config should not be null";
        
        int refreshedClusterCount = refreshedConfig.getClusterCount();
        log.info("Refreshed cluster count: {}", refreshedClusterCount);
        
        // Verify configuration is still working
        assert refreshedClusterCount == initialClusterCount : "Cluster count should be preserved after refresh";
        assert refreshedConfig.isClusterConfigured("prod_cluster") : "Prod cluster should still be configured after refresh";
        
        log.info("✅ Configuration refresh working correctly");
    }
}

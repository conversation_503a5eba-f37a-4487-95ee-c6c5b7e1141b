package com.lenskart.commons.database;

import com.lenskart.commons.config.DatabaseConfig;
import com.lenskart.commons.config.MySQLClusterConfig;
import com.lenskart.commons.database.mysql.DatabaseConnectionManager;
import com.lenskart.commons.loader.ConfigRegistry;
import lombok.extern.slf4j.Slf4j;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import javax.sql.DataSource;
import java.util.Map;

/**
 * Test class for MySQL cluster configuration functionality
 */
@Slf4j
public class MySQLClusterConfigTest {

    private ConfigRegistry configRegistry;
    private MySQLClusterConfig clusterConfig;

    @BeforeClass
    public void setup() {
        log.info("=== Setting up MySQL Cluster Configuration Test ===");
        configRegistry = ConfigRegistry.getInstance();
        clusterConfig = configRegistry.getMySQLClusterConfig();
    }

    @Test
    public void testMySQLClusterConfigLoading() {
        log.info("=== Testing MySQL Cluster Configuration Loading ===");
        
        assert clusterConfig != null : "MySQL cluster configuration should be loaded";
        
        log.info("Cluster Host: {}", clusterConfig.getClusterHost());
        log.info("Cluster Port: {}", clusterConfig.getClusterPort());
        log.info("Default Username: {}", clusterConfig.getDefaultUsername());
        log.info("Driver: {}", clusterConfig.getDriverClassName());
        log.info("Max Pool Size: {}", clusterConfig.getMaxPoolSize());
        
        assert "mysql-cluster.preprod.internal".equals(clusterConfig.getClusterHost()) : 
            "Cluster host should match configuration";
        assert clusterConfig.getClusterPort() == 3306 : "Cluster port should be 3306";
        assert "cluster_user".equals(clusterConfig.getDefaultUsername()) : 
            "Default username should match configuration";
        
        log.info("✅ MySQL cluster configuration loaded successfully");
    }

    @Test
    public void testDatabaseSpecificConfigurations() {
        log.info("=== Testing Database-Specific Configurations ===");
        
        // Test nexs database (uses cluster defaults)
        assert clusterConfig.isDatabaseConfigured("nexs") : "nexs database should be configured";
        
        String nexsUrl = clusterConfig.createJdbcUrl("nexs");
        log.info("NEXS URL: {}", nexsUrl);
        assert nexsUrl.contains("mysql-cluster.preprod.internal:3306/wms") : 
            "NEXS URL should use cluster host and correct database name";
        
        String nexsUsername = clusterConfig.getUsername("nexs");
        assert "cluster_user".equals(nexsUsername) : "NEXS should use cluster username";
        
        // Test tracking_middleware database (overrides credentials and pool settings)
        assert clusterConfig.isDatabaseConfigured("tracking_middleware") : 
            "tracking_middleware database should be configured";
        
        String middlewareUsername = clusterConfig.getUsername("tracking_middleware");
        String middlewarePassword = clusterConfig.getPassword("tracking_middleware");
        int middlewareMaxPool = clusterConfig.getMaxPoolSize("tracking_middleware");
        
        log.info("Middleware Username: {}", middlewareUsername);
        log.info("Middleware Max Pool: {}", middlewareMaxPool);
        
        assert "user_middleware".equals(middlewareUsername) : 
            "Middleware should use overridden username";
        assert "test_123".equals(middlewarePassword) : 
            "Middleware should use overridden password";
        assert middlewareMaxPool == 30 : "Middleware should use overridden pool size";
        
        // Test analytics database (custom user and small pool)
        assert clusterConfig.isDatabaseConfigured("analytics") : 
            "analytics database should be configured";
        
        String analyticsUsername = clusterConfig.getUsername("analytics");
        int analyticsMaxPool = clusterConfig.getMaxPoolSize("analytics");
        int analyticsMinIdle = clusterConfig.getMinIdle("analytics");
        
        log.info("Analytics Username: {}", analyticsUsername);
        log.info("Analytics Max Pool: {}", analyticsMaxPool);
        log.info("Analytics Min Idle: {}", analyticsMinIdle);
        
        assert "analytics_reader".equals(analyticsUsername) : 
            "Analytics should use read-only username";
        assert analyticsMaxPool == 5 : "Analytics should use small pool size";
        assert analyticsMinIdle == 2 : "Analytics should use small min idle";
        
        log.info("✅ Database-specific configurations working correctly");
    }

    @Test
    public void testDatabaseConfigCreation() {
        log.info("=== Testing DatabaseConfig Creation ===");
        
        // Test creating DatabaseConfig for different databases
        String[] testDatabases = {"nexs", "tracking_middleware", "user_service", "inventory", "analytics"};
        
        for (String dbName : testDatabases) {
            log.info("Testing DatabaseConfig creation for: {}", dbName);
            
            DatabaseConfig dbConfig = clusterConfig.createDatabaseConfig(dbName);
            
            assert dbConfig != null : "DatabaseConfig should not be null for " + dbName;
            assert dbName.equals(dbConfig.getName()) : "Database name should match";
            assert dbConfig.getUrl() != null : "URL should not be null";
            assert dbConfig.getUsername() != null : "Username should not be null";
            assert dbConfig.getPassword() != null : "Password should not be null";
            assert dbConfig.getDriverClassName() != null : "Driver should not be null";
            
            log.info("  Name: {}", dbConfig.getName());
            log.info("  URL: {}", dbConfig.getUrl());
            log.info("  Username: {}", dbConfig.getUsername());
            log.info("  Max Pool Size: {}", dbConfig.getMaxPoolSize());
            
            // Verify URL format
            assert dbConfig.getUrl().startsWith("************************************************/") : 
                "URL should use cluster host and port";
        }
        
        log.info("✅ DatabaseConfig creation working correctly");
    }

    @Test
    public void testBackwardCompatibility() {
        log.info("=== Testing Backward Compatibility ===");
        
        // Test that individual database configurations still work
        DatabaseConfig legacyConfig = configRegistry.getDatabaseConfig("legacy_db");
        
        assert legacyConfig != null : "Legacy database configuration should be available";
        assert legacyConfig.getUrl().contains("legacy-host") : 
            "Legacy config should use individual host";
        
        log.info("Legacy DB URL: {}", legacyConfig.getUrl());
        log.info("Legacy DB Username: {}", legacyConfig.getUsername());
        
        log.info("✅ Backward compatibility maintained");
    }

    @Test
    public void testDatabaseConnectionManager() {
        log.info("=== Testing DatabaseConnectionManager Integration ===");
        
        try {
            // Test getting DataSource for cluster-managed databases
            DataSource nexsDataSource = DatabaseConnectionManager.getDataSource("nexs");
            assert nexsDataSource != null : "NEXS DataSource should not be null";
            
            DataSource middlewareDataSource = DatabaseConnectionManager.getDataSource("tracking_middleware");
            assert middlewareDataSource != null : "Middleware DataSource should not be null";
            
            DataSource analyticsDataSource = DatabaseConnectionManager.getDataSource("analytics");
            assert analyticsDataSource != null : "Analytics DataSource should not be null";
            
            // Test getting DataSource for legacy database
            DataSource legacyDataSource = DatabaseConnectionManager.getDataSource("legacy_db");
            assert legacyDataSource != null : "Legacy DataSource should not be null";
            
            log.info("✅ DatabaseConnectionManager integration working correctly");
            
        } catch (Exception e) {
            log.error("Error testing DatabaseConnectionManager: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Test
    public void testConfiguredDatabases() {
        log.info("=== Testing Configured Databases ===");
        
        java.util.Set<String> configuredDatabases = clusterConfig.getConfiguredDatabases();
        
        log.info("Configured databases: {}", configuredDatabases);
        
        assert configuredDatabases.contains("nexs") : "Should contain nexs database";
        assert configuredDatabases.contains("tracking_middleware") : "Should contain tracking_middleware database";
        assert configuredDatabases.contains("user_service") : "Should contain user_service database";
        assert configuredDatabases.contains("inventory") : "Should contain inventory database";
        assert configuredDatabases.contains("analytics") : "Should contain analytics database";
        
        assert configuredDatabases.size() == 5 : "Should have exactly 5 configured databases";
        
        log.info("✅ Configured databases test passed");
    }

    @Test
    public void testUrlGeneration() {
        log.info("=== Testing URL Generation ===");
        
        // Test URL generation for different databases
        String nexsUrl = clusterConfig.createJdbcUrl("nexs");
        String inventoryUrl = clusterConfig.createJdbcUrl("inventory");
        
        log.info("NEXS URL: {}", nexsUrl);
        log.info("Inventory URL: {}", inventoryUrl);
        
        // Verify base URL structure
        assert nexsUrl.equals("************************************************/wms?useSSL=false&serverTimezone=UTC") : 
            "NEXS URL should match expected format";
        
        // Verify inventory URL with additional parameters
        assert inventoryUrl.contains("**********************************************************") : 
            "Inventory URL should contain correct base";
        assert inventoryUrl.contains("useCompression=true") : 
            "Inventory URL should contain additional parameters";
        assert inventoryUrl.contains("cachePrepStmts=true") : 
            "Inventory URL should contain cache parameters";
        
        log.info("✅ URL generation working correctly");
    }

    @Test
    public void testConfigurationRefresh() {
        log.info("=== Testing Configuration Refresh ===");
        
        // Get initial configuration
        MySQLClusterConfig initialConfig = configRegistry.getMySQLClusterConfig();
        assert initialConfig != null : "Initial config should not be null";
        
        // Refresh configuration
        configRegistry.refresh();
        
        // Get refreshed configuration
        MySQLClusterConfig refreshedConfig = configRegistry.getMySQLClusterConfig();
        assert refreshedConfig != null : "Refreshed config should not be null";
        
        // Verify configuration is still working
        assert refreshedConfig.isDatabaseConfigured("nexs") : "NEXS should still be configured after refresh";
        assert refreshedConfig.getClusterHost().equals("mysql-cluster.preprod.internal") : 
            "Cluster host should be preserved after refresh";
        
        log.info("✅ Configuration refresh working correctly");
    }

    @Test
    public void testErrorHandling() {
        log.info("=== Testing Error Handling ===");
        
        try {
            // Test accessing non-configured database
            clusterConfig.createDatabaseConfig("non_existent_db");
            assert false : "Should throw exception for non-existent database";
        } catch (IllegalArgumentException e) {
            log.info("✅ Correctly threw exception for non-existent database: {}", e.getMessage());
        }
        
        // Test checking if database is configured
        assert !clusterConfig.isDatabaseConfigured("non_existent_db") : 
            "Should return false for non-existent database";
        
        log.info("✅ Error handling working correctly");
    }
}

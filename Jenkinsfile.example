@Library('jenkins/shared-functions.groovy') _

// Example <PERSON><PERSON><PERSON>pel<PERSON>
// This pipeline is specifically configured for the example module

def sharedFunctions

node {
    // Load shared functions
    sharedFunctions = load 'jenkins/shared-functions.groovy'
}

// Module-specific configuration
def moduleConfig = [
    moduleName: 'example',
    suiteXmlFile: 'src/test/resources/example-discovery.xml',
    defaultEnvironment: 'preprod',
    defaultTestCategory: 'REGRESSION'
]

// Execute the pipeline using shared functions
sharedFunctions.executeModulePipeline(moduleConfig)

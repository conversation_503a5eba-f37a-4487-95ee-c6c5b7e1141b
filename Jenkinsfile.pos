@Library('jenkins/shared-functions.groovy') _

// POS Module Jenkins Pipeline
// This pipeline is specifically configured for the pos module

def sharedFunctions

node {
    // Load shared functions
    sharedFunctions = load 'jenkins/shared-functions.groovy'
}

// Module-specific configuration
def moduleConfig = [
    moduleName: 'pos',
    suiteXmlFile: 'src/test/resources/pos-regression.xml',
    defaultEnvironment: 'preprod',
    defaultTestCategory: 'REGRESSION'
]

// Execute the pipeline using shared functions
sharedFunctions.executeModulePipeline(moduleConfig)

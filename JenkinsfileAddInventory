pipeline {
    agent any
     tools {
                maven 'Maven' // GoTO -> Global Tool Configuration -> <PERSON><PERSON> -> check for 'Maven' with the name of the Maven installation in Jenkins
                jdk 'JDK-21'
            }

    parameters {
        choice(name: 'ENVIRONMENT', choices: ['preprod', 'qe1'], description: 'Select the environment to run tests against')
        string(name: 'Quantity', defaultValue: '1', description: 'Add Quantity')
        string(name: 'pid', defaultValue: '', description: 'Product ID')
        string(name: 'location', defaultValue: 'test-1-1', description: 'Location')
        string(name: 'facility', defaultValue: 'QNXS2', description: 'Facility code')
        string(name: 'legalOwner', defaultValue: 'LKIN', description: 'Legal owner')
    }

    environment {
            // Set Maven options
            MAVEN_OPTS = '-Xmx512m -Xms256m -XX:MaxMetaspaceSize=256m -XX:+UseG1GC'
            // Set the environment property for tests
            TEST_ENV = "${params.ENVIRONMENT}"
        }

    stages {
        stage('Checkout') {
            steps {
                checkout scm
            }
        }

         stage('Build') {
                    steps {
                                echo "**************** Printing Java Version ****************"
                                sh 'pwd' // Prints workspace path
                                sh 'echo $JAVA_HOME'
                                sh 'java -version'
                                sh 'echo $MAVEN_OPTS'

                                // Build the project without running tests
                                sh "mvn clean install -DskipTests -s ${env.WORKSPACE}/settings.xml"

                    }
                }

        stage('Run IMS Stock Test Suite') {
            steps {
                script {
                    int times = params.Quantity.toInteger()

                    for (int i = 1; i <= times; i++) {
                        echo "🔁 Running iteration #${i} in environment: ${params.ENVIRONMENT}"

                        sh """
                            mvn test -pl nexs -Denvironment=${params.ENVIRONMENT} \
                            -Dsurefire.suiteXmlFiles=src/test/resources/nexs-addInventory.xml \
                            -Dpid=${params.pid} \
                            -Dlocation=${params.location} \
                            -Dfacility=${params.facility} \
                            -DlegalOwner=${params.legalOwner}
                        """
                    }
                }
            }
        }
    }
}
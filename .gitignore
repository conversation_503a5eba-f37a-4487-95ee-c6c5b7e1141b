# Maven
target/
!.mvn/wrapper/maven-wrapper.jar
!**/src/main/**/target/
!**/src/test/**/target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

### IntelliJ IDEA ###
.idea/
*.iws
*.iml
*.ipr
.idea_modules/
out/
.idea/sonarlint/

### Eclipse ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache

### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/
build/
!**/src/main/**/build/
!**/src/test/**/build/

### VS Code ###
.vscode/

### Mac OS ###
.DS_Store

### Test Reports ###
test-output/
allure-results/
allure-report/
#extent-reports/
reports/
*.html
.allure

### Logs ###
logs/
*.log

### Configuration Files ###
# Exclude property files that might contain sensitive information
*.properties
# But include templates
!*.properties.template
# Exclude environment-specific YAML files
src/main/resources/config/*/prod.yml
src/main/resources/config/*/dev.yml
src/main/resources/config/*/qa.yml
src/main/resources/config/*/staging.yml
# But include default configurations
!src/main/resources/config/*/default.yml

### Docker ###
.env
docker-compose.override.yml

### Jenkins ###
.jenkins/
workspace/

### Temporary Files ###
*.tmp
*.bak
*.swp
*~.nib
local.properties
.loadpath
.recommenders

### System Files ###
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
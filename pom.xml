<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.lenskart</groupId>
    <artifactId>be-automation</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <modules>
        <module>commons</module>
        <module>juno</module>
        <module>pos</module>
        <module>scm</module>
        <module>nexs</module>
        <module>cs</module>
        <module>cosmos</module>
        <module>example</module>
        <module>e2e</module>
    </modules>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <rest-assured.version>5.5.2</rest-assured.version>
        <jackson.version>2.15.2</jackson.version>
        <testng.version>7.8.0</testng.version>
        <lombok.version>1.18.30</lombok.version>
        <mysql.version>8.0.33</mysql.version>
        <hikaricp.version>5.0.1</hikaricp.version>
        <commons-dbcp2.version>2.9.0</commons-dbcp2.version>
        <extentreports.version>5.1.1</extentreports.version>
        <allure.version>2.24.0</allure.version>
        <allure-testng.version>2.24.0</allure-testng.version>
        <slf4j.version>2.0.9</slf4j.version>
        <logback.version>1.5.18</logback.version>
        <commons-io.version>2.15.0</commons-io.version>
        <aspectj.version>1.9.20.1</aspectj.version>
        <snakeyaml.version>2.0</snakeyaml.version>
        <jsch.version>0.1.55</jsch.version>
        <mongodb.version>4.11.1</mongodb.version>
        <json.version>20250107</json.version>
        <jedis.version>5.1.0</jedis.version>
        <elasticsearch.version>7.17.28</elasticsearch.version>
        <juno-schema.version>14.79</juno-schema.version>
        <oshi.version>6.6.0</oshi.version>
        <awaitility.version>4.3.0</awaitility.version>
    </properties>

    <build>
        <plugins>
            <!-- Allure Maven Plugin -->
            <plugin>
                <groupId>io.qameta.allure</groupId>
                <artifactId>allure-maven</artifactId>
                <version>2.12.0</version>
                <configuration>
                    <reportVersion>${allure.version}</reportVersion>
                </configuration>
            </plugin>

            <!-- Maven Surefire Plugin -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.2.2</version>
                <!-- Configuration for Surefire is defined in each module -->
                <configuration>
                    <!-- Empty configuration as each module will define its own suiteXmlFiles -->
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
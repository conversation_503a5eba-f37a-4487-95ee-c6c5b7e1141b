@Library('jenkins/shared-functions.groovy') _

// Cosmos Module Jenkins Pipeline
// This pipeline is specifically configured for the cosmos module

def sharedFunctions

node {
    // Load shared functions
    sharedFunctions = load 'jenkins/shared-functions.groovy'
}

// Module-specific configuration
def moduleConfig = [
    moduleName: 'cosmos',
    suiteXmlFile: 'src/test/resources/cosmos-regression.xml',
    defaultEnvironment: 'preprod',
    defaultTestCategory: 'REGRESSION'
]

// Execute the pipeline using shared functions
sharedFunctions.executeModulePipeline(moduleConfig)

pipeline {
    agent any

    // Define parameters for the pipeline
    parameters {
        choice(name: 'ENVIRONMENT', choices: ['preprod', 'prod'], description: 'Select the environment to run tests against')
        choice(name: 'SUITE_TYPE', choices: ['juno', 'scm', 'nexs', 'cs', 'e2e', 'pos', 'all'], description: 'Select the test suite to run')
        choice(name: 'TEST_CATEGORY', choices: ['SANITY', 'REGRESSION', 'E2E', 'ALL'], description: 'Select the test category to run')
    }

    // Define environment variables
    environment {
        // Set Maven options
        MAVEN_OPTS = '-Xmx1024m -XX:MaxPermSize=256m'
        // Set the environment property for tests
        TEST_ENV = "${params.ENVIRONMENT}"
    }

    stages {
        // Stage to checkout code from Git
        stage('Checkout') {
            steps {
                // Clean workspace before checkout
                cleanWs()

                // Checkout code from Git repository
                checkout scm

                // Print information about the build
                echo "Building for environment: ${params.ENVIRONMENT}"
                echo "Test suite: ${params.SUITE_TYPE}"
            }
        }

        // Stage to build the project without running tests
        stage('Build') {
            steps {
                // Build the project without running tests
                sh 'mvn clean package -DskipTests'
            }
        }

        // Stage to run tests based on selected parameters
        stage('Test') {
            steps {
                script {
                    // Set up test command based on selected suite type
                    def testCommand = 'mvn test'

                    // Add environment parameter
                    testCommand += " -Denvironment=${params.ENVIRONMENT}"

                    // Add module selection based on SUITE_TYPE parameter
                    if (params.SUITE_TYPE != 'all') {
                        testCommand += " -pl ${params.SUITE_TYPE}"
                    }

                    // Add test category parameter if not ALL
                    if (params.TEST_CATEGORY != 'ALL') {
                        testCommand += " -DtestCategory=${params.TEST_CATEGORY}"
                    }

                    // Add additional Maven parameters for test execution
                    testCommand += ' -Dmaven.test.failure.ignore=true'

                    // Execute the test command
                    sh testCommand
                }
            }
        }
    }

    post {
        always {
            // Archive test results
            junit '**/target/surefire-reports/*.xml'

            // Archive Allure results
            archiveArtifacts artifacts: 'allure-results/**', allowEmptyArchive: true

            // Generate Allure report if the plugin is installed
            script {
                try {
                    allure([
                        includeProperties: false,
                        jdk: '',
                        properties: [],
                        reportBuildPolicy: 'ALWAYS',
                        results: [[path: 'allure-results']]
                    ])
                } catch (Exception e) {
                    echo "Allure report generation failed: ${e.message}"
                }
            }

            // Archive test output directory
            archiveArtifacts artifacts: 'test-output/**', allowEmptyArchive: true

            // Archive logs directory
            archiveArtifacts artifacts: 'logs/**', allowEmptyArchive: true

            // Send email notification - to be implemented
            emailext (
                subject: "Build ${currentBuild.result}: Job '${env.JOB_NAME} [${env.BUILD_NUMBER}]'",
                body: """<p>Build Status: ${currentBuild.result}</p>
                        <p>Build URL: <a href='${env.BUILD_URL}'>${env.BUILD_URL}</a></p>
                        <p>Environment: ${params.ENVIRONMENT}</p>
                        <p>Suite Type: ${params.SUITE_TYPE}</p>""",
                recipientProviders: [[$class: 'DevelopersRecipientProvider'], [$class: 'RequesterRecipientProvider']]
            )
        }

        success {
            echo 'Build and tests completed successfully!'
        }

        failure {
            echo 'Build or tests failed!'
        }

        unstable {
            echo 'Build is unstable (some tests failed)!'
        }
    }
}

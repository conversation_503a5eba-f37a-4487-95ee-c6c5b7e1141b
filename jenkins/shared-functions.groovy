// Shared Jenkins Pipeline Functions
// This file contains reusable functions for module-specific Jenkins pipelines

def executeModulePipeline(moduleConfig) {
    def moduleName = moduleConfig.moduleName
    def suiteXmlFile = moduleConfig.suiteXmlFile
    def defaultEnvironment = moduleConfig.defaultEnvironment ?: 'preprod'
    def defaultTestCategory = moduleConfig.defaultTestCategory ?: 'REGRESSION'
    
    pipeline {
        agent any
        tools {
            maven 'Maven' // GoTO -> Global Tool Configuration -> Maven -> check for 'Maven' with the name of the Maven installation in Jenkins
            jdk 'JDK-21'
        }
        
        parameters {
            choice(
                name: 'ENVIRONMENT',
                choices: ['preprod', 'prod'],
                description: "Environment to run tests against (default: ${defaultEnvironment})"
            )
            choice(
                name: 'TEST_CATEGORY', 
                choices: ['SANITY', 'REGRESSION', 'E2E'],
                description: "Test category to execute (default: ${defaultTestCategory})"
            )
        }
        
        environment {
            MAVEN_OPTS = '-Xmx512m -Xms256m -XX:MaxMetaspaceSize=256m -XX:+UseG1GC'
//            JAVA_HOME = '/usr/lib/jvm/java-11-openjdk'
        }
        
        stages {
            stage('Checkout') {
                steps {
                    script {
                        echo "🚀 Starting ${moduleName} module pipeline"
                        echo "📋 Module: ${moduleName}"
                        echo "🧪 Suite File: ${suiteXmlFile}"
                        echo "🌍 Environment: ${params.ENVIRONMENT ?: defaultEnvironment}"
                        echo "📊 Test Category: ${params.TEST_CATEGORY ?: defaultTestCategory}"
                    }
                    
                    checkout scm
                    
                    script {
                        // Set build display name
                        def environment = params.ENVIRONMENT ?: defaultEnvironment
                        def testCategory = params.TEST_CATEGORY ?: defaultTestCategory
                        currentBuild.displayName = "#${env.BUILD_NUMBER} - ${moduleName} - ${environment}"
                        currentBuild.description = "Module: ${moduleName} | Environment: ${environment} | Category: ${testCategory}"
                    }
                }
            }
            
            stage('Build') {
                steps {
                    script {
                        echo "🔨 Building ${moduleName} module..."
                    }

                    sh "mvn clean install -DskipTests -s ${env.WORKSPACE}/settings.xml"
                    
                    script {
                        echo "✅ Build completed successfully for ${moduleName}"
                    }
                }
            }
            
            stage('Test') {
                steps {
                    script {
                        def environment = params.ENVIRONMENT ?: defaultEnvironment
                        def testCategory = params.TEST_CATEGORY ?: defaultTestCategory
                        
                        echo "🧪 Running tests for ${moduleName} module..."
                        echo "📄 Using suite file: ${suiteXmlFile}"
                        echo "🌍 Environment: ${environment}"
                        echo "📊 Test Category: ${testCategory}"
                    }
                    
                    script {
                        def environment = params.ENVIRONMENT ?: defaultEnvironment
                        def testCategory = params.TEST_CATEGORY ?: defaultTestCategory
                        
                        try {
                            sh """
                                mvn test -pl ${moduleName} \\
                                    -Dsurefire.suiteXmlFiles=${suiteXmlFile} \\
                                    -Denvironment=${environment} \\
                                    -DtestCategory=${testCategory} \\
                                    -DskipTests=false \\
                                    -Dmaven.test.failure.ignore=true
                            """
                        } catch (Exception e) {
                            echo "⚠️ Test execution completed with issues: ${e.message}"
                            currentBuild.result = 'UNSTABLE'
                        }
                    }
                }
            }
        }
        
        post {
            always {
                script {
                    processTestResults(moduleName, suiteXmlFile, 
                                     params.ENVIRONMENT ?: defaultEnvironment, 
                                     params.TEST_CATEGORY ?: defaultTestCategory)
                }
            }
            
            failure {
                script {
                    echo "Build or tests failed for ${moduleName}!"
                    sendFailureNotification(moduleName, params.ENVIRONMENT ?: defaultEnvironment)
                }
            }
            
            success {
                echo "✅ Build and tests completed successfully for ${moduleName}!"
            }
        }
    }
}

def processTestResults(moduleName, suiteXmlFile, environment, testCategory) {
    echo "📊 Processing test results for ${moduleName}..."
    
    // Archive test results only if they exist and are recent
    def testReportsExist = sh(script: "find . -name '*.xml' -path '*/target/surefire-reports/*' -newer Jenkinsfile* | head -1", returnStdout: true).trim()
    
    if (testReportsExist) {
        try {
            junit '**/target/surefire-reports/*.xml'
        } catch (Exception e) {
            echo "Failed to parse test results: ${e.message}"
        }
    } else {
        echo "No recent test results found - tests may not have run due to build failure"
    }
    
    // Archive test output and logs
    def testOutputPath = "${moduleName}/test-output/**"
    archiveArtifacts artifacts: testOutputPath, allowEmptyArchive: true
    
    // Archive centralized log files at root level
    archiveArtifacts artifacts: 'automation.log', allowEmptyArchive: true, fingerprint: false
    archiveArtifacts artifacts: 'http-requests.log', allowEmptyArchive: true, fingerprint: false
    
    // Collect test statistics and send email
    collectTestStatsAndSendEmail(moduleName, suiteXmlFile, environment, testCategory)
}

def collectTestStatsAndSendEmail(moduleName, suiteXmlFile, environment, testCategory) {
    def totalTests = 0
    def passedTests = 0
    def failedTests = 0
    def skippedTests = 0
    def testDuration = currentBuild.duration ?: 0
    def testsExecuted = false

    // Parse test results from JUnit XML files
    try {
        def testResultAction = currentBuild.rawBuild.getAction(hudson.tasks.junit.TestResultAction.class)
        if (testResultAction != null) {
            totalTests = testResultAction.totalCount
            passedTests = testResultAction.totalCount - testResultAction.failCount - testResultAction.skipCount
            failedTests = testResultAction.failCount
            skippedTests = testResultAction.skipCount
            testsExecuted = true
            echo "Total tests count: ${totalTests}"
            echo "Passed tests count: ${passedTests}"
            echo "Failed tests count: ${failedTests}"
            echo "Skipped tests count: ${skippedTests}"
        } else {
            echo "No test results found - tests may not have been executed"
            testsExecuted = false
        }
    } catch (Exception e) {
        echo "Failed to parse test results: ${e.message}"
        testsExecuted = false
    }
    
    // Send email notification only if tests were executed
    if (testsExecuted) {
        sendEmailNotification(moduleName, suiteXmlFile, environment, testCategory, 
                            totalTests, passedTests, failedTests, skippedTests, testDuration)
    } else {
        echo "Skipping main email notification - tests were not executed due to build failure"
    }
}

def sendEmailNotification(moduleName, suiteXmlFile, environment, testCategory, 
                         totalTests, passedTests, failedTests, skippedTests, testDuration) {
    
    def buildStatus = currentBuild.result ?: 'SUCCESS'
    def statusIcon = buildStatus == 'SUCCESS' ? "✅" : (buildStatus == 'UNSTABLE' ? "⚠️" : "❌")
    def statusColor = buildStatus == 'SUCCESS' ? "#28a745" : (buildStatus == 'UNSTABLE' ? "#ffc107" : "#dc3545")
    def timestamp = new Date().format("yyyy-MM-dd HH:mm:ss", TimeZone.getTimeZone("IST"))
    def executionTime = testDuration > 0 ? 
        String.format("%.2f minutes", testDuration / 60000.0) : "N/A"
    
    // Check for attachments
    def extentReportExists = false
    def extentReportFile = ""
    
    try {
        extentReportFile = "${moduleName}/test-output/extent-reports/extent-report.html"
        extentReportExists = fileExists(extentReportFile)
    } catch (Exception e) {
        echo "Error checking extent report existence: ${e.message}"
    }

    def automationLogExists = fileExists('automation.log')
    def httpLogExists = fileExists('http-requests.log')
    
    // Build attachments pattern
    def attachmentsList = []
    if (extentReportExists) {
        attachmentsList.add(extentReportFile)
    }
    if (automationLogExists) {
        attachmentsList.add('automation.log')
    }
    if (httpLogExists) {
        attachmentsList.add('http-requests.log')
    }
    def attachmentsPattern = attachmentsList.join(',')
    
    echo "Email attachments: ${attachmentsPattern}"
    
    emailext (
        subject: "${statusIcon} ${buildStatus}: ${env.JOB_NAME} [${env.BUILD_NUMBER}] - ${moduleName} Module",
        mimeType: 'text/html',
        body: generateEmailBody(moduleName, suiteXmlFile, environment, testCategory, buildStatus, statusIcon, statusColor,
                              totalTests, passedTests, failedTests, skippedTests, executionTime, timestamp,
                              extentReportExists, extentReportFile, automationLogExists, httpLogExists),
        attachmentsPattern: attachmentsPattern,
        to: '<EMAIL>,<EMAIL>,<EMAIL>'
    )
}

def sendFailureNotification(moduleName, environment) {
    def failureAutomationLogExists = fileExists('automation.log')
    def failureAttachments = failureAutomationLogExists ? 'automation.log' : ''
    
    def statusIcon = "❌"
    def statusColor = "#dc3545"
    def timestamp = new Date().format("yyyy-MM-dd HH:mm:ss", TimeZone.getTimeZone("IST"))
    def executionTime = currentBuild.duration > 0 ? 
        String.format("%.2f minutes", currentBuild.duration / 60000.0) : "N/A"
    
    try {
        emailext (
            subject: "${statusIcon} BUILD FAILED: ${env.JOB_NAME} [${env.BUILD_NUMBER}] - ${moduleName} Module",
            mimeType: 'text/html',
            body: generateFailureEmailBody(moduleName, environment, statusIcon, statusColor, executionTime, timestamp, failureAutomationLogExists),
            attachmentsPattern: failureAttachments,
            to: '<EMAIL>,<EMAIL>,<EMAIL>'
        )
    } catch (Exception e) {
        echo "Failed to send failure notification email: ${e.message}"
    }
}

def generateEmailBody(moduleName, suiteXmlFile, environment, testCategory, buildStatus, statusIcon, statusColor,
                     totalTests, passedTests, failedTests, skippedTests, executionTime, timestamp,
                     extentReportExists, extentReportFile, automationLogExists, httpLogExists) {
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header { background-color: ${statusColor}; color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center; }
            .content { padding: 20px; }
            .section { margin: 20px 0; padding: 15px; background-color: #f8f9fa; border-radius: 5px; }
            .stats-grid { display: grid; grid-template-columns: repeat(4, 1fr); gap: 10px; margin: 15px 0; }
            .stat-card { background: white; padding: 15px; border-radius: 5px; text-align: center; border: 1px solid #dee2e6; }
            .stat-number { font-size: 24px; font-weight: bold; margin-bottom: 5px; }
            .stat-label { font-size: 12px; color: #6c757d; text-transform: uppercase; }
            .success { color: #28a745; }
            .failure { color: #dc3545; }
            .warning { color: #ffc107; }
            .info-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
            .info-table th, .info-table td { padding: 8px 12px; text-align: left; border-bottom: 1px solid #eee; }
            .info-table th { background-color: #f8f9fa; font-weight: bold; }
            .button { display: inline-block; padding: 10px 20px; background-color: ${statusColor}; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
            .footer { background-color: #f8f9fa; padding: 15px; border-radius: 0 0 8px 8px; text-align: center; font-size: 12px; color: #666; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>${statusIcon} ${buildStatus}</h1>
                <h2>Backend Automation [Build #${env.BUILD_NUMBER}]</h2>
                <p>Module: <strong>${moduleName}</strong> | Status: <strong>${buildStatus}</strong></p>
            </div>

            <div class="content">
                <div class="section">
                    <h3>📊 Test Statistics</h3>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number">${totalTests}</div>
                            <div class="stat-label">Total Tests</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number success">${passedTests}</div>
                            <div class="stat-label">Passed</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number failure">${failedTests}</div>
                            <div class="stat-label">Failed</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number warning">${skippedTests}</div>
                            <div class="stat-label">Skipped</div>
                        </div>
                    </div>
                </div>

                <div class="section">
                    <h3>🔧 Build Information</h3>
                    <table class="info-table">
                        <tr><th>Module</th><td>${moduleName}</td></tr>
                        <tr><th>Environment</th><td>${environment}</td></tr>
                        <tr><th>Test Category</th><td>${testCategory}</td></tr>
                        <tr><th>Suite File</th><td>${suiteXmlFile}</td></tr>
                        <tr><th>Execution Time</th><td>${executionTime}</td></tr>
                        <tr><th>Build Number</th><td>#${env.BUILD_NUMBER}</td></tr>
                        <tr><th>Timestamp</th><td>${timestamp}</td></tr>
                    </table>
                </div>

                <div class="section">
                    <h3>📈 Test Results Summary</h3>
                    <p><strong>Pass Rate:</strong> ${totalTests > 0 ? String.format("%.1f%%", (passedTests * 100.0 / totalTests)) : "N/A"}</p>
                    <p><strong>Failure Rate:</strong> ${totalTests > 0 ? String.format("%.1f%%", (failedTests * 100.0 / totalTests)) : "N/A"}</p>
                    ${failedTests > 0 ? "<p style='color: #dc3545;'><strong>⚠️ ${failedTests} test(s) failed. Please review the detailed report.</strong></p>" : ""}
                </div>

                ${extentReportExists || automationLogExists ? """
                <div class="section">
                    <h3>📋 Test Reports & Logs</h3>
                    ${extentReportExists ? """
                    <p><strong>📊 Extent Report:</strong> Detailed test execution report</p>
                    <a href="${env.BUILD_URL}artifact/${extentReportFile.replace('\\', '/')}" class="button">Download Extent Report</a>
                    """ : ""}
                    ${automationLogExists ? """
                    <p><strong>📝 Automation Logs:</strong> Complete test execution logs</p>
                    <a href="${env.BUILD_URL}artifact/automation.log" class="button">Download Automation Logs</a>
                    """ : ""}
                    ${httpLogExists ? """
                    <p><strong>🌐 HTTP Logs:</strong> API requests and responses</p>
                    <a href="${env.BUILD_URL}artifact/http-requests.log" class="button">Download HTTP Logs</a>
                    """ : ""}
                </div>
                """ : ""}

                <div class="section">
                    <h3>🔗 Quick Links</h3>
                    <a href="${env.BUILD_URL}" class="button">View Build Details</a>
                    <a href="${env.BUILD_URL}console" class="button">Console Output</a>
                </div>
            </div>

            <div class="footer">
                <p>This is an automated message from Jenkins CI/CD Pipeline</p>
                <p>Lenskart Automation Framework | Generated on ${timestamp}</p>
            </div>
        </div>
    </body>
    </html>
    """
}

def generateFailureEmailBody(moduleName, environment, statusIcon, statusColor, executionTime, timestamp, failureAutomationLogExists) {
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header { background-color: ${statusColor}; color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center; }
            .content { padding: 20px; }
            .failure-box { background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; padding: 15px; margin: 15px 0; }
            .info-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
            .info-table th, .info-table td { padding: 8px 12px; text-align: left; border-bottom: 1px solid #eee; }
            .info-table th { background-color: #f8f9fa; font-weight: bold; }
            .button { display: inline-block; padding: 10px 20px; background-color: ${statusColor}; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
            .footer { background-color: #f8f9fa; padding: 15px; border-radius: 0 0 8px 8px; text-align: center; font-size: 12px; color: #666; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>${statusIcon} Build Failed</h1>
                <h2>Backend Automation [Build #${env.BUILD_NUMBER}]</h2>
                <p>Module: <strong>${moduleName}</strong> | Status: <strong>FAILURE</strong></p>
            </div>

            <div class="content">
                <div class="failure-box">
                    <h3 style="color: #721c24;">❌ Build Failure</h3>
                    <p style="color: #721c24;">The build failed before tests could be executed. Check console output for details.</p>
                </div>

                <h3>🔧 Build Information</h3>
                <table class="info-table">
                    <tr><th>Module</th><td>${moduleName}</td></tr>
                    <tr><th>Environment</th><td>${environment}</td></tr>
                    <tr><th>Execution Time</th><td>${executionTime}</td></tr>
                    <tr><th>Build Number</th><td>#${env.BUILD_NUMBER}</td></tr>
                    <tr><th>Timestamp</th><td>${timestamp}</td></tr>
                </table>

                <h3>🔗 Quick Links</h3>
                <a href="${env.BUILD_URL}" class="button">View Build Details</a>
                <a href="${env.BUILD_URL}console" class="button">Console Output</a>
                ${failureAutomationLogExists ? """<a href="${env.BUILD_URL}artifact/automation.log" class="button">Download Build Logs</a>""" : ""}
            </div>

            <div class="footer">
                <p>This is an automated message from Jenkins CI/CD Pipeline</p>
                <p>Lenskart Automation Framework | Generated on ${timestamp}</p>
            </div>
        </div>
    </body>
    </html>
    """
}

return this

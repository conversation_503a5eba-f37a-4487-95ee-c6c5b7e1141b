import groovy.transform.Field

@Field
def testsExecuted = false
pipeline {
    agent any
    tools {
        maven 'Maven'
        jdk 'JDK-21'
    }

    triggers {
        cron('H 4 * * *')
    }

    // Define parameters for the pipeline
    parameters {
        choice(name: 'ENVIRONMENT', choices: ['preprod', 'prod'], description: 'Select the environment to run tests against')
        choice(name: 'TEST_CATEGORY', choices: ['SANITY', 'REGRESSION', 'E2E', 'ALL'], description: 'Select the test category to run')
    }

    // Define environment variables
    environment {
        // Set Maven options
        MAVEN_OPTS = '-Xmx512m -Xms256m -XX:MaxMetaspaceSize=256m -XX:+UseG1GC'
        // Set the environment property for tests
        TEST_ENV = "${params.ENVIRONMENT}"
        DEFAULT_ENVIRONMENT = 'preprod'
        DEFAULT_TEST_CATEGORY = 'ALL'
    }

    stages {

        // Stage to checkout code from Git
        stage('Checkout') {
            steps {
                // Checkout code from Git repository
                checkout scm

                script {
                    def environment = params.ENVIRONMENT ?: env.DEFAULT_ENVIRONMENT
                    def testCategory = params.TEST_CATEGORY ?: env.DEFAULT_TEST_CATEGORY
                    currentBuild.displayName = "#${env.BUILD_NUMBER} - Multi-Module NEXS - ${environment}"
                    currentBuild.description = "Modules: e2e, nexs, scm | Environment: ${environment} | Category: ${testCategory}"
                }
            }
        }

        // Stage to build the project without running tests
        stage('Build') {
            steps {
                echo "🔨 Building all required modules..."

                // Build the project without running tests
                sh "mvn clean install -DskipTests -q -s ${env.WORKSPACE}/settings.xml"

                script {
                    echo "✅ Build completed successfully for all modules"
                }
            }
        }

        // Stage to run tests from multiple modules
        stage('Test - Multi-Module Execution') {
            parallel {
                stage('E2E NEXS Tests') {
                    steps {
                        script {
                            echo "🧪 Running E2E NEXS tests from e2e module..."
                            
                            def testCommand = 'mvn test -pl e2e'
                            testCommand += " -Dsurefire.suiteXmlFiles=src/test/resources/e2e-nexs-testng.xml"
                            testCommand += " -Denvironment=${params.ENVIRONMENT}"
                            
                            if (params.TEST_CATEGORY != 'ALL') {
                                testCommand += " -DtestCategory=${params.TEST_CATEGORY}"
                            }
                            
                            testCommand += ' -Dmaven.test.failure.ignore=true -DskipTests=false'

                            testCommand += ' -s'
                            testCommand += " ${env.WORKSPACE}/settings.xml"
                            echo "E2E NEXS Test Command: ${testCommand}"
                            sh testCommand
                        }
                    }
                }
                
                stage('NEXS Add Inventory Tests') {
                    steps {
                        script {
                            echo "🧪 Running NEXS Add Inventory tests from nexs module..."
                            
                            def testCommand = 'mvn test -pl nexs'
                            testCommand += " -Dsurefire.suiteXmlFiles=src/test/resources/nexs-addInventory.xml"
                            testCommand += " -Denvironment=${params.ENVIRONMENT}"
                            
                            if (params.TEST_CATEGORY != 'ALL') {
                                testCommand += " -DtestCategory=${params.TEST_CATEGORY}"
                            }
                            
                            testCommand += ' -Dmaven.test.failure.ignore=true -DskipTests=false'
                            testCommand += ' -s'
                            testCommand += " ${env.WORKSPACE}/settings.xml"
                            echo "NEXS Add Inventory Test Command: ${testCommand}"
                            sh testCommand
                        }
                    }
                }
                
                stage('SCM Tests') {
                    steps {
                        script {
                            echo "🧪 Running SCM tests from scm module..."
                            
                            def testCommand = 'mvn test -pl scm'
                            testCommand += " -Dsurefire.suiteXmlFiles=src/test/resources/scm-testng.xml"
                            testCommand += " -Denvironment=${params.ENVIRONMENT}"
                            
                            if (params.TEST_CATEGORY != 'ALL') {
                                testCommand += " -DtestCategory=${params.TEST_CATEGORY}"
                            }
                            
                            testCommand += ' -Dmaven.test.failure.ignore=true -DskipTests=false'
                            testCommand += ' -s'
                            testCommand += " ${env.WORKSPACE}/settings.xml"
                            echo "SCM Test Command: ${testCommand}"
                            sh testCommand
                        }
                    }
                }
            }
        }
    }

    post {
        always {
            script {
                echo "📊 Processing aggregated test results from multiple modules..."
                
                // Archive test results from all modules
                def testReportsExist = sh(script: "find . -name '*.xml' -path '*/target/surefire-reports/*' -newer codepipeline/Jenkins-nexs.groovy | head -1", returnStdout: true).trim()

                if (testReportsExist) {
                    try {
                        junit '**/target/surefire-reports/*.xml'
                    } catch (Exception e) {
                        echo "Failed to parse test results: ${e.message}"
                    }
                } else {
                    echo "No recent test results found - tests may not have run due to build failure"
                }

                // Archive extent reports from all three modules
                echo "📋 Archiving extent reports from multiple modules..."
                
                // Archive extent reports from e2e module
                archiveArtifacts artifacts: 'e2e/test-output/extent-reports/extent-report.html', allowEmptyArchive: true, fingerprint: false
                
                // Archive extent reports from nexs module
                archiveArtifacts artifacts: 'nexs/test-output/extent-reports/extent-report.html', allowEmptyArchive: true, fingerprint: false
                
                // Archive extent reports from scm module
                archiveArtifacts artifacts: 'scm/test-output/extent-reports/extent-report.html', allowEmptyArchive: true, fingerprint: false

                // Archive complete test-output directories from all modules
                archiveArtifacts artifacts: 'e2e/test-output/**', allowEmptyArchive: true
                archiveArtifacts artifacts: 'nexs/test-output/**', allowEmptyArchive: true
                archiveArtifacts artifacts: 'scm/test-output/**', allowEmptyArchive: true

                // Archive centralized log files at root level
                archiveArtifacts artifacts: 'automation.log', allowEmptyArchive: true, fingerprint: false
                archiveArtifacts artifacts: 'http-requests.log', allowEmptyArchive: true, fingerprint: false

                // Collect aggregated test statistics from all modules
                def totalTests = 0
                def passedTests = 0
                def failedTests = 0
                def skippedTests = 0
                def testDuration = currentBuild.duration ?: 0

                // Parse aggregated test results from JUnit XML files
                try {
                    def testResultAction = currentBuild.rawBuild.getAction(hudson.tasks.junit.TestResultAction.class)
                    if (testResultAction != null) {
                        totalTests = testResultAction.totalCount
                        passedTests = testResultAction.totalCount - testResultAction.failCount - testResultAction.skipCount
                        failedTests = testResultAction.failCount
                        skippedTests = testResultAction.skipCount
                        testsExecuted = true
                        echo "📊 Aggregated Test Results:"
                        echo "Total tests count: ${totalTests}"
                        echo "Passed tests count: ${passedTests}"
                        echo "Failed tests count: ${failedTests}"
                        echo "Skipped tests count: ${skippedTests}"
                    } else {
                        echo "No test results found - tests may not have been executed"
                        testsExecuted = false
                    }
                } catch (Exception e) {
                    echo "Failed to parse test results: ${e.message}"
                    testsExecuted = false
                }

                // Determine build status icon and color
                def statusIcon = ""
                def statusColor = ""
                def buildStatus = currentBuild.result ?: 'SUCCESS'

                switch(buildStatus) {
                    case 'SUCCESS':
                        statusIcon = "✅"
                        statusColor = "#28a745"
                        break
                    case 'FAILURE':
                        statusIcon = "❌"
                        statusColor = "#dc3545"
                        break
                    case 'UNSTABLE':
                        statusIcon = "⚠️"
                        statusColor = "#ffc107"
                        break
                    default:
                        statusIcon = "ℹ️"
                        statusColor = "#17a2b8"
                }

                // Calculate test execution time
                def executionTime = testDuration > 0 ?
                    String.format("%.2f minutes", testDuration / 60000.0) :
                    "N/A"

                // Get current timestamp in IST
                def timestamp = new Date().format("yyyy-MM-dd HH:mm:ss", TimeZone.getTimeZone("IST"))

                // Check for extent reports from all modules
                def e2eReportExists = fileExists('e2e/test-output/extent-reports/extent-report.html')
                def nexsReportExists = fileExists('nexs/test-output/extent-reports/extent-report.html')
                def scmReportExists = fileExists('scm/test-output/extent-reports/extent-report.html')
                
                // Check for automation logs
                def automationLogExists = fileExists('automation.log')
                def httpLogExists = fileExists('http-requests.log')

                // Build attachments pattern for all extent reports
                def attachmentsList = []
                if (e2eReportExists) {
                    attachmentsList.add('e2e/test-output/extent-reports/extent-report.html')
                }
                if (nexsReportExists) {
                    attachmentsList.add('nexs/test-output/extent-reports/extent-report.html')
                }
                if (scmReportExists) {
                    attachmentsList.add('scm/test-output/extent-reports/extent-report.html')
                }
                if (automationLogExists) {
                    attachmentsList.add('automation.log')
                }
                if (httpLogExists) {
                    attachmentsList.add('http-requests.log')
                }
                def attachmentsPattern = attachmentsList.join(',')

                echo "📧 Email attachments: ${attachmentsPattern}"

                // Send aggregated email notification
                sendAggregatedEmailNotification(statusIcon, statusColor, buildStatus, totalTests, passedTests, 
                                               failedTests, skippedTests, executionTime, timestamp, 
                                               e2eReportExists, nexsReportExists, scmReportExists, 
                                               automationLogExists, httpLogExists, attachmentsPattern)
            }
        }

        success {
            echo '✅ Multi-module build and tests completed successfully!'
            script {
                echo "✅ All tests passed across e2e-nexs, nexs-addInventory, and scm modules in ${params.ENVIRONMENT} environment"
                echo "📊 Multi-module test execution completed successfully"
            }
        }

        failure {
            script {
                echo '❌ Multi-module build or tests failed!'
                sendFailureEmailNotification()
            }
        }

        unstable {
            echo '⚠️ Multi-module build is unstable (some tests failed)!'
        }
    }
}

def sendAggregatedEmailNotification(statusIcon, statusColor, buildStatus, totalTests, passedTests,
                                   failedTests, skippedTests, executionTime, timestamp,
                                   e2eReportExists, nexsReportExists, scmReportExists,
                                   automationLogExists, httpLogExists, attachmentsPattern) {

    if (testsExecuted) {
        echo "📧 Sending aggregated email notification with multi-module test results..."
        try {
            emailext (
                subject: "${statusIcon} ${buildStatus}:Pre-Prod Multi-Module NEXS Tests [${env.BUILD_NUMBER}] - ${params.ENVIRONMENT}",
                mimeType: 'text/html',
                body: generateAggregatedEmailBody(statusIcon, statusColor, buildStatus, totalTests, passedTests,
                                                failedTests, skippedTests, executionTime, timestamp,
                                                e2eReportExists, nexsReportExists, scmReportExists,
                                                automationLogExists, httpLogExists),
                attachmentsPattern: attachmentsPattern,
                to: '<EMAIL>,<EMAIL>'
            )
            echo "📧 Aggregated email sent successfully!"
        } catch (Exception e) {
            echo "📧 Failed to send aggregated email notification: ${e.message}"
            echo "📧 Email error details: ${e.toString()}"
        }
    } else {
        echo "📧 Skipping main email notification - tests were not executed due to build failure"
    }
}

def sendFailureEmailNotification() {
    if (!testsExecuted) {
        echo "📧 Sending multi-module build failure notification..."

        def failureAutomationLogExists = fileExists('automation.log')
        def failureAttachments = failureAutomationLogExists ? 'automation.log' : ''

        def statusIcon = "❌"
        def statusColor = "#dc3545"
        def timestamp = new Date().format("yyyy-MM-dd HH:mm:ss", TimeZone.getTimeZone("IST"))
        def executionTime = currentBuild.duration > 0 ?
            String.format("%.2f minutes", currentBuild.duration / 60000.0) : "N/A"

        try {
            emailext (
                subject: "${statusIcon} BUILD FAILED:Pre-Prod Multi-Module NEXS Tests [${env.BUILD_NUMBER}]",
                mimeType: 'text/html',
                body: generateFailureEmailBody(statusIcon, statusColor, executionTime, timestamp, failureAutomationLogExists),
                attachmentsPattern: failureAttachments,
                to: '<EMAIL>,<EMAIL>'
            )
            echo "📧 Failure email sent successfully!"
        } catch (Exception e) {
            echo "📧 Failed to send failure notification email: ${e.message}"
        }
    } else {
        echo "📧 Skipping build failure email - tests were executed and main email was already sent"
    }
}

def generateAggregatedEmailBody(statusIcon, statusColor, buildStatus, totalTests, passedTests,
                               failedTests, skippedTests, executionTime, timestamp,
                               e2eReportExists, nexsReportExists, scmReportExists,
                               automationLogExists, httpLogExists) {
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
            .container { max-width: 900px; margin: 0 auto; background-color: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header { background-color: ${statusColor}; color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center; }
            .content { padding: 20px; }
            .section { margin-bottom: 20px; }
            .section h3 { color: #333; border-bottom: 2px solid #eee; padding-bottom: 5px; }
            .stats-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin: 15px 0; }
            .stat-card { background-color: #f8f9fa; padding: 15px; border-radius: 5px; text-align: center; border-left: 4px solid ${statusColor}; }
            .stat-number { font-size: 24px; font-weight: bold; color: ${statusColor}; }
            .stat-label { font-size: 12px; color: #666; text-transform: uppercase; }
            .module-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px; margin: 15px 0; }
            .module-card { background-color: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #007bff; }
            .module-title { font-weight: bold; color: #007bff; margin-bottom: 10px; }
            .info-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
            .info-table th, .info-table td { padding: 8px 12px; text-align: left; border-bottom: 1px solid #eee; }
            .info-table th { background-color: #f8f9fa; font-weight: bold; }
            .button { display: inline-block; padding: 10px 20px; background-color: ${statusColor}; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
            .footer { background-color: #f8f9fa; padding: 15px; border-radius: 0 0 8px 8px; text-align: center; font-size: 12px; color: #666; }
            .success { color: #28a745; }
            .failure { color: #dc3545; }
            .warning { color: #ffc107; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>${statusIcon} Multi-Module NEXS Test Execution</h1>
                <h2>Backend Automation [Build #${env.BUILD_NUMBER}]</h2>
                <p>Status: <strong>${buildStatus}</strong> | Environment: <strong>${params.ENVIRONMENT}</strong></p>
            </div>

            <div class="content">
                <div class="section">
                    <h3>📊 Aggregated Test Statistics</h3>
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number">${totalTests}</div>
                            <div class="stat-label">Total Tests</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number success">${passedTests}</div>
                            <div class="stat-label">Passed</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number failure">${failedTests}</div>
                            <div class="stat-label">Failed</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number warning">${skippedTests}</div>
                            <div class="stat-label">Skipped</div>
                        </div>
                    </div>
                </div>

                <div class="section">
                    <h3>🏗️ Module Execution Overview</h3>
                    <div class="module-grid">
                        <div class="module-card">
                            <div class="module-title">📋 E2E NEXS Module</div>
                            <p><strong>Suite:</strong> e2e-nexs-testng.xml</p>
                            <p><strong>Module:</strong> e2e</p>
                            <p><strong>Report:</strong> ${e2eReportExists ? '✅ Generated' : '❌ Not Found'}</p>
                        </div>
                        <div class="module-card">
                            <div class="module-title">📦 NEXS Add Inventory</div>
                            <p><strong>Suite:</strong> nexs-addInventory.xml</p>
                            <p><strong>Module:</strong> nexs</p>
                            <p><strong>Report:</strong> ${nexsReportExists ? '✅ Generated' : '❌ Not Found'}</p>
                        </div>
                        <div class="module-card">
                            <div class="module-title">🚚 SCM Module</div>
                            <p><strong>Suite:</strong> scm-testng.xml</p>
                            <p><strong>Module:</strong> scm</p>
                            <p><strong>Report:</strong> ${scmReportExists ? '✅ Generated' : '❌ Not Found'}</p>
                        </div>
                    </div>
                </div>

                <div class="section">
                    <h3>🔧 Build Information</h3>
                    <table class="info-table">
                        <tr><th>Environment</th><td>${params.ENVIRONMENT}</td></tr>
                        <tr><th>Test Category</th><td>${params.TEST_CATEGORY}</td></tr>
                        <tr><th>Execution Time</th><td>${executionTime}</td></tr>
                        <tr><th>Build Number</th><td>#${env.BUILD_NUMBER}</td></tr>
                        <tr><th>Timestamp</th><td>${timestamp}</td></tr>
                        <tr><th>Node</th><td>${env.NODE_NAME ?: 'Unknown'}</td></tr>
                    </table>
                </div>

                <div class="section">
                    <h3>📈 Test Results Summary</h3>
                    <p><strong>Pass Rate:</strong> ${totalTests > 0 ? String.format("%.1f%%", (passedTests * 100.0 / totalTests)) : "N/A"}</p>
                    <p><strong>Failure Rate:</strong> ${totalTests > 0 ? String.format("%.1f%%", (failedTests * 100.0 / totalTests)) : "N/A"}</p>
                    ${failedTests > 0 ? "<p style='color: #dc3545;'><strong>⚠️ ${failedTests} test(s) failed across modules. Please review the detailed reports.</strong></p>" : ""}
                    ${skippedTests > 0 ? "<p style='color: #ffc107;'><strong>ℹ️ ${skippedTests} test(s) were skipped across modules.</strong></p>" : ""}
                </div>

                ${(e2eReportExists || nexsReportExists || scmReportExists || automationLogExists) ? """
                <div class="section">
                    <h3>📋 Test Reports & Logs</h3>
                    ${e2eReportExists ? """
                    <p><strong>📊 E2E NEXS Report:</strong> Detailed test execution report for e2e-nexs-testng.xml</p>
                    <a href="${env.BUILD_URL}artifact/e2e/test-output/extent-reports/extent-report.html" class="button">Download E2E NEXS Report</a>
                    """ : ""}
                    ${nexsReportExists ? """
                    <p><strong>📦 NEXS Add Inventory Report:</strong> Detailed test execution report for nexs-addInventory.xml</p>
                    <a href="${env.BUILD_URL}artifact/nexs/test-output/extent-reports/extent-report.html" class="button">Download NEXS Report</a>
                    """ : ""}
                    ${scmReportExists ? """
                    <p><strong>🚚 SCM Report:</strong> Detailed test execution report for scm-testng.xml</p>
                    <a href="${env.BUILD_URL}artifact/scm/test-output/extent-reports/extent-report.html" class="button">Download SCM Report</a>
                    """ : ""}
                    ${automationLogExists ? """
                    <p><strong>📝 Automation Logs:</strong> Complete test execution logs with detailed debugging information</p>
                    <a href="${env.BUILD_URL}artifact/automation.log" class="button">Download Automation Logs</a>
                    """ : ""}
                    ${httpLogExists ? """
                    <p><strong>🌐 HTTP Logs:</strong> API requests and responses for debugging</p>
                    <a href="${env.BUILD_URL}artifact/http-requests.log" class="button">Download HTTP Logs</a>
                    """ : ""}
                    <p style="margin-top: 15px;"><em>All reports and logs are attached to this email and available in build artifacts.</em></p>
                </div>
                """ : ""}

                <div class="section">
                    <h3>🔗 Quick Links</h3>
                    <a href="${env.BUILD_URL}" class="button">View Build Details</a>
                    <a href="${env.BUILD_URL}console" class="button">Console Output</a>
                    <a href="${env.BUILD_URL}testReport" class="button">Test Results</a>
                    <a href="${env.BUILD_URL}artifact/" class="button">Download Artifacts</a>
                </div>

                <div class="section">
                    <h3>📝 Additional Information</h3>
                    <ul>
                        <li><strong>Git Branch:</strong> ${env.GIT_BRANCH ?: 'Unknown'}</li>
                        <li><strong>Git Commit:</strong> ${env.GIT_COMMIT?.take(8) ?: 'Unknown'}</li>
                        <li><strong>Triggered By:</strong> ${env.BUILD_USER ?: 'System'}</li>
                        <li><strong>Jenkins URL:</strong> <a href="${env.JENKINS_URL}">${env.JENKINS_URL}</a></li>
                    </ul>
                </div>
            </div>

            <div class="footer">
                <p>This is an automated message from Jenkins CI/CD Pipeline</p>
                <p>Multi-Module NEXS Test Execution | Generated on ${timestamp}</p>
            </div>
        </div>
    </body>
    </html>
    """
}

def generateFailureEmailBody(statusIcon, statusColor, executionTime, timestamp, failureAutomationLogExists) {
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <style>
            body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .header { background-color: ${statusColor}; color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center; }
            .content { padding: 20px; }
            .failure-box { background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; padding: 15px; margin: 15px 0; }
            .info-table { width: 100%; border-collapse: collapse; margin: 10px 0; }
            .info-table th, .info-table td { padding: 8px 12px; text-align: left; border-bottom: 1px solid #eee; }
            .info-table th { background-color: #f8f9fa; font-weight: bold; }
            .button { display: inline-block; padding: 10px 20px; background-color: ${statusColor}; color: white; text-decoration: none; border-radius: 5px; margin: 5px; }
            .footer { background-color: #f8f9fa; padding: 15px; border-radius: 0 0 8px 8px; text-align: center; font-size: 12px; color: #666; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>${statusIcon} Multi-Module Build Failed</h1>
                <h2>Backend Automation [Build #${env.BUILD_NUMBER}]</h2>
                <p>Status: <strong>FAILURE</strong></p>
            </div>

            <div class="content">
                <div class="failure-box">
                    <h3 style="color: #721c24;">❌ Multi-Module Build Failure</h3>
                    <p style="color: #721c24;">The build failed before tests could be executed across the following modules:</p>
                    <ul style="color: #721c24;">
                        <li><strong>E2E Module:</strong> e2e-nexs-testng.xml</li>
                        <li><strong>NEXS Module:</strong> nexs-addInventory.xml</li>
                        <li><strong>SCM Module:</strong> scm-testng.xml</li>
                    </ul>
                    <p style="color: #721c24;">This could be due to compilation errors, missing dependencies, or configuration issues.</p>
                </div>

                <h3>🔧 Build Information</h3>
                <table class="info-table">
                    <tr><th>Environment</th><td>${params.ENVIRONMENT}</td></tr>
                    <tr><th>Test Category</th><td>${params.TEST_CATEGORY}</td></tr>
                    <tr><th>Execution Time</th><td>${executionTime}</td></tr>
                    <tr><th>Build Number</th><td>#${env.BUILD_NUMBER}</td></tr>
                    <tr><th>Timestamp</th><td>${timestamp}</td></tr>
                    <tr><th>Node</th><td>${env.NODE_NAME ?: 'Unknown'}</td></tr>
                </table>

                <h3>🔗 Quick Links</h3>
                <a href="${env.BUILD_URL}" class="button">View Build Details</a>
                <a href="${env.BUILD_URL}console" class="button">Console Output</a>
                ${failureAutomationLogExists ? """<a href="${env.BUILD_URL}artifact/automation.log" class="button">Download Build Logs</a>""" : ""}

                <h3>📝 Additional Information</h3>
                <ul>
                    <li><strong>Git Branch:</strong> ${env.GIT_BRANCH ?: 'Unknown'}</li>
                    <li><strong>Git Commit:</strong> ${env.GIT_COMMIT?.take(8) ?: 'Unknown'}</li>
                    <li><strong>Triggered By:</strong> ${env.BUILD_USER ?: 'System'}</li>
                    <li><strong>Jenkins URL:</strong> <a href="${env.JENKINS_URL}">${env.JENKINS_URL}</a></li>
                </ul>
            </div>

            <div class="footer">
                <p>This is an automated message from Jenkins CI/CD Pipeline</p>
                <p>Multi-Module NEXS Test Execution | Generated on ${timestamp}</p>
            </div>
        </div>
    </body>
    </html>
    """
}

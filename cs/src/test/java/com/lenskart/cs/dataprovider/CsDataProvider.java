package com.lenskart.cs.dataprovider;

import com.lenskart.commons.model.*;
import com.lenskart.cs.model.CsOrderContext;

import java.util.List;

import static com.lenskart.commons.model.PowerTypes.ZERO_POWER;

public class CsDataProvider {

    @org.testng.annotations.DataProvider(name = "shippingEstimate")
    public Object[][] shippingEstimateContext() {
        return new Object[][]{

                {
                        CsOrderContext.ShippingEstimate.builder().source_facility("").pincode(121001).shipping_country(Countries.IN).build()
                }
        };
    }
}

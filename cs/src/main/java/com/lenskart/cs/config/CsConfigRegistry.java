package com.lenskart.cs.config;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Central registry for Cse configurations
 */
@Slf4j
public class CsConfigRegistry {

    // Singleton instance
    private static CsConfigRegistry instance;

    // Configuration caches
    private final Map<String, String> baseUrls = new ConcurrentHashMap<>();

    // Constants
    private static final String DEFAULT_ENVIRONMENT = "preprod";
    private static final String BASE_URLS_SECTION = "baseUrls";

    // Private constructor for singleton
    private CsConfigRegistry() {
        // Initialize configurations
        loadConfigurations();
    }

    /**
     * Get singleton instance
     *
     * @return CseConfigRegistry instance
     */
    public static synchronized CsConfigRegistry getInstance() {
        if (instance == null) {
            instance = new CsConfigRegistry();
        }
        return instance;
    }

    /**
     * Load all configurations
     */
    private void loadConfigurations() {
        loadBaseUrls();
    }

    /**
     * Load base URLs for services
     */
    @SuppressWarnings("unchecked")
    private void loadBaseUrls() {
        try {
            // Load the Cse configuration
            CsConfig config = CsConfigLoader.loadConfig();

            // Get the environment configuration
            CsConfig.EnvironmentConfig envConfig = config.getEnvironment(DEFAULT_ENVIRONMENT);

            if (envConfig == null) {
                log.warn("Environment not found: {}", DEFAULT_ENVIRONMENT);
                return;
            }

            // Get the base URLs
            Map<String, String> urls = envConfig.getBaseUrls();

            if (urls == null || urls.isEmpty()) {
                log.warn("No base URLs found in environment: {}", DEFAULT_ENVIRONMENT);
                return;
            }

            // Store the base URLs
            baseUrls.putAll(urls);

            log.info("Loaded {} base URLs", baseUrls.size());
        } catch (Exception e) {
            log.error("Failed to load base URLs: {}", e.getMessage(), e);
        }
    }

    /**
     * Get base URL for a service
     *
     * @param serviceName Service name
     * @return Base URL
     */
    public String getBaseUrl(String serviceName) {
        return baseUrls.get(serviceName);
    }

    /**
     * Get all base URLs
     *
     * @return Map of base URLs
     */
    public Map<String, String> getAllBaseUrls() {
        return new HashMap<>(baseUrls);
    }

    /**
     * Refresh all configurations
     */
    public synchronized void refresh() {
        // Clear caches
        baseUrls.clear();

        // Clear the config loader cache
        CsConfigLoader.clearCache();

        // Reload configurations
        loadConfigurations();

        log.info("All configurations refreshed");
    }
}

package com.lenskart.cs.config;

import com.lenskart.commons.config.ConfigProvider;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 * Configuration provider for Juno module that wraps JunoConfigRegistry
 * to implement the standard ConfigProvider interface.
 */
@Slf4j
public class CsConfigProvider implements ConfigProvider {
    
    private final CsConfigRegistry csConfigRegistry;
    
    // Singleton instance
    private static volatile CsConfigProvider instance;
    
    /**
     * Private constructor for singleton pattern
     */
    private CsConfigProvider() {
        this.csConfigRegistry = CsConfigRegistry.getInstance();
    }
    
    /**
     * Gets the singleton instance of JunoConfigProvider
     *
     * @return The singleton instance
     */
    public static CsConfigProvider getInstance() {
        if (instance == null) {
            synchronized (CsConfigProvider.class) {
                if (instance == null) {
                    instance = new CsConfigProvider();
                }
            }
        }
        return instance;
    }
    
    @Override
    public String getBaseUrl(String serviceName) {
        try {
            return csConfigRegistry.getBaseUrl(serviceName);
        } catch (Exception e) {
            log.error("Error getting base URL for service {} from Juno registry: {}", 
                serviceName, e.getMessage());
            return null;
        }
    }
    
    @Override
    public Map<String, String> getAllBaseUrls() {
        try {
            return csConfigRegistry.getAllBaseUrls();
        } catch (Exception e) {
            log.error("Error getting all base URLs from Juno registry: {}", e.getMessage());
            return Map.of();
        }
    }
    
    @Override
    public void refresh() {
        try {
            csConfigRegistry.refresh();
            log.info("Juno configuration refreshed successfully");
        } catch (Exception e) {
            log.error("Error refreshing Juno configuration: {}", e.getMessage(), e);
        }
    }
    
    @Override
    public boolean isInitialized() {
        return csConfigRegistry != null;
    }
    
    @Override
    public String getProviderName() {
        return "JunoConfigProvider";
    }
    
    /**
     * Gets the underlying Juno registry for direct access if needed
     *
     * @return The JunoConfigRegistry instance
     */
    public CsConfigRegistry getJunoRegistry() {
        return csConfigRegistry;
    }
}

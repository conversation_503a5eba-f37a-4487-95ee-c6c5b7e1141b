package com.lenskart.cs.model;

import lombok.Builder;
import lombok.Data;

import java.util.List;

@Builder
@Data
public class ReturnRequest {

    private String entity;
    private String facility_code;
    private String return_source;
    private String initiated_by;
    private String return_method;
    private String productIdsMap;
    private Integer incrementId;
    private String storeEmail;
    private String is_courier_reassigned;
    private String newCourier;
    private String oldCourier;
    private String salesman_name;
    private String salesman_number;
    private boolean callback_required_to_salesman;
    private String store_facility_code;
    private ReturnPickupAddress pickup_address;
    private List<ReturnItemDetails> items;
}

package com.lenskart.cs.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

@Builder
@Data
public class ItemDetails {

    private String unicomPutawayCode;
    private String putawayType;
    private Integer incrementId;
    private Integer uwItemId;
    private String barcode;
    private String gatePassCode;
    private String inventoryType;
    private String unicomReversePickupId;
    private String awbNumber;
    private String unicomOrderCode;
    private String returnReason;
    private Object product;
    private String facilityCode;
    private String log;
    private boolean notReceivedYN;
    private String resolution;
    private String qcStatus;
    private Integer productClassification;
    private Boolean isAccessoryMissing;
    private Integer putawayCode;
    private String userId;
    private String referenceType;
    private Integer productId;
    private BigDecimal totalPrice;
    private Integer b2bReferenceItemId;
    private Integer isTransitionOrder;
    private String redispatchOrder;
    private Integer returnId;
    private String returnManifestCode;
    private String referencePutawayCode;
    private Boolean isPsuedoGatePass;
    private Integer isDualCompanyFlag;
    private BigDecimal itemPriceWithTax;
    private Boolean isStoreMigrated;
    private Boolean isB2BOrder;
    private Boolean isCOCOGatePass;
    private String lastPiece;
    private Boolean isLensOnly = false;
    private Integer gatepassItemId;
    private Boolean isReversePickupPresent;
    private String productDeliveryType;
    private String itemResolutionApiRes;
    private String itemResolutionApiResMsg;
    private Boolean isExistPidOnPos;
    private String sellerFacility;
    private Integer franchiseId;
    private String navChannel;
    private Boolean isFOFOGatePass;
    private String localFittingFacility;
    private String extraItem;
    private String posGatepass;
    private String gatepassItemCode;
    private String gatepassFacility;
    private Boolean fofoOtcFlag = false;
    private Boolean rtoItem;
    private String extraItemSaveChanges;
    private String randomNo;
    private Boolean isBulkOrder;
    private Integer localFittingIncrementId;
    private Map<Integer, Integer> localFittingFacilityMap;
    @JsonIgnore
    private String grnPutawayCode;
    private Boolean gatePassDualFlag;
    private String originalBarcode;
    private Boolean gatePassItemDualFlag;
    private Boolean isExchangeFlow;
    private Boolean isInsuranceClaim;
    private String purpose;
    private String fittingFacilityCode;
    private Boolean isPOGRN;
    private Integer quantity;
    private String l1Feedback;
    private String l2Feedback;
    private String channel;
    private Integer rating;
    private Boolean detractorFlag;
    private String reasonType;
    private String poGrnVendorCode;
    private String poGrnCurrencyCode;
    private BigDecimal poGrnPriceWithTax;
    private Boolean isHighReturnPid;
    private Boolean isHearingAidProduct;
    private Boolean isSmartGlassesProduct;
    private Boolean isSwapsProduct;
    private Integer customerUwItemId;
    private Date itemAddedInGatepassDate;
    private boolean isMarkedRTOItem;
    private String dealsKartOrderId;
}

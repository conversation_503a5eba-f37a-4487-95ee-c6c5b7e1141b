package com.lenskart.cs.model;

import lombok.Data;

import java.util.Map;
import java.util.Set;

@Data
public class RTOReceivingMetadata {

    private String shippingPackageId;
    private String facilityCode;
    private boolean isVirtual;
    private Integer b2bUwItemId;
    private boolean isB2B;
    private String unicomOrderCode;
    private String returnManifestCode;
    private String runId;
    private Object irfData;
    private Integer returnId;
    private Integer virtualReturnId;
    private Integer putawayCode;
    private String b2bFacilityCode;
    private Map<String, String> apiLogs;
    private boolean doRefund;
    private boolean dualCompany;
    private Integer itemId;
    private String b2bUnicomOrderCode;
    private String b2bShippingPackageId;
    private boolean refundInitiated;
    private String userId;
    private String userEmail;
    private String referencePutawayCode;
    private Set<Integer> productClassicifactions;
    private Object gmInvoiceStatusResponse;
    private boolean isGmShipment;
    private int retryCount;
    private boolean ifInRetry;
    private boolean storeOnNexS;
    private String b2bReceivingFacility;
    private String navChannel;
    private boolean isBulkShipment;
    private String bulkOrderStoreFacility;
    private boolean postVirtualReturnNexs;
    private boolean inSchedulerRetry;
}

package com.lenskart.cs.model;

import lombok.Builder;
import lombok.Data;

import java.util.List;

@Builder
@Data
public class ReturnItemDetails {

    private Integer magento_item_id;
    private Integer uw_item_id;
    private String qc_status;
    private boolean need_approval;
    private boolean do_refund;
    private String refund_method;
    private boolean claim_insurance;
    private String refund_method_request;
    private List<ReturnItemReasonDetails> returnItemReasonDetails;

}

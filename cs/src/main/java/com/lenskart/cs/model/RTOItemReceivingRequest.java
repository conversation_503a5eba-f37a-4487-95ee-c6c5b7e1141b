package com.lenskart.cs.model;

import lombok.Builder;
import lombok.Data;

import java.util.List;

@Builder
@Data
public class RTOItemReceivingRequest {

    private String userId;
    private String userEmail;
    private String facility;
    private List<ItemDetails> itemDetails;
    private String identifierType;
    private String identifierId;
    private String ShipmentId;
    private String returnType;
    private String country;
    private boolean isGMShipment;
    private boolean ifInRetry;
    private int retryCount;
    private String runId;
    private String b2bReceivingFacility;
    private boolean isStoreOnNexS;
    private boolean inSchedulerRetry;
    private boolean postVirtualProcess;
    private String shippingId;
}

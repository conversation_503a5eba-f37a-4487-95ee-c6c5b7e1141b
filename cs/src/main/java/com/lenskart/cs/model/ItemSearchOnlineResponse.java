package com.lenskart.cs.model;

import com.lenskart.core.model.ItemDetails;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@NoArgsConstructor
@AllArgsConstructor
@Data
public class ItemSearchOnlineResponse {
    private List<com.lenskart.core.model.ItemDetails> itemDetailsList;
    private int totalCount;
    private String gatepassCreatedAt;
    private String storeAddress;
    private String country;
    private Object extraItemsPresentInThisGatepassIds;
    private Object receivedAsExtraItemInOtherGatepassIds;
    private Object extraItemOffset;
    private boolean nexsRtoAtUnicom;

    @Builder
    @Data
    public static class Product {
        private String splOrderFlag;
        private String fulfillable;
        private boolean isMultiple;
        private String colorCode;
        private int subProductId;
    }
}
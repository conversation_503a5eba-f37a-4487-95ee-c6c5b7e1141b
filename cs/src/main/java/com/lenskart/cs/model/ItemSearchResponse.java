package com.lenskart.cs.model;

import lombok.Builder;
import lombok.Data;

import java.sql.Date;
import java.util.List;
import java.util.Map;


@Builder
@Data
public class ItemSearchResponse {

    List<ItemDetails> itemDetailsList;
    Integer totalCount;
    Date gatepassCreatedAt;
    String storeAddress;
    String country;
    Map<Integer, String> extraItemsPresentInThisGatepassIds;
    Map<Integer, String> receivedAsExtraItemInOtherGatepassIds;
    Integer extraItemOffset;
    boolean isNexsRtoAtUnicom;
}

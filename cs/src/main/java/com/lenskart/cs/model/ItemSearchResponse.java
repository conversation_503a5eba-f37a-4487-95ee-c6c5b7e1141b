package com.lenskart.cs.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Builder;
import lombok.Data;

import java.sql.Date;
import java.util.List;
import java.util.Map;


@Builder
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ItemSearchResponse {

    List<ItemDetails> itemDetailsList;
    Integer totalCount;
    Date gatepassCreatedAt;
    String storeAddress;
    String country;
    Map<Integer, String> extraItemsPresentInThisGatepassIds;
    Map<Integer, String> receivedAsExtraItemInOtherGatepassIds;
    Integer extraItemOffset;
    boolean isNexsRtoAtUnicom;
}

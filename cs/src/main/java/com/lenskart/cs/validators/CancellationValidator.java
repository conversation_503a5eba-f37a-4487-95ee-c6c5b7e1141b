package com.lenskart.cs.validators;

import com.lenskart.commons.base.IValidator;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.model.ReturnResponse;
import com.lenskart.scm.database.ScmDbUtils;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.testng.Assert;

import java.util.List;
import java.util.Map;


@Builder
@Slf4j
public class CancellationValidator implements IValidator {
    CsOrderContext csOrderContext;
    ReturnResponse returnResponse;
    OrderContext orderContext;
    @Override
    public void validateNode() {
        // Add validation logic for PickingCompletionHelper
    }

    @Override
    public void validateDBEntities() {
        List<Map<String, Object>> orderStatus= ScmDbUtils.getOrderStatus(String.valueOf(orderContext.getOrderId()),csOrderContext.getProductIDToBeCancelled());
        for (Map<String, Object> map : orderStatus) {
            Assert.assertEquals(map.get("shipment_status"),csOrderContext.getCancelledOrderShipmentStatus());
        }
    }
}

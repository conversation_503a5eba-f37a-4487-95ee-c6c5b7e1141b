package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.cs.model.CsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

@SuperBuilder
public class CsCancellationHelper extends CsBaseHelper implements ServiceHelper {


    OrderContext orderContext;
    JSONObject payload;
    Response response;
    CsOrderContext csOrderContext;

    @Override
    public ServiceHelper init() {
        return this;
    }

    @Override
    public ServiceHelper process() {

        CancellationHelper cancellationHelper = CancellationHelper.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build();

        cancellationHelper.test();
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return null;
    }


    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import groovy.util.logging.Slf4j;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

import static com.lenskart.cs.endpoints.CsEndpoints.AUTH_LOGIN;

@SuperBuilder
@Slf4j
public class AuthenticationHelper extends CsBaseHelper implements ServiceHelper {

    JSONObject payload;
    OrderContext orderContext;
    Response response;
    OrderContext.Headers orderContextHeader;

    @Override
    public ServiceHelper init() {
        headers = getHeaders(orderContext);

        return this;
    }

    @Override
    public ServiceHelper process() {

        /* Fetch session Token */
        response = RestUtils.get(AUTH_LOGIN.getUrl(), headers, null, 200);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

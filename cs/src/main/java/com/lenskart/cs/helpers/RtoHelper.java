package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.model.ItemSearchResponse;
import com.lenskart.cs.model.RTOItemReceivingRequest;
import com.lenskart.cs.model.RTOReceivingMetadata;
import com.lenskart.cs.requestbuilders.CsRequestBuilders;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

import static com.lenskart.cs.endpoints.CsEndpoints.RTO_RECEIVING;

@SuperBuilder
public class RtoHelper extends CsBaseHelper implements ServiceHelper {


    JSONObject payload;
    OrderContext orderContext;
    Response response;
    RTOItemReceivingRequest request;
    ItemSearchHelper itemSearchHelper;
    RTOReceivingMetadata rtoReceivingMetadata;
    ItemSearchResponse itemSearchResponse;

    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext);
        queryParams = getQueryParams(orderContext);
        request = CsRequestBuilders.getRTOItemReceivingRequest(orderContext, itemSearchHelper.getItemSearchResponse());
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(RTO_RECEIVING.getUrl(), headers, JsonUtils.convertObjectToJsonString(payload), 200);
        rtoReceivingMetadata = parseResponse(response.asPrettyString(), RTOReceivingMetadata.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

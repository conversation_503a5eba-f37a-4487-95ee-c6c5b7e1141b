package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;

import com.lenskart.cs.model.CancellationRequest;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.requestbuilders.CsRequestBuilders;
import com.lenskart.cs.validators.CancellationValidator;
import com.lenskart.cs.validators.ReturnValidator;
import groovy.util.logging.Slf4j;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;

import java.util.Map;

import static com.lenskart.cs.endpoints.CsEndpoints.CANCEL_INVOICE;


@SuperBuilder
@Slf4j
public class CancellationHelper extends CsBaseHelper implements ServiceHelper {


    String payload;
    OrderContext orderContext;
    Response response;
    CancellationRequest cancellationRequest;
    CsOrderContext csOrderContext;

    @Override
    public ServiceHelper init() {
        payload = JsonUtils.convertObjectToJsonString(CsRequestBuilders.getCancellationRequest(csOrderContext));
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(CANCEL_INVOICE.getUrl(Map.of("orderID",String.valueOf(orderContext.getOrderId()))), null, payload,202);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        CancellationValidator validator = CancellationValidator.builder().csOrderContext(csOrderContext).orderContext(orderContext).build();
        validator.validateNode();
        validator.validateDBEntities();
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

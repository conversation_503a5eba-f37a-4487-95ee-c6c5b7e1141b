package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.cs.model.CsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

@SuperBuilder
public class CsRTOHelper extends CsBaseHelper implements ExecutionHelper {


    OrderContext orderContext;
    JSONObject payload;
    Response response;
    CsOrderContext csOrderContext;


    @Override
    public ExecutionHelper init() {
        return this;
    }

    @Override
    public ExecutionHelper orchestrateFlow() {

        AuthenticationHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        ItemSearchHelper itemSearchHelper =ItemSearchHelper.builder()
                .orderContext(orderContext)
                .build();

        itemSearchHelper.test();

        RtoHelper.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .itemSearchHelper(itemSearchHelper)
                .build()
                .test();

        return this;
    }

    @Override
    public ExecutionHelper test() {
        init();
        orchestrateFlow();
        return this;
    }
}

package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.model.OpenPutawaysResponse;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import java.util.Map;
import static com.lenskart.cs.endpoints.CsEndpoints.OPEN_PUTAWAYS;

@Slf4j
@SuperBuilder
@Getter
public class OpenPutawaysHelper extends CsBaseHelper implements ServiceHelper {

    private String userId;
    private OrderContext orderContext;
    private CsOrderContext csOrderContext;
    private Response response;
    private OpenPutawaysResponse openPutawaysResponse;
    private Map<String, String> customHeaders;

    @Override
    public ServiceHelper init() {
        statusCode=200;
        headers = getHeadersWithSessionToken(orderContext,csOrderContext);
        queryParams = getOpenPutaway(csOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(OPEN_PUTAWAYS.getUrl(), headers, queryParams, statusCode);
        openPutawaysResponse = response.as(OpenPutawaysResponse.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
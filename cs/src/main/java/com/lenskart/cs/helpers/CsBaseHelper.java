package com.lenskart.cs.helpers;

import com.lenskart.commons.base.BaseHelper;
import com.lenskart.commons.constants.HeaderMapper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.model.PowerTypes;
import com.lenskart.cs.exceptions.CseExceptionStates;
import com.lenskart.cs.requestbuilders.CsRequestBuilders;
import lombok.experimental.SuperBuilder;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@SuperBuilder
public class CsBaseHelper extends BaseHelper<CseExceptionStates, Object> {


    public Map<String, String> getHeaders(OrderContext orderContext) {
        headers = new HashMap<>();
        headers.put(HeaderMapper.X_FACILITY.getHeaderName(), "LKH03");
        headers.put(HeaderMapper.X_USERNAME.getHeaderName(), "<EMAIL>");
        headers.put(HeaderMapper.X_PASSWORD.getHeaderName(), "NDQ3Mjk=");
        return headers;
    }


    public Map<String, String> getHeadersWithSessionToken(OrderContext orderContext) {
        headers = new HashMap<>();
        headers.put(HeaderMapper.X_FACILITY.getHeaderName(), CsRequestBuilders.getProduct(orderContext,"148248").getFacilityCode());
        headers.put(HeaderMapper.X_CS_COOKIE.getHeaderName(), orderContext.getHeaders().getCsCookie());
        return headers;
    }


    public Map<String, Object> getQueryParams(OrderContext orderContext) {
        queryParams = new HashMap<>();
        List<OrderContext.ProductList> productLists = orderContext.getProductLists();
        queryParams.put("shippingPackageId", productLists.get(0).getShippingPackageId());
        queryParams.put("trackingNumber", "as");
        queryParams.put("userName","<EMAIL>");
        return queryParams;
    }
}

package com.lenskart.cs.helpers;

import com.lenskart.commons.base.BaseHelper;
import com.lenskart.commons.constants.HeaderMapper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.cs.exceptions.CseExceptionStates;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.requestbuilders.CsRequestBuilders;
import com.lenskart.cs.util.ChecksumUtil;
import lombok.experimental.SuperBuilder;
import java.util.HashMap;
import java.util.Map;


@SuperBuilder
public class CsBaseHelper extends BaseHelper<CseExceptionStates, Object> {


    public Map<String, String> getHeaders(OrderContext orderContext,CsOrderContext csOrderContext) {
        headers = new HashMap<>();
        headers.put(HeaderMapper.X_FACILITY.getHeaderName(), CsRequestBuilders.getProduct(orderContext,csOrderContext.getProductIDToBeReturned()).getFacilityCode());
        headers.put(HeaderMapper.X_USERNAME.getHeaderName(), "120401");
        headers.put(HeaderMapper.X_PASSWORD.getHeaderName(), "VWRpdEBQYXNzMg==");
        return headers;
    }

    // store the secret in config - remove this hardcoding
    public Map<String, String> getHeaders(String payload) {
        headers = new HashMap<>();
        headers.put(HeaderMapper.X_FACILITY.getHeaderName(), ChecksumUtil.createChecksum("123", payload));
        return headers;
    }


    public Map<String, String> getHeadersWithSessionToken(OrderContext orderContext, CsOrderContext csOrderContext) {
        headers = new HashMap<>();
        headers.put(HeaderMapper.X_FACILITY.getHeaderName(), CsRequestBuilders.getProduct(orderContext,csOrderContext.getProductIDToBeReturned()).getFacilityCode());
        headers.put(HeaderMapper.X_CS_COOKIE.getHeaderName(), orderContext.getHeaders().getCsCookie());
        return headers;
    }

    public Map<String, String> getTrackingValidatorHeaders(OrderContext orderContext, CsOrderContext csOrderContext, CsOrderContext.ReceivingGatePass csOrderContextReceivingGatePass) {
        headers = new HashMap<>();
        headers.putAll(getHeadersWithSessionToken(orderContext,csOrderContext));
        headers.put(HeaderMapper.X_USERNAME.getHeaderName(), csOrderContextReceivingGatePass.getUserName());
        return headers;
    }


    public Map<String, Object> getQueryParams(OrderContext orderContext,CsOrderContext csOrderContext) {
        queryParams = new HashMap<>();
        queryParams.put("shippingPackageId", CsRequestBuilders.getProduct(orderContext,csOrderContext.getProductIDToBeReturned()).getShippingPackageId());
        queryParams.put("trackingNumber", "as");
        queryParams.put("userName","<EMAIL>");
        return queryParams;
    }
    public Map<String, Object> getQueryParamsEstimateAPI(OrderContext orderContext,CsOrderContext.ShippingEstimate csOrderContextShipping) {
        queryParams = new HashMap<>();
        queryParams.put("pincode", csOrderContextShipping.getPincode());
        queryParams.put("classification", csOrderContextShipping.getClassification());
        queryParams.put("frame_type",csOrderContextShipping.getFrame_type());
        queryParams.put("power_type", csOrderContextShipping.getPowerTypes());
        queryParams.put("in_days", csOrderContextShipping.getIn_days());
        queryParams.put("dispach_date_req",csOrderContextShipping.getDispach_date_req());
        queryParams.put("packages", csOrderContextShipping.getPackages());
        queryParams.put("brand", csOrderContextShipping.getBrand());
        queryParams.put("lens_type",csOrderContextShipping.getLens_type());
        queryParams.put("platform",csOrderContextShipping.getPlatform());
        queryParams.put("r_sph", csOrderContextShipping.getR_sph());
        queryParams.put("l_sph", csOrderContextShipping.getL_sph());
        queryParams.put("r_cyl",csOrderContextShipping.getR_cyl());
        queryParams.put("l_cyl",csOrderContextShipping.getL_cyl());
        queryParams.put("r_axi", csOrderContextShipping.getR_axi());
        queryParams.put("l_axi", csOrderContextShipping.getL_axi());
        queryParams.put("last_piece",csOrderContextShipping.getLast_piece());
        queryParams.put("lk_country", csOrderContextShipping.getLk_country());
        queryParams.put("shipping_country",csOrderContextShipping.getShipping_country());
        queryParams.put("cart_item_id",csOrderContextShipping.getCart_item_id());
        queryParams.put("localFittingRequired", csOrderContextShipping.getLocalFittingRequired());
        queryParams.put("source_facility", csOrderContextShipping.getSource_facility());
        queryParams.put("ship_to_store",csOrderContextShipping.getShip_to_store());
        queryParams.put("lens_only",csOrderContextShipping.getLens_only());
        queryParams.put("source_country",csOrderContextShipping.getSource_country());
        queryParams.put("coating_id", csOrderContextShipping.getCoating_id());
        queryParams.put("store_facility", csOrderContextShipping.getStore_facility());
        queryParams.put("true_last_piece",csOrderContextShipping.getTrue_last_piece());
        queryParams.put("use_new", csOrderContextShipping.getUse_new());
        queryParams.put("screen_name", csOrderContextShipping.getScreen_name());
        return queryParams;
    }

    /**
     * Create query parameters for offline product search
     * @param csOrderContext CS order context containing product details
     * @return Map of query parameters
     */
    public Map<String, Object> getOfflineProductSearchQueryParams(CsOrderContext csOrderContext) {
        queryParams=new HashMap<>();
        queryParams.put("gatepassNo", csOrderContext.getGatepassNo());
        return queryParams;
    }
    public Map<String, Object> getPassClosureSearchQueryParams(CsOrderContext csOrderContext) {
        queryParams=new HashMap<>();
        queryParams.put("gatepassId", csOrderContext.getGatepassNo());
        return queryParams;
    }
    public Map<String, Object> receivingReasonQueryParams(CsOrderContext csOrderContext) {
        queryParams=new HashMap<>();
        queryParams.put("uwItemId", csOrderContext.getUwItemId());
        queryParams.put("gatepassItemId", csOrderContext.getGatepassItemId());
        queryParams.put("barcode", csOrderContext.getBarcode());
        queryParams.put("source", csOrderContext.getSource());
        return queryParams;
    }

    public Map<String, Object> getOpenPutaway(CsOrderContext csOrderContext) {
        queryParams=new HashMap<>();
        queryParams.put("userId", csOrderContext.getUserId());
        return queryParams;
    }
}

package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.model.ItemSearchOnlineResponse;

import com.lenskart.cs.requestbuilders.CsRequestBuilders;

import io.restassured.response.Response;
import lombok.Builder;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;


import java.util.HashMap;
import java.util.Map;

import static com.lenskart.cs.endpoints.CsEndpoints.ITEM_SEARCH_BY_ORDER_AND_FACILITY;

@SuperBuilder
@Builder
public class ItemSearchByOrderFacilityHelper extends CsBaseHelper implements ServiceHelper {

    OrderContext orderContext;
    CsOrderContext csOrderContext;
    Response response;
    ItemSearchOnlineResponse itemSearchOnlineResponse;
    String productID;

    @Override
    public ServiceHelper init() {
        statusCode = 200;
        headers = getHeadersWithSessionToken(orderContext,csOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {

        
        response = RestUtils.get(
            ITEM_SEARCH_BY_ORDER_AND_FACILITY.getUrl(Map.of(
                            "orderId", String.valueOf(orderContext.getOrderId()),
                            "facilityCode", CsRequestBuilders.getProduct(orderContext,productID).getFacilityCode())),
            headers, 
            null, // No query params needed for this API
            statusCode
        );
        itemSearchOnlineResponse = response.as(ItemSearchOnlineResponse.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}
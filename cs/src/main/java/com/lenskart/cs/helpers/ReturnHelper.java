package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.model.*;
import com.lenskart.cs.requestbuilders.CsRequestBuilders;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;


import static com.lenskart.cs.endpoints.CsEndpoints.RETURN;

@SuperBuilder
public class ReturnHelper extends CsBaseHelper implements ServiceHelper {


    JSONObject payload;
    OrderContext orderContext;
    Response response;
    ReturnRequest request;
    CsOrderContext csOrderContext;
    ReturnResponse returnResponse;
    ReturnIReversePickInfoRequest returnIReversePickInfo;
    ReversePickupInfoHelper reversePickupInfoHelper;

    @Override
    public ServiceHelper init() {
        statusCode=200;
        request = CsRequestBuilders.getReturnRequestDetails(orderContext,csOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.post(RETURN.getUrl(), null, JsonUtils.convertObjectToJsonString(request), 200);
        returnResponse = response.as(ReturnResponse.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

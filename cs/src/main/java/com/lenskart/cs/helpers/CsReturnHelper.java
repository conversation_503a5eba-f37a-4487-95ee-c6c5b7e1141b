package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.commons.model.OrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

@SuperBuilder
public class CsReturnHelper extends CsBaseHelper implements ExecutionHelper {


    OrderContext orderContext;
    JSONObject payload;
    Response response;


    @Override
    public ExecutionHelper init() {
        return this;
    }

    @Override
    public ExecutionHelper orchestrateFlow() {

        AuthenticationHelper.builder()
                .orderContext(orderContext)
                .build()
                .test();

        ItemSearchHelper itemSearchHelper =ItemSearchHelper.builder()
                .orderContext(orderContext)
                .build();

        itemSearchHelper.test();

        RtoHelper.builder()
                .orderContext(orderContext)
                .itemSearchHelper(itemSearchHelper)
                .build()
                .test();

        return this;
    }

    @Override
    public ExecutionHelper test() {
        init();
        orchestrateFlow();
        return this;
    }
}

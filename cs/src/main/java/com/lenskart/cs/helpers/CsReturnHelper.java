package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ExecutionHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.cs.model.CsOrderContext;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

@SuperBuilder
public class CsReturnHelper extends CsBaseHelper implements ExecutionHelper {


    OrderContext orderContext;
    JSONObject payload;
    Response response;
    CsOrderContext csOrderContext;

    @Override
    public ExecutionHelper init() {
        return this;
    }

    @Override
    public ExecutionHelper orchestrateFlow() {

        ReturnHelper returnHelper = ReturnHelper.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .build();

        returnHelper.test();

        ReversePickupInfoHelper.builder()
                .returnHelper(returnHelper)
                .orderContext(orderContext)
                .build().test();

        ReturnItemHelper.builder()
                .orderContext(orderContext)
                .csOrderContext(csOrderContext)
                .returnHelper(returnHelper)
                .build().test();


        return this;
    }

    @Override
    public ExecutionHelper test() {
        init();
        orchestrateFlow();
        return this;
    }
}

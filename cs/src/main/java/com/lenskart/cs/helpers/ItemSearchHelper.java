package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.model.CsOrderContext;
import com.lenskart.cs.model.ItemDetails;
import com.lenskart.cs.model.ItemSearchResponse;
import com.lenskart.cs.model.RTOItemReceivingRequest;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import static com.lenskart.cs.endpoints.CsEndpoints.ITEM_SEARCH_RTO;

@Slf4j
@SuperBuilder
@Getter
public class ItemSearchHelper extends CsBaseHelper implements ServiceHelper {

    JSONObject payload;
    OrderContext orderContext;
    Response response;
    ItemSearchResponse itemSearchResponse;
    ItemDetails itemDetails;
    CsOrderContext csOrderContext;
    RTOItemReceivingRequest rtoItemReceivingRequest;


    @Override
    public ServiceHelper init() {
        statusCode=200;
        headers = getHeadersWithSessionToken(orderContext,csOrderContext);
        queryParams = getQueryParams(orderContext,csOrderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(ITEM_SEARCH_RTO.getUrl(), headers, queryParams, statusCode);
        itemSearchResponse = response.as(ItemSearchResponse.class);
        log.info(JsonUtils.convertObjectToJsonString(itemSearchResponse));
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

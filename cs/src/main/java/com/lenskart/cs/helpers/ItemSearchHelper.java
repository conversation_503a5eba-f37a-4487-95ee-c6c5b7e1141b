package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import groovy.util.logging.Slf4j;
import io.restassured.response.Response;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

@SuperBuilder
@Slf4j
public class ItemSearchHelper extends CsBaseHelper implements ServiceHelper {

    JSONObject payload;
    OrderContext orderContext;
    Response response;
    OrderContext.Headers orderContextHeader;


    @Override
    public ServiceHelper init() {
        headers = getHeaders(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

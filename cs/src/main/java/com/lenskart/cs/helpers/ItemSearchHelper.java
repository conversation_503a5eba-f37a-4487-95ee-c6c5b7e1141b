package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.JsonUtils;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.model.ItemDetails;
import com.lenskart.cs.model.ItemSearchResponse;
import com.lenskart.cs.model.RTOItemReceivingRequest;
import com.lenskart.juno.schema.product.ProductResult;
import groovy.util.logging.Slf4j;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

import java.util.List;

import static com.lenskart.commons.constants.Constants.RESULT;
import static com.lenskart.cs.constants.Constant.CS_COOKIE_STR;
import static com.lenskart.cs.endpoints.CsEndpoints.AUTH_LOGIN;
import static com.lenskart.cs.endpoints.CsEndpoints.ITEM_SEARCH_RTO;

@lombok.extern.slf4j.Slf4j
@SuperBuilder
@Slf4j
@Getter
public class ItemSearchHelper extends CsBaseHelper implements ServiceHelper {

    JSONObject payload;
    OrderContext orderContext;
    Response response;
    ItemSearchResponse itemSearchResponse;
    ItemDetails itemDetails;
    RTOItemReceivingRequest rtoItemReceivingRequest;


    @Override
    public ServiceHelper init() {
        statusCode=200;
        headers = getHeadersWithSessionToken(orderContext);
        queryParams = getQueryParams(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {
        response = RestUtils.get(ITEM_SEARCH_RTO.getUrl(), headers, queryParams, statusCode);
        itemSearchResponse = parseResponse(RestUtils.getValueFromResponse(response, "itemDetailsList"), ItemSearchResponse.class);
        log.info(JsonUtils.convertObjectToJsonString(itemSearchResponse));
        rtoItemReceivingRequest.setItemDetails(itemSearchResponse.getItemDetailsList());
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

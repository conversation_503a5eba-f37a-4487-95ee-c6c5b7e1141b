package com.lenskart.cs.helpers;

import com.lenskart.commons.base.ServiceHelper;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.commons.utils.RestUtils;
import com.lenskart.cs.model.ItemSearchResponse;
import com.lenskart.juno.schema.product.ProductResult;
import groovy.util.logging.Slf4j;
import io.restassured.response.Response;
import lombok.Getter;
import lombok.experimental.SuperBuilder;
import org.json.JSONObject;

import static com.lenskart.commons.constants.Constants.RESULT;
import static com.lenskart.cs.constants.Constant.CS_COOKIE_STR;
import static com.lenskart.cs.endpoints.CsEndpoints.AUTH_LOGIN;
import static com.lenskart.cs.endpoints.CsEndpoints.ITEM_SEARCH_RTO;

@SuperBuilder
@Slf4j
@Getter
public class ItemSearchHelper extends CsBaseHelper implements ServiceHelper {

    JSONObject payload;
    OrderContext orderContext;
    Response response;
    ItemSearchResponse itemSearchResponse;


    @Override
    public ServiceHelper init() {
        headers = getHeadersWithSessionToken(orderContext);
        queryParams = getQueryParams(orderContext);
        return this;
    }

    @Override
    public ServiceHelper process() {

        response = RestUtils.get(ITEM_SEARCH_RTO.getUrl(), headers, queryParams, 200);
        itemSearchResponse = parseResponse(response.asPrettyString(), ItemSearchResponse.class);
        return this;
    }

    @Override
    public ServiceHelper validate() {
        return this;
    }

    @Override
    public ServiceHelper test() {
        init();
        process();
        validate();
        return this;
    }
}

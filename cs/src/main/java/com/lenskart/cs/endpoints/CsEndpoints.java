package com.lenskart.cs.endpoints;

import com.lenskart.commons.endpoints.BaseEndpoint;
import lombok.Getter;

import java.util.Map;

@Getter
public enum CsEndpoints implements BaseEndpoint {

    AUTH_LOGIN("/auth/login", "reverseService"),
    ITEM_SEARCH_RTO("/item-search/rto", "reverseService"),
    RTO_RECEIVING("/receiving/rto-receiving", "reverseService"),
    RETURN("/return", "orderOpsService"),
    REVERSE_PICKUP_INFO_UPDATE("/return/reverse-pickup-info/update", "orderOpsService"),
    INITIATE_REFUND("/v1.0/initiate-exchange-order-refund", "refundService"),
    CANCEL_INVOICE("/{$orderID}/cancel-invoice", "orderOpsService"),
    RETURN_ITEMS("/reverse/return-items", "orderOpsService");



    private final String endpoint;
    private final String serviceName;

    CsEndpoints(String endpoint, String serviceName) {
        this.endpoint = endpoint;
        this.serviceName = serviceName;
    }

    @Override
    public String getUrl() {
        return CsEndpointManager.getEndpointUrl(this);
    }

    @Override
    public String getUrl(Map<String, String> pathParams) {
        return CsEndpointManager.getEndpointUrl(this, pathParams);
    }

}

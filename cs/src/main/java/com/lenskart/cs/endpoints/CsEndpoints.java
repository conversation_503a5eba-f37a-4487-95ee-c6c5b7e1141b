package com.lenskart.cs.endpoints;

import com.lenskart.commons.endpoints.BaseEndpoint;
import lombok.Getter;

import java.util.Map;

@Getter
public enum CsEndpoints implements BaseEndpoint {

    AUTH_LOGIN("/auth/login", "reverseService"),
    ITEM_SEARCH_RTO("/item-search/rto", "reverseService"),
    RTO_RECEIVING("/receiving/rto-receiving", "reverseService"),
    RETURN("/return", "orderOpsService"),
    REVERSE_PICKUP_INFO_UPDATE("/return/reverse-pickup-info/update", "orderOpsService"),
    INITIATE_REFUND("/v1.0/initiate-exchange-order-refund", "refundService"),
    RETURN_ITEMS("/reverse/return-items", "orderOpsService"),
    ITEM_SEARCH_OFFLINE_PRODUCT("/item-search/offline-product/{$productId}", "reverseService"),
    CANCEL_INVOICE("/{$orderID}/cancel-invoice", "orderOpsService"),
    SHIPPING_ESTIMATE("/estimate", "shippingService"),
    ITEM_SEARCH_BY_ORDER_AND_FACILITY("/item-search/{$orderId}/{$facilityCode}", "reverseService"),
    PINCODE_ELIGIBILITY("/v1/pincode/eligibility","shippingService"),
    DELIVERY_ETA("/v1/delivery-eta", "shippingService"),
    LOCAL_FITTING_GATE_PASS_RECEIVING("/receiving/gatepass/local-fitting", "reverseService"),
    ITEM_RESOLUTION_RECEIVING("/receiving/item-resolution", "reverseService"),
    FETCH_RECEIVING_REASON("/reason/receiving/fetch", "reverseService"),
    GATE_PASS_CLOSURE("/gatePassClosure/checkState", "reverseService"),
    GATE_PASS_CLOSUREOFFLINE("/gatePassClosure/offline/", "reverseService"),
    TRACKING_NUMBER_VALIDATOR("/trackingNumber/validate/{$id}/{$trackingNumber}/{$userId}/{$returnType}/{$facilityCode}", "reverseService"),
    REASON_RECEIVING_SAVE("/reason/receiving/save", "reverseService"),
    OPEN_PUTAWAYS("/putaway/receiving/open-putaways/{$userId}", "reverseService"),
    METHOD_WISE_REFUND_DETAILS("get-method-wise-refund-details", "refundService"),
    RTO_PUTAWAY("/putaway/rto-receiving/open-putaways/{$userId}", "reverseService"),
    RETURN_DETAILS_V2("/return/details/v2.0/get", "returnService"),
    RECEIVING_QC_ACTION("/qc/action", "reverseService"),
    CREATE_RETURN_DIRECT_RECEIVED("/return/v1.0/create-return/direct-received", "returnService"),
    RETURN_REFUND_AWAITED_RTO("/return/v1.0/awaited-rto/return-refund", "returnService"),
    UPDATE_RETURN_REFUND_METHOD("/return/v1.0/update/order-status-refund", "returnService");

    private final String endpoint;
    private final String serviceName;

    CsEndpoints(String endpoint, String serviceName) {
        this.endpoint = endpoint;
        this.serviceName = serviceName;
    }

    @Override
    public String getUrl() {
        return CsEndpointManager.getEndpointUrl(this);
    }

    @Override
    public String getUrl(Map<String, String> pathParams) {
        return CsEndpointManager.getEndpointUrl(this, pathParams);
    }

}

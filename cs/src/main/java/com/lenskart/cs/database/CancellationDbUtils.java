package com.lenskart.cs.database;


import com.lenskart.commons.database.mysql.MySQLQueryExecutor;
import com.lenskart.commons.model.Cluster;
import com.lenskart.commons.model.OrderContext;
import com.lenskart.cs.constants.Constant;
import com.lenskart.cs.model.CsOrderContext;

import java.util.List;
import java.util.Map;

public class CancellationDbUtils {

    public static List<Map<String, Object>> getOrderStatus(OrderContext orderContext, CsOrderContext csOrderContext) {
        List<Map<String, Object>> orderStatus = MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), Constant.INVENTORY_DB,
                        ReturnQueries.RETURN_DETAIL,
                        orderContext.getOrderId(),csOrderContext.getProductIDToBeCancelled()) ;
            return orderStatus;
    }
}

package com.lenskart.cs.database;


import com.lenskart.commons.database.mysql.MySQLQueryExecutor;
import com.lenskart.commons.model.Cluster;
import com.lenskart.cs.constants.Constant;
import com.lenskart.cs.model.ReturnResponse;


import java.util.Map;

public class ReturnDbUtils {

    public static Map<String, Object> getReturnDetails(ReturnResponse returnResponse) {
        Map<String, Object> returnDetails = MySQLQueryExecutor
                .executeQuery(Cluster.SCM_CLUSTER.getClusterName(), Constant.RETURN_DB,
                        ReturnQueries.RETURN_DETAIL,
                        returnResponse).getFirst();
            return returnDetails;
    }
}

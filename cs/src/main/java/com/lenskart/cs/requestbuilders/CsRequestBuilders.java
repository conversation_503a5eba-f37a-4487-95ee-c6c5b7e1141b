package com.lenskart.cs.requestbuilders;

import com.lenskart.commons.model.OrderContext;
import com.lenskart.cs.model.ItemSearchResponse;
import com.lenskart.cs.model.RTOItemReceivingRequest;

import java.util.List;

public class CsRequestBuilders {

    public static RTOItemReceivingRequest getRTOItemReceivingRequest(OrderContext orderContext, ItemSearchResponse itemSearchResponse) {
        // TODO - add all the fields
        return RTOItemReceivingRequest.builder()
                .b2bReceivingFacility(getProduct(orderContext,"148248").getFacilityCode())
                .identifierId("shippingId")
                .identifierType(getProduct(orderContext,"148248").getShippingPackageId())
                .itemDetails(itemSearchResponse.getItemDetailsList())
                .returnType("shippingId")
                .shippingId(getProduct(orderContext,"148248").getShippingPackageId())
                .userId("17722")
                .build();
    }

    private static OrderContext.ProductList getProduct(OrderContext orderContext, String productID){
        List<OrderContext.ProductList> productList=orderContext.getProductLists();
        for (OrderContext.ProductList product : productList) {
            if (productID.equals(product.getProductId())) {
                return product;
            }
        }
        return null;
    }

    public static RTOItemReceivingRequest getCancellationRequest(OrderContext orderContext, ItemSearchResponse itemSearchResponse) {
        // TODO - add all the fields
        return RTOItemReceivingRequest.builder()
                .b2bReceivingFacility(getProduct(orderContext,"148248").getFacilityCode())
                .identifierId("shippingId")
                .identifierType(getProduct(orderContext,"148248").getShippingPackageId())
                .itemDetails(itemSearchResponse.getItemDetailsList())
                .returnType("shippingId")
                .shippingId(getProduct(orderContext,"148248").getShippingPackageId())
                .userId("17722")
                .build();
    }
}

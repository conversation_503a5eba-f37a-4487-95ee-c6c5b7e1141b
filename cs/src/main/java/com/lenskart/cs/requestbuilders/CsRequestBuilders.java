package com.lenskart.cs.requestbuilders;

import com.lenskart.commons.model.OrderContext;
import com.lenskart.cs.model.*;
import com.lenskart.cs.model.ItemSearchResponse;
import com.lenskart.cs.model.RTOItemReceivingRequest;
import com.lenskart.cs.model.ReturnOrderRequestDTO;
import org.json.JSONObject;
import java.util.Collections;
import java.util.List;

public class CsRequestBuilders {

    public static RTOItemReceivingRequest getRTOItemReceivingRequest(OrderContext orderContext, ItemSearchResponse itemSearchResponse) {
        // TODO - add all the fields
        return RTOItemReceivingRequest.builder()
                .b2bReceivingFacility(getProduct(orderContext,"148248").getFacilityCode())
                .identifierId("shippingId")
                .identifierType(getProduct(orderContext,"148248").getShippingPackageId())
                .itemDetails(itemSearchResponse.getItemDetailsList())
                .returnType("shippingId")
                .shippingId(getProduct(orderContext,"148248").getShippingPackageId())
                .userId("17722")
                .build();
    }

    public static OrderContext.ProductList getProduct(OrderContext orderContext, String productID){
        List<OrderContext.ProductList> productList=orderContext.getProductLists();
        for (OrderContext.ProductList product : productList) {
            if (productID.equals(product.getProductId())) {
                return product;
            }
        }
        return null;
    }

    private static ReturnPickupAddress getReturnPickUpAddress(){

        return ReturnPickupAddress.builder()
                .city("NEW DELHI")
                .email("<EMAIL>")
                .state("DELHI")
                .country("India")
                .pincode(110059)
                .first_name("MEENU")
                .last_name("MEENU")
                .street_1("A1/157,2ND FLOOR,FRONT SIDE")
                .street_2("UTTAM NAGAR,NEW DELHI").build();
    }


    private static List<ReturnItemReasonDetails> getReturnReasonDetailsList() {
        ReturnItemReasonDetails reasonDetails = ReturnItemReasonDetails.builder()
                .primaryReasonId(2003)
                .type("RETURN")
                .additionalComments("Return")
                .secondaryReasonId(211)
                .build();
        return Collections.singletonList(reasonDetails);
    }

    private static List<ReturnItemDetails> getReturnItemDetails(OrderContext orderContext){
        ReturnItemDetails returnItemDetails = ReturnItemDetails.builder()
                .returnItemReasonDetails(getReturnReasonDetailsList())
                .magento_item_id(getProduct(orderContext,"").getItemId())
                .uw_item_id(null)
                .claim_insurance(false)
                .do_refund(true)
                .need_approval(false)
                .qc_status("Pass")
                .refund_method("storecredit")
                .build();
        return Collections.singletonList(returnItemDetails);
    }

    public static ReturnRequest getReturnRequestDetails(OrderContext orderContext){
        return ReturnRequest.builder()
                .returnItemDetails(getReturnItemDetails(orderContext))
                .returnPickupAddress(getReturnPickUpAddress())
                .storeEmail(null)
                .is_courier_reassigned(null)
                .store_facility_code(null)
                .salesman_number("**********")
                .salesman_name("Ankit Singh")
                .return_method("storecredit")
                .return_source("vsm")
                .incrementId(null   )
                .newCourier(null)
                .oldCourier(null)
                .initiated_by(null)
                .facility_code(null)
                .exchange_address(null)
                .build();

    }

    public static RTOItemReceivingRequest getCancellationRequest(OrderContext orderContext, ItemSearchResponse itemSearchResponse) {
        // TODO - add all the fields
        return RTOItemReceivingRequest.builder()
                .b2bReceivingFacility(getProduct(orderContext,"148248").getFacilityCode())
                .identifierId("shippingId")
                .identifierType(getProduct(orderContext,"148248").getShippingPackageId())
                .itemDetails(itemSearchResponse.getItemDetailsList())
                .returnType("shippingId")
                .shippingId(getProduct(orderContext,"148248").getShippingPackageId())
                .userId("17722")
                .build();
    }


    public static ReturnIReversePickInfoRequest getReturnReversePickInfoRequest(ReturnResponse returnResponse) {
        return ReturnIReversePickInfoRequest.builder()
                .awb(returnResponse.getReversePickupDetails().getAwbNumber())
                .courier(returnResponse.getReversePickupDetails().getCourier())
                .pickupId(returnResponse.getResult().getGroupId())
                .referenceId(null)
                .userId(0)
                .userName("Ankit")
                .build();
    }

    public static String createPayloadForRefund(int orderId) {
        JSONObject payload = new JSONObject();
        payload.put("exchangeOrderId", 0);
        payload.put("masterOrderId", orderId);
        return payload.toString();
    }


    public static ReturnOrderRequestDTO createPayloadForReturn(OrderContext orderContext) {

        ReturnOrderRequestDTO returnOrderRequestDTO = ReturnOrderRequestDTO.builder()
                .incrementId(orderContext.getOrderId())
                .source("web")
                .referenceOrderCode("123") // what should be this value?
                .doRefund(true)
                .isDualCo(false)
                .facility(orderContext.getProductLists().get(0).getProductId()) // check this and fix at item level
                .raiseRPUatUnicom(true)
                .raiseRPUatNexs(false)
                .rtoItem(false)
                .awaitedRtoItem(false)
                .groupId(987654321l)
                .reasonDetail("Customer Request")
                .refundMethod("Credit Card")
                .shippingPackageId(getProduct(orderContext,"148248").getShippingPackageId())
                .uwOrderDTOs(List.of())
                .orderDTOs(List.of())
                .ordersHeaderDTO(new Object())
                .userId("123") // check value for this
                .build();
        return returnOrderRequestDTO;
    }
}

# CS Module

## Overview

The **CS (Customer Service) Module** provides comprehensive automation testing capabilities for Lenskart's customer service operations, including RTO (Return to Origin) management, customer support workflows, order assistance, and customer communication systems.

## 🏗️ **Architecture**

### **Core Components**

- **RTO Management**: Return to Origin order processing and workflows
- **Customer Support**: Customer service ticket and case management
- **Order Assistance**: Customer order support and resolution
- **Communication Systems**: Customer communication and notification management
- **Refund Processing**: Return and refund workflow automation
- **Customer Data Management**: Customer information and history management

## 📁 **Package Structure**

```
com.lenskart.cs/
├── config/              # CS-specific configuration management
├── endpoints/           # CS API endpoints and endpoint manager
├── exceptions/          # CS-specific exception states
├── helpers/             # Service helper classes for CS operations
│   └── rto/             # RTO-specific helper classes
├── model/               # CS-specific data models
└── requestbuilders/     # Request builders for CS API calls
```

## 🔧 **Key Features**

### **1. RTO (Return to Origin) Management**

#### **RTO Order Processing**
```java
CsRTOHelper rtoHelper = new CsRTOHelper();

// Create RTO request
RTORequest rtoRequest = RTORequest.builder()
    .orderId("ORD123456")
    .reason("CUSTOMER_RETURN")
    .returnType("FULL_RETURN")
    .customerComments("Product not as expected")
    .build();

String rtoId = rtoHelper.createRTORequest(rtoRequest);

// Get RTO status
RTOStatus status = rtoHelper.getRTOStatus(rtoId);

// Process RTO approval
boolean approved = rtoHelper.approveRTO(rtoId, "CS_AGENT_001");

// Complete RTO process
boolean completed = rtoHelper.completeRTO(rtoId, returnDetails);
```

#### **RTO Workflow Management**
```java
// Get pending RTO requests
List<RTORequest> pendingRTOs = rtoHelper.getPendingRTORequests();

// Get RTO history for customer
List<RTORequest> customerRTOs = rtoHelper.getCustomerRTOHistory(customerId);

// Validate RTO eligibility
boolean eligible = rtoHelper.isEligibleForRTO("ORD123456");

// Calculate RTO refund amount
double refundAmount = rtoHelper.calculateRTORefund("ORD123456", returnItems);
```

### **2. Customer Support Operations**

#### **Support Ticket Management**
```java
CustomerSupportHelper supportHelper = new CustomerSupportHelper();

// Create support ticket
SupportTicket ticket = SupportTicket.builder()
    .customerId("CUST123456")
    .orderId("ORD123456")
    .category("ORDER_ISSUE")
    .priority("HIGH")
    .description("Order not delivered on time")
    .build();

String ticketId = supportHelper.createSupportTicket(ticket);

// Update ticket status
supportHelper.updateTicketStatus(ticketId, TicketStatus.IN_PROGRESS);

// Add ticket comment
supportHelper.addTicketComment(ticketId, "Investigating delivery delay", "CS_AGENT_001");

// Resolve ticket
supportHelper.resolveTicket(ticketId, "Issue resolved - order delivered", "CS_AGENT_001");
```

### **3. Order Assistance**

#### **Order Support Operations**
```java
OrderAssistanceHelper orderHelper = new OrderAssistanceHelper();

// Get order issues
List<OrderIssue> issues = orderHelper.getOrderIssues("ORD123456");

// Create order modification request
ModificationRequest modRequest = ModificationRequest.builder()
    .orderId("ORD123456")
    .modificationType("ADDRESS_CHANGE")
    .newAddress(updatedAddress)
    .reason("Customer requested address change")
    .build();

boolean modified = orderHelper.modifyOrder(modRequest);

// Cancel order on behalf of customer
boolean cancelled = orderHelper.cancelOrderForCustomer("ORD123456", "CUSTOMER_REQUEST");

// Expedite order delivery
boolean expedited = orderHelper.expediteOrder("ORD123456", "CUSTOMER_COMPLAINT");
```

### **4. Customer Communication**

#### **Communication Management**
```java
CustomerCommunicationHelper commHelper = new CustomerCommunicationHelper();

// Send order update notification
NotificationRequest notification = NotificationRequest.builder()
    .customerId("CUST123456")
    .orderId("ORD123456")
    .type("ORDER_UPDATE")
    .message("Your order has been dispatched")
    .channel("EMAIL")
    .build();

boolean sent = commHelper.sendNotification(notification);

// Get communication history
List<Communication> history = commHelper.getCommunicationHistory("CUST123456");

// Send promotional communication
boolean promoSent = commHelper.sendPromotionalMessage(customerId, promoDetails);
```

### **5. Refund Processing**

#### **Refund Operations**
```java
RefundProcessingHelper refundHelper = new RefundProcessingHelper();

// Initiate refund
RefundRequest refundRequest = RefundRequest.builder()
    .orderId("ORD123456")
    .refundAmount(2999.00)
    .refundReason("PRODUCT_RETURN")
    .refundMethod("ORIGINAL_PAYMENT")
    .build();

String refundId = refundHelper.initiateRefund(refundRequest);

// Get refund status
RefundStatus refundStatus = refundHelper.getRefundStatus(refundId);

// Process refund approval
boolean approved = refundHelper.approveRefund(refundId, "CS_MANAGER_001");

// Complete refund
boolean completed = refundHelper.completeRefund(refundId);
```

## 🌐 **API Endpoints**

### **CS Endpoints**
```java
public enum CsEndpoints implements BaseEndpoint {
    // RTO Management
    CREATE_RTO("/api/v1/rto/create", "csService", "POST", "Create RTO request"),
    GET_RTO_STATUS("/api/v1/rto/{rtoId}/status", "csService", "GET", "Get RTO status"),
    APPROVE_RTO("/api/v1/rto/{rtoId}/approve", "csService", "POST", "Approve RTO request"),
    
    // Support Tickets
    CREATE_TICKET("/api/v1/support/ticket/create", "csService", "POST", "Create support ticket"),
    GET_TICKET("/api/v1/support/ticket/{ticketId}", "csService", "GET", "Get ticket details"),
    UPDATE_TICKET("/api/v1/support/ticket/{ticketId}/update", "csService", "PUT", "Update ticket"),
    
    // Order Assistance
    GET_ORDER_ISSUES("/api/v1/orders/{orderId}/issues", "csService", "GET", "Get order issues"),
    MODIFY_ORDER("/api/v1/orders/{orderId}/modify", "csService", "POST", "Modify order"),
    CANCEL_ORDER("/api/v1/orders/{orderId}/cancel", "csService", "POST", "Cancel order"),
    
    // Customer Communication
    SEND_NOTIFICATION("/api/v1/communication/notify", "csService", "POST", "Send notification"),
    GET_COMM_HISTORY("/api/v1/communication/{customerId}/history", "csService", "GET", 
                    "Get communication history"),
    
    // Refund Processing
    INITIATE_REFUND("/api/v1/refund/initiate", "csService", "POST", "Initiate refund"),
    GET_REFUND_STATUS("/api/v1/refund/{refundId}/status", "csService", "GET", "Get refund status"),
    APPROVE_REFUND("/api/v1/refund/{refundId}/approve", "csService", "POST", "Approve refund");
}
```

## 🔧 **Configuration**

### **cs.yml Configuration**
```yaml
cs:
  baseUrls:
    csService: https://cs.preprod.lenskart.com
    notificationService: https://notifications.preprod.lenskart.com
    
  authentication:
    csUser: "cs_automation"
    csPassword: "CsPass123!"
    sessionTimeout: 3600
    
  rto:
    enableRTO: true
    rtoEligibilityDays: 30
    rtoReasons:
      - "CUSTOMER_RETURN"
      - "PRODUCT_DEFECT"
      - "WRONG_PRODUCT"
      - "SIZE_ISSUE"
      - "QUALITY_ISSUE"
    rtoTypes:
      - "FULL_RETURN"
      - "PARTIAL_RETURN"
      - "EXCHANGE"
    autoApprovalThreshold: 5000
    
  support:
    ticketCategories:
      - "ORDER_ISSUE"
      - "PRODUCT_INQUIRY"
      - "DELIVERY_ISSUE"
      - "PAYMENT_ISSUE"
      - "TECHNICAL_SUPPORT"
    ticketPriorities:
      - "LOW"
      - "MEDIUM"
      - "HIGH"
      - "URGENT"
    slaHours:
      LOW: 72
      MEDIUM: 48
      HIGH: 24
      URGENT: 4
      
  refund:
    enableRefund: true
    refundMethods:
      - "ORIGINAL_PAYMENT"
      - "BANK_TRANSFER"
      - "WALLET_CREDIT"
    refundProcessingDays: 7
    autoRefundThreshold: 2000
    
  communication:
    channels:
      - "EMAIL"
      - "SMS"
      - "PUSH_NOTIFICATION"
      - "WHATSAPP"
    enablePromoNotifications: true
    
  timeouts:
    apiTimeout: 30000
    rtoProcessingTimeout: 300000
    refundProcessingTimeout: 600000
```

## 🧪 **Testing**

### **Test Categories**

#### **Sanity Tests**
```java
@Test
@TestCategory(TestCategory.Category.SANITY)
public void testRTOCreation() {
    CsRTOHelper rtoHelper = new CsRTOHelper();
    
    RTORequest request = RTORequest.builder()
        .orderId("ORD123456")
        .reason("CUSTOMER_RETURN")
        .returnType("FULL_RETURN")
        .build();
        
    String rtoId = rtoHelper.createRTORequest(request);
    assert rtoId != null && !rtoId.isEmpty();
}

@Test
@TestCategory(TestCategory.Category.SANITY)
public void testSupportTicketCreation() {
    CustomerSupportHelper supportHelper = new CustomerSupportHelper();
    
    SupportTicket ticket = SupportTicket.builder()
        .customerId("CUST123456")
        .category("ORDER_ISSUE")
        .priority("MEDIUM")
        .description("Test ticket")
        .build();
        
    String ticketId = supportHelper.createSupportTicket(ticket);
    assert ticketId != null && !ticketId.isEmpty();
}
```

#### **Regression Tests**
```java
@Test
@TestCategory(TestCategory.Category.REGRESSION)
public void testCompleteRTOWorkflow() {
    CsRTOHelper rtoHelper = new CsRTOHelper();
    
    // Create RTO request
    RTORequest request = RTORequest.builder()
        .orderId("ORD123456")
        .reason("PRODUCT_DEFECT")
        .returnType("FULL_RETURN")
        .build();
        
    String rtoId = rtoHelper.createRTORequest(request);
    
    // Approve RTO
    boolean approved = rtoHelper.approveRTO(rtoId, "CS_AGENT_001");
    assert approved;
    
    // Complete RTO
    boolean completed = rtoHelper.completeRTO(rtoId, returnDetails);
    assert completed;
    
    // Verify RTO status
    RTOStatus status = rtoHelper.getRTOStatus(rtoId);
    assert status == RTOStatus.COMPLETED;
}
```

### **Running CS Tests**
```bash
# Run all CS tests
mvn test -pl cs

# Run specific test categories
mvn test -pl cs -DtestCategory=SANITY

# Run with specific environment
mvn test -pl cs -Denvironment=preprod

# Run specific test class
mvn test -pl cs -Dtest=CsRTOHelperTest
```

## 📊 **Data Models**

### **CS-Specific Models**
```java
// RTO Request Model
@Data
@Builder
public class RTORequest {
    private String orderId;
    private String customerId;
    private String reason;
    private String returnType;
    private String customerComments;
    private List<ReturnItem> returnItems;
    private LocalDateTime requestedAt;
    private RTOStatus status;
}

// Support Ticket Model
@Data
@Builder
public class SupportTicket {
    private String ticketId;
    private String customerId;
    private String orderId;
    private String category;
    private String priority;
    private String description;
    private TicketStatus status;
    private String assignedAgent;
    private LocalDateTime createdAt;
    private LocalDateTime resolvedAt;
}

// Refund Request Model
@Data
@Builder
public class RefundRequest {
    private String orderId;
    private String customerId;
    private double refundAmount;
    private String refundReason;
    private String refundMethod;
    private RefundStatus status;
    private LocalDateTime initiatedAt;
}
```

## 🛠️ **Request Builders**

### **CS Request Builder**
```java
CsRequestBuilder builder = new CsRequestBuilder();

// Build RTO request
Map<String, Object> rtoRequest = builder
    .withOrderId("ORD123456")
    .withReason("CUSTOMER_RETURN")
    .withReturnType("FULL_RETURN")
    .withCustomerComments("Product not as expected")
    .buildRTORequest();

// Build support ticket request
Map<String, Object> ticketRequest = builder
    .withCustomerId("CUST123456")
    .withCategory("ORDER_ISSUE")
    .withPriority("HIGH")
    .withDescription("Order delivery delayed")
    .buildSupportTicketRequest();
```

## 🔍 **Exception Handling**

### **CS Exception States**
```java
public enum CsExceptionStates {
    RTO_NOT_ELIGIBLE("CS_001", "Order not eligible for RTO"),
    RTO_CREATION_FAILED("CS_002", "Failed to create RTO request"),
    TICKET_CREATION_FAILED("CS_003", "Failed to create support ticket"),
    REFUND_NOT_ALLOWED("CS_004", "Refund not allowed for this order"),
    COMMUNICATION_FAILED("CS_005", "Failed to send customer communication"),
    AGENT_NOT_AVAILABLE("CS_006", "Customer service agent not available");
}
```

## 📈 **Performance Features**

- **Workflow Automation**: Automated RTO and refund processing workflows
- **SLA Management**: Automatic SLA tracking and escalation
- **Batch Processing**: Support for batch operations on multiple tickets/RTOs
- **Real-time Notifications**: Instant customer communication
- **Analytics Integration**: Customer service performance metrics

## 🔗 **Integration Points**

### **With Other Modules**
- **Commons**: Uses shared utilities and database connections
- **Juno**: Accesses order data for customer service operations
- **NEXS**: Integrates for order modification and cancellation
- **Cosmos**: Gets order status for customer inquiries

### **External Services**
- **Payment Gateway**: Refund processing integration
- **Notification Service**: Customer communication channels
- **CRM System**: Customer data and history integration
- **Analytics Service**: Customer service performance tracking

## 🚀 **Getting Started**

### **1. Add CS Dependency**
```xml
<dependency>
    <groupId>com.lenskart</groupId>
    <artifactId>cs</artifactId>
    <version>1.0.0</version>
</dependency>
```

### **2. Configure CS Service**
Update `cs.yml` with your customer service configuration.

### **3. Create Your First Test**
```java
public class MyCsTest {
    @Test
    @TestCategory(TestCategory.Category.SANITY)
    public void testRTOEligibility() {
        CsRTOHelper helper = new CsRTOHelper();
        boolean eligible = helper.isEligibleForRTO("ORD123456");
        // Assert based on order characteristics
    }
}
```

## 📚 **Examples**

Check the test classes for comprehensive examples:
- **RTO Management**: Complete RTO workflow from creation to completion
- **Support Tickets**: Ticket creation, management, and resolution
- **Order Assistance**: Order modification and cancellation workflows
- **Customer Communication**: Notification and communication management
- **Refund Processing**: End-to-end refund workflow automation

The CS module provides comprehensive automation capabilities for testing Lenskart's customer service operations, ensuring efficient customer support, RTO processing, and customer communication workflows.

@Library('jenkins/shared-functions.groovy') _

// SCM Module Jenkins Pipeline
// This pipeline is specifically configured for the scm module

def sharedFunctions

node {
    // Load shared functions
    sharedFunctions = load 'jenkins/shared-functions.groovy'
}

// Module-specific configuration
def moduleConfig = [
    moduleName: 'scm',
    suiteXmlFile: 'src/test/resources/scm-regression.xml',
    defaultEnvironment: 'preprod',
    defaultTestCategory: 'REGRESSION'
]

// Execute the pipeline using shared functions
sharedFunctions.executeModulePipeline(moduleConfig)
